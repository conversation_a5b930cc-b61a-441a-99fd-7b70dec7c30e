from object_registry import locate_instance
from reseller.reporting.nav_purchase_report.purchase_report_service import (
    PurchaseInvoiceDataPushService,
)

from data import res
import superjson


def clean_and_parse_with_superjson(json_str):
    res_fixed = json_str.replace('\r\n', ' ').replace('\\', ' ')
    return superjson.json.loads(res_fixed)

python_data = clean_and_parse_with_superjson(res)
print("Number of entries", len(python_data))


purchase_invoice_data_push_service: PurchaseInvoiceDataPushService = locate_instance(PurchaseInvoiceDataPushService)
#
#
purchase_invoice_data_push_service.push_hotel_invoices_for_date(
    hotel_invoice_reports=python_data
)