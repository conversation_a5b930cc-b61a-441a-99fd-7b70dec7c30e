{"variables": {"aws_access_key": "", "aws_secret_key": "", "aws_region": "ap-south-1", "vpc_id": "vpc-1f820776", "subnet_id": "subnet-a57bfccc", "distro": "1604"}, "builders": [{"type": "amazon-ebs", "tags": {"BuildUUID": "{{user `build_uuid`}}", "pod": "b2b", "env": "production"}, "run_tags": {"pod": "b2b", "env": "production", "Name": "b2b-p-reseller-deployment"}, "vpc_id": "{{user `vpc_id`}}", "subnet_id": "{{user `subnet_id`}}", "access_key": "{{user `aws_access_key`}}", "secret_key": "{{user `aws_secret_key`}}", "region": "{{user `aws_region`}}", "source_ami_filter": {"filters": {"virtualization-type": "hvm", "name": "treebo_base-*", "root-device-type": "ebs"}, "most_recent": true}, "instance_type": "t2.medium", "ssh_username": "ubuntu", "ami_name": "b2b-p-reseller-{{timestamp}}-{{user `distro`}}", "associate_public_ip_address": "false", "spot_price": "auto", "spot_price_auto_product": "Linux/UNIX (Amazon VPC)"}], "provisioners": [{"type": "shell", "inline": ["sudo mkdir -p /opt/reseller/docker/compose/", "sudo chmod -R 755 /opt/reseller && sudo chown -R ubuntu:ubuntu /opt/reseller"]}, {"type": "file", "source": "en<PERSON><PERSON>", "destination": "/opt/reseller/"}, {"type": "file", "source": "code_repo/setup.sh", "destination": "/opt/reseller/"}, {"type": "file", "source": "code_repo/docker/compose/reporting-worker-compose.yml", "destination": "/opt/reseller/docker/compose/"}, {"type": "shell", "environment_vars": ["BUILD_NO={{user `BUILD_NO`}}", "config_branch={{user `config_branch`}}"], "inline": ["sudo chmod -R 755 /opt/reseller", "sudo BUILD_DIR=/opt /opt/reseller/setup.sh production $BUILD_NO reseller $config_branch reporting"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true}]}