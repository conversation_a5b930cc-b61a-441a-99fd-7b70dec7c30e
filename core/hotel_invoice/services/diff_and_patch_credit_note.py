from integrations.einvoice.cleartax.einvoice_dto import IrpDetailsDTO


def diff_hotel_credit_note(old_credit_note, new_credit_note):
    # gstin = has_gstin_been_updated(old_buy_invoice.seller_gst_details, new_buy_invoice.seller_gst_details)
    gstin = False  # stubbing for cs events to decide if gstin has changed
    status = has_credit_note_status_updated(
        old_credit_note.status, new_credit_note.status
    )
    reference_invoice_number = has_credit_note_reference_number_been_updated(
        old_credit_note.reference_invoice_numbers,
        new_credit_note.reference_invoice_numbers,
    )
    is_einvoice = new_credit_note.is_einvoice and not old_credit_note.is_einvoice
    items_to_check = [
        gstin,
        status,
        reference_invoice_number,
        is_einvoice,
    ]
    if not any(items_to_check):
        return False
    return True


def has_credit_note_status_updated(old_credit_note_status, new_credit_note_status):
    return old_credit_note_status != new_credit_note_status


def has_credit_note_reference_number_been_updated(
    old_credit_note_reference_number,  # pylint: disable=invalid-name
    new_credit_note_reference_number,
):
    if old_credit_note_reference_number:
        return (
            old_credit_note_reference_number.sort()
            != new_credit_note_reference_number.sort()
        )
    return old_credit_note_reference_number != new_credit_note_reference_number


def patch_hotel_credit_note(
    old_credit_note, new_credit_note
):  # pylint: disable=invalid-name
    old_credit_note.status = new_credit_note.status

    old_credit_note.line_items = new_credit_note.line_items
    # old_buy_invoice.seller_gst_details = new_buy_invoice.seller_gst_details
    if not old_credit_note.order_id:
        old_credit_note.order_id = new_credit_note.order_id
    old_credit_note.reference_invoice_numbers = (
        new_credit_note.reference_invoice_numbers
    )
    if not old_credit_note.is_einvoice and new_credit_note.is_einvoice:
        old_credit_note.set_irp_details(
            IrpDetailsDTO(
                irn=new_credit_note.irn,
                qr_code=new_credit_note.qr_code,
                ack_no=new_credit_note.ack_no,
                acked_on=new_credit_note.acked_on,
                signed_invoice=new_credit_note.signed_invoice,
            )
        )
    return old_credit_note
