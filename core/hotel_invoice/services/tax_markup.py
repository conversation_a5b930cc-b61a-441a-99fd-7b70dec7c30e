from core.common.constants import HsnCodes
from core.common.data_classes.charge import Charge
from core.common.data_classes.tax_breakup import TaxBreakup


class TaxMarkup:
    def __init__(self, charge: Charge):
        """
        :param charge: Charge that is to be marked up
        """
        self.charge = charge

    @classmethod
    def is_markup_required(cls, charge):
        return charge.hsn_code in [HsnCodes.Food, HsnCodes.Food2] or (
            not charge.hsn_code and "food" in charge.smart_description.lower()
        )

    def maybe_markup(self) -> Charge:
        """May or may not markup based on preconditions"""
        if not self.is_markup_required(self.charge):
            return self.charge
        return self.markup()

    def markup(self) -> Charge:
        """
        The whole logic depends on HotelInvoice(Pretax + Tax) = CustomerInvoice(Pretax)
        and HotelInvoice(TaxBreakup percentages) is same as CustomerInvoice(TaxBreakup percentages)
        Our aim is to deduce HotelInvoice(Pretax)

        Mathematically:

        Equation1: Bₚₜ + Bₜ = Sₚₜ
        Equation2: Bₜ =  sum of [Bₚₜ * item.percentage for item in Sₜ.tax_breakup]

        Substituting Eq2 with Eq1:
        Bₚₜ + [Bₚₜ * item.percentage for item in Sₜ.tax_breakup] = Sₚₜ

        Bₚₜ (1 + percent1/100 + percent2/100 ) = Sₚₜ
        # where percent1, percent 2 are percentages of tax breakup in sell invoice

        Bₚₜ = Sₚₜ/ (1 + percent1/100 + percent2/100 )
        """
        tax_breakups = self.charge.tax_breakup
        tax_breakup_percentages = [item.percent for item in tax_breakups]
        dividing_factor = sum([100] + tax_breakup_percentages) / 100

        new_pre_tax = self.charge.pre_tax / dividing_factor

        new_tax_breakups = [
            TaxBreakup(
                code=item.code,
                amount=(new_pre_tax * item.percent / 100),
                percent=item.percent,
            )
            for item in tax_breakups
        ]

        new_tax = sum([item.amount for item in new_tax_breakups])

        new_charge = Charge(
            uid=self.charge.uid,
            name=self.charge.name,
            quantity=self.charge.quantity,
            applicable_date=self.charge.applicable_date,
            pre_tax=new_pre_tax,
            tax=new_tax,
            tax_breakup=new_tax_breakups,
            hsn_code=self.charge.hsn_code,
            smart_description=self.charge.smart_description,
            tax_code=self.charge.tax_code,
        )
        return new_charge
