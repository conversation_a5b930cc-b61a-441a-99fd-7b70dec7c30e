from core.common.constants import HsnCodes


class SmartChargeDescription:
    description_hsn_map = {
        HsnCodes.RoomRent: "Stay: Room Rent",
        HsnCodes.Banquet: "Stay: Banquet",
        HsnCodes.Food: "Charge: Food / Beverages",
        HsnCodes.Food2: "Charge: Food / Beverages",
        HsnCodes.Laundry: "Charge: Laundry",
        HsnCodes.Taxi: "Charge: Taxi",
        HsnCodes.AlcoholicBeverage: "Charge: Alcoholic beverages",
    }

    unidentified_charge_prefix = "Misc"

    @classmethod
    def get_smart_description(cls, charge_hsn_code, charge_description=""):
        charge_hsn_code = charge_hsn_code.lower().strip()
        try:
            smart_description = cls.description_hsn_map[charge_hsn_code]
        except KeyError:
            charge_description = charge_description.replace(
                "B2B-", ""
            ).title()  # for b2b addons
            smart_description = (
                f"{cls.unidentified_charge_prefix}: {charge_description}"
            )

        return smart_description
