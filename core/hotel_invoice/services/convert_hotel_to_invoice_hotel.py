from core.hotel.data_classes.hotel import Hotel
from core.hotel_invoice.data_classes.invoice_hotel import InvoiceHotel


def convert_hotel_to_invoice_hotel(hotel: Hotel, pms_type: str) -> InvoiceHotel:
    invoice_hotel = InvoiceHotel(
        uid=hotel.uid,
        name=hotel.name,
        email=hotel.email,
        phone_number=hotel.phone_number,
        account_details=hotel.account_details,
        gst_details=hotel.gst_details,
        legal_state_code=hotel.legal_state_code,
        legal_signature_url=hotel.legal_signature_url,
        navision_code=hotel.navision_code,
        pms_id=hotel.pms_id(pms_type),
        msme_number=hotel.msme_number,
    )
    return invoice_hotel
