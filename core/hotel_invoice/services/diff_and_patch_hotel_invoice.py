import logging

from integrations.einvoice.cleartax.einvoice_dto import IrpDetailsDTO

logger = logging.getLogger(__name__)


def diff_hotel_invoice(old_hotel_invoice, new_hotel_invoice):
    # gstin = has_gstin_been_updated(old_hotel_invoice.seller_gst_details, new_hotel_invoice.seller_gst_details)
    gstin = False  # stubbing for cs events to decide if gstin has changed
    status = has_status_updated(old_hotel_invoice, new_hotel_invoice)
    issued_to_type = has_issued_to_type_updated(
        old_hotel_invoice.issued_to_type, new_hotel_invoice.issued_to_type
    )
    is_einvoice = new_hotel_invoice.is_einvoice and not old_hotel_invoice.is_einvoice
    items_to_check = [gstin, status, issued_to_type, is_einvoice]
    if not any(items_to_check):
        return False
    return True


def has_issued_to_type_updated(old_issued_to_type, new_issued_to_type):
    return old_issued_to_type != new_issued_to_type


def is_line_items_sum_nearly_zero(new_hotel_invoice_line_items):
    new_hotel_invoice_line_items = list(new_hotel_invoice_line_items)
    sum_of_all_line_item_charges = 0
    for line_item in new_hotel_invoice_line_items:
        sum_of_all_line_item_charges += line_item.charge.pre_tax + line_item.charge.tax
    if abs(sum_of_all_line_item_charges) < 1:
        return True
    return False


def has_status_updated(old_hotel_invoice, new_hotel_invoice):
    return old_hotel_invoice.status != new_hotel_invoice.status or (
        not old_hotel_invoice.is_locked_in_crs and new_hotel_invoice.is_locked_in_crs
    )


def patch_hotel_invoice(
    old_hotel_invoice, new_hotel_invoice
):  # pylint: disable=invalid-name
    old_hotel_invoice.status = new_hotel_invoice.status
    if new_hotel_invoice.is_btt_invoice and is_line_items_sum_nearly_zero(
        new_hotel_invoice.line_items
    ):
        logger.info(
            f"Skipping the line item update, got a btt_invoice with "
            f"invoice_number {new_hotel_invoice.invoice_number} and hotel_id {new_hotel_invoice.hotel_id}"
        )
    else:
        old_hotel_invoice.line_items = new_hotel_invoice.line_items
    # old_hotel_invoice.seller_gst_details = new_hotel_invoice.seller_gst_details
    if not old_hotel_invoice.order_id:
        old_hotel_invoice.order_id = new_hotel_invoice.order_id
    old_hotel_invoice.check_in = new_hotel_invoice.check_in
    old_hotel_invoice.check_out = new_hotel_invoice.check_out
    old_hotel_invoice.issued_to_type = new_hotel_invoice.issued_to_type
    if not old_hotel_invoice.is_locked_in_crs and new_hotel_invoice.is_locked_in_crs:
        old_hotel_invoice.is_locked_in_crs = new_hotel_invoice.is_locked_in_crs
    if not old_hotel_invoice.is_einvoice and new_hotel_invoice.is_einvoice:
        old_hotel_invoice.set_irp_details(
            IrpDetailsDTO(
                irn=new_hotel_invoice.irn,
                qr_code=new_hotel_invoice.qr_code,
                ack_no=new_hotel_invoice.ack_no,
                acked_on=new_hotel_invoice.acked_on,
                signed_invoice=new_hotel_invoice.signed_invoice,
            )
        )
    return old_hotel_invoice
