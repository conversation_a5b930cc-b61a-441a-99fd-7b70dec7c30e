import datetime
from dataclasses import dataclass
from typing import ClassVar, Sequence

from core.common.constants import CreditNoteStatus
from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.gst_details import GstDetails
from core.hotel_invoice.data_classes.credit_note_line_item import LineItem
from core.hotel_invoice.data_classes.invoice_hotel import InvoiceHotel


@dataclass
class HotelCreditNote(BaseDataClass):
    credit_note_id: str
    booking_id: str
    hotel_id: str
    pms_type: str
    credit_note_number: str
    bill_id: str
    issued_to_type: str
    hotel: InvoiceHotel
    gst_details: GstDetails
    line_items: Sequence[LineItem]
    reference_invoice_numbers: Sequence[str] = None

    status: str = CreditNoteStatus.INITIATED
    created_at: ClassVar[datetime.datetime] = None

    source: str = ""
    order_id: str = ""
    credit_note_url: str = ""
    credit_note_date: str = ""

    irn: str = None
    qr_code: str = None
    is_einvoice: bool = False
    ack_no: str = None
    acked_on: str = None
    signed_invoice: str = None

    @property
    def uid(self):
        return self.credit_note_id

    def get_linked_invoice_ids(self):
        return [line_item.original_invoice_id for line_item in self.line_items or []]

    def set_irp_details(self, irp_details):
        self.irn = irp_details.irn
        self.qr_code = irp_details.qr_code
        self.ack_no = irp_details.ack_no
        self.signed_invoice.irp_ack_date = irp_details.acked_on
        self.is_einvoice = True
