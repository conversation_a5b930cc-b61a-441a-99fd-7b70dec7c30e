from collections import defaultdict

from core.common.constants import InvoiceStatus


class InvoiceTotals:
    def __init__(self, invoice):
        self.invoice = invoice

    @property
    def all_tax_codes(self):
        tax_codes_sublist = [
            list([item.code for item in line_item.charge.tax_breakup])
            for line_item in self.invoice.line_items
        ]
        return sorted(
            set(sum(tax_codes_sublist, []))
        )  # flattens list of list and extracts set

    @property
    def pre_tax(self):
        if self.invoice.status == InvoiceStatus.CANCELLED:
            return 0
        return sum([line_item.charge.pre_tax for line_item in self.invoice.line_items])

    @property
    def tax(self):
        if self.invoice.status == InvoiceStatus.CANCELLED:
            return 0
        return sum([line_item.charge.tax for line_item in self.invoice.line_items])

    @property
    def post_tax(self):
        if self.invoice.status == InvoiceStatus.CANCELLED:
            return 0
        return self.pre_tax + self.tax

    @property
    def tax_breakup(self):
        taxes_sum_per_tax = defaultdict(int)
        for tax_name in self.all_tax_codes:
            for line_item in self.invoice.line_items:
                tax_breakup = line_item.charge.get_tax_breakup_for_tax_code(tax_name)
                taxes_sum_per_tax[tax_name] += (
                    0
                    if self.invoice.status == InvoiceStatus.CANCELLED
                    else tax_breakup.amount
                )
        return taxes_sum_per_tax
