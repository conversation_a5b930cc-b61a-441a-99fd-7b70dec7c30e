from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import Optional, Sequence

from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.credit_note_charge import Charge
from core.common.data_classes.customer import Customer
from core.hotel.data_classes.room import Room


@dataclass(eq=True, unsafe_hash=True)
class LineItem(BaseDataClass):
    charge: Charge
    room: Room
    customers: Sequence[Customer]
    original_invoice_id: Optional[str] = None

    def __post_init__(self):
        self.customers = frozenset(self.customers)
