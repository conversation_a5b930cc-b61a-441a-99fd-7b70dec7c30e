from dataclasses import dataclass  # pylint: disable=wrong-import-order

from core.common.data_classes.base import BaseDataClass


@dataclass
class HotelInvoiceReport(BaseDataClass):
    cs_hotel_id: str
    hotel_id: str
    hotel_trade_name: str
    hotel_nav_code: str
    billed_by_legal_name: str
    billed_by_gstin: str
    billed_by_address: str
    billed_by_city: str
    billed_by_state: str
    billed_by_pin_code: str
    group_code: str
    booking_id: str
    reference_number: str
    room_booking_code: str
    booking_date: str
    checkin: str
    checkout: str
    days: str
    room_number: str
    room_type: str
    occupancy: str
    guest_names: str
    invoice_number: str
    invoice_date: str
    charge_type: str
    sac_code: str
    pre_tax_price: str
    total_amount: str
    cgst_value: str
    cgst_percent: str
    sgst_value: str
    sgst_percent: str
    igst_value: str
    igst_percent: str
    flood_cess_value: int
    flood_cess_percent: int
    billed_to_legal_name: str
    billed_to_gstin_number: str
    billed_to_address: str
    billed_to_city: str
    billed_to_state: str
    billed_to_pin_code: str
    booking_owner_name: str
    customer_invoice_number: str
    source: str
    sub_source: str
    report_date: str
    pms_type: str
    issued_to_type: str
    total_invoice_amount: str
    original_invoice_number: str
    entity_type: str
    unique_ref_id: str
    status: str = ""
    purchase_invoice_report_status: str = ""
    created_on: str = ""
