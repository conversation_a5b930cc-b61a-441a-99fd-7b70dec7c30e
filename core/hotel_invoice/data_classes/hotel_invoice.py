import datetime
from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import ClassVar, Sequence

from core.common.constants import InvoiceStatus
from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.gst_details import GstDetails
from core.common.utils.date import maybe_convert_string_to_datetime
from core.hotel_invoice.data_classes.invoice_hotel import InvoiceHotel
from core.hotel_invoice.data_classes.line_item import LineItem


@dataclass
class HotelInvoice(BaseDataClass):
    invoice_id: str
    booking_id: str
    hotel_id: str
    pms_type: str
    invoice_number: str
    bill_id: str
    issued_to_type: str

    hotel: InvoiceHotel
    gst_details: GstDetails
    line_items: Sequence[LineItem]

    status: str = InvoiceStatus.INITIATED
    source: str = ""
    order_id: str = ""
    check_in: str = ""
    check_out: str = ""
    invoice_url: str = ""
    invoice_date: str = ""
    # for backward compatibility
    _created_at: ClassVar[datetime.datetime]

    __is_btt_invoice = False
    is_locked_in_crs: bool = False

    irn: str = None
    qr_code: str = None
    is_einvoice: bool = False
    ack_no: str = None
    acked_on: str = None
    signed_invoice: str = None

    @property
    def uid(self):
        return self.invoice_id

    def __post_init__(self):
        self.line_items = tuple(self.line_items)
        if self.check_in:
            self.check_in = maybe_convert_string_to_datetime(self.check_in)

        if self.check_out:
            self.check_out = maybe_convert_string_to_datetime(self.check_out)

    @property
    def is_btt_invoice(self) -> bool:
        return self.__is_btt_invoice

    @is_btt_invoice.setter
    def is_btt_invoice(self, is_btt_invoice: bool) -> None:
        self.__is_btt_invoice = is_btt_invoice

    @property
    def created_at(self):
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        assert created_at is not None
        self._created_at = created_at

    def set_irp_details(self, irp_details):
        self.irn = irp_details.irn
        self.qr_code = irp_details.qr_code
        self.ack_no = irp_details.ack_no
        self.signed_invoice = irp_details.acked_on
        self.is_einvoice = True
