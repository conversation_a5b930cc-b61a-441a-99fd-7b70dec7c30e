import datetime
import logging
import uuid

import requests

from core.common.constants import PmsType
from core.hotel_invoice.data_classes.hotel_invoice import (
    HotelInvoice as HotelInvoiceDataClass,
)
from core.notification import Slack, constants, notify
from integrations.crs.treebo_crs.invoice.get_hotel_invoice import CRSGetHotelInvoice
from reseller import app
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)
from reseller.repository.invoice_mapping import get_invoice_mapping_repository

logger = logging.getLogger(__name__)


class HotelInvoice:
    pms_type = PmsType.CRS

    @staticmethod
    def get_domain():
        return ServiceRegistryClient.get_service_url(
            ServiceEndPointNames.CRS_SERVICE_URL
        )

    @classmethod
    def get_from_customer_invoice(
        cls, customer_invoice_id, invoice_hotel, booking_id, gst_details
    ):
        crs_hotel_invoice = CRSGetHotelInvoice.from_customer_invoice(
            customer_invoice_id
        )
        return cls._convert_to_hotel_invoice_dataclass(
            crs_hotel_invoice, invoice_hotel, booking_id, gst_details
        )

    @classmethod
    def _convert_to_hotel_invoice_dataclass(
        cls, crs_hotel_invoice_data, invoice_hotel, booking_id, gst_details
    ):
        uid = str(uuid.uuid4())

        bare_invoice = HotelInvoiceDataClass(
            invoice_id=uid,
            hotel_id=invoice_hotel.uid,
            booking_id=booking_id,
            pms_type="crs",
            invoice_number=crs_hotel_invoice_data["invoice_number"],
            hotel=invoice_hotel,
            line_items=[],
            gst_details=gst_details,
            source="",
            issued_to_type=crs_hotel_invoice_data["issued_to_type"],
            bill_id=crs_hotel_invoice_data["bill_id"],
            invoice_date=crs_hotel_invoice_data.get("invoice_date"),
        )
        bare_invoice.created_at = crs_hotel_invoice_data.get(
            "created_at", datetime.datetime.now()
        )
        bare_invoice.invoice_date = crs_hotel_invoice_data["invoice_date"]
        return bare_invoice

    @classmethod
    def get_invoice_url(cls, hotel_invoice_id):
        invoice_mapping = get_invoice_mapping_repository().get(
            hotel_invoice_id=hotel_invoice_id
        )
        crs_url = (
            f"{cls.get_domain()}/v1/invoices/{invoice_mapping.customer_invoice_id}"
        )
        try:
            invoice = requests.get(crs_url)
            invoice_data = invoice.json()["data"]
            return invoice_data["invoice_url"]
        except Exception as e:
            msg = f"Not able to fetch customer invoice pdf url {crs_url} due to {str(e)} for invoice {invoice_mapping.customer_invoice_id}"
            logger.error(msg)
            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))

    @classmethod
    def get_invoice_signed_url(cls, hotel_invoice_id):
        invoice_mapping = get_invoice_mapping_repository().get(
            hotel_invoice_id=hotel_invoice_id
        )
        crs_url = (
            f"{cls.get_domain()}/v1/invoices/{invoice_mapping.customer_invoice_id}"
        )
        try:
            invoice = requests.get(crs_url)
            invoice_data = invoice.json()["data"]
            return invoice_data["signed_url"]
        except Exception as e:
            msg = f"Not able to fetch customer invoice pdf signed url due to {str(e)}"
            logger.error(msg)
            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))

    @classmethod
    def get_hotel_invoice_metadata(self, hotel_invoice, hotel, source):
        meta = {}
        meta["hotel_id"] = hotel.pms_id(hotel_invoice.pms_type)
        meta["source"] = source
        return meta
