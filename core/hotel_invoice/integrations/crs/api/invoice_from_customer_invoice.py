from flask import jsonify, request
from flask.views import MethodView
from logger import logger
from marshmallow import ValidationError

from core.common.api.api_response import APIResponse
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.integrations.crs.dto.invoice_request import (
    InvoiceFromCustomerInvoiceRequestSchema,
)
from core.hotel_invoice.integrations.crs.hotel_invoice import HotelInvoice
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from reseller.services.treebo_gst import TreeboGetGSTDetails


class CRSGetInvoiceFromCustomerInvoiceAPI(MethodView):
    """

    WARNING: FOR INTERNAL USE ONLY
    """

    def get(self):
        request_data = jsonify(request.args).json

        try:
            InvoiceFromCustomerInvoiceRequestSchema().load(data=request_data)

            logger.info(
                "received {req} request for invoice-id: {iid}".format(
                    iid=request_data["customer_invoice_id"], req=self.__class__.__name__
                )
            )

            hotel = GetHotel(hotel_id=request_data["hotel_id"]).get_hotel()

            gst_details = TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details()

            invoice_hotel = convert_hotel_to_invoice_hotel(hotel, "crs")
            get_invoices_json = HotelInvoice().get_from_customer_invoice(
                request_data["customer_invoice_id"],
                invoice_hotel=invoice_hotel,
                booking_id=request_data["booking_id"],
                gst_details=gst_details,
            )

        except ValidationError as e:
            logger.error(f"Validation failed due to: {str(e)}")
            return APIResponse.error_response(message=str(e), resp_code=400)
        except Exception as e:
            message = "error running getinvoice api on hx for invoice_id: {iid}".format(
                iid=request_data["invoice_id"]
            )
            logger.exception(message)

            return APIResponse.error_response(message=message, resp_code=500)

        return APIResponse.success_response(message=get_invoices_json, resp_code=200)
