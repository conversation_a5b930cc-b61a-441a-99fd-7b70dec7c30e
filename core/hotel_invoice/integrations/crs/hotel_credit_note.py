import logging
import uuid

import requests

from core.common.constants import PmsType
from core.hotel_invoice.data_classes.hotel_credit_note import (
    HotelCreditNote as HotelCreditNoteDataclass,
)
from core.notification import Slack, constants, notify
from integrations.crs.treebo_crs.invoice.get_hotel_credit_note import (
    CRSGetHotelCreditNote,
)
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)
from reseller.repository.hotel_customer_credit_note_mapping import (
    get_hotel_customer_credit_note_mapping_repository,
)

logger = logging.getLogger(__name__)


class HotelCreditNote:
    pms_type = PmsType.CRS

    @staticmethod
    def get_domain():
        return ServiceRegistryClient.get_service_url(
            ServiceEndPointNames.CRS_SERVICE_URL
        )

    @classmethod
    def get_from_customer_credit_note(
        cls, customer_credit_note_id, invoice_hotel, booking_id, gst_details
    ):
        crs_hotel_credit_note = CRSGetHotelCreditNote.from_customer_credit_note_id(
            customer_credit_note_id
        )
        return (
            cls._convert_to_hotel_credit_note_dataclass(
                crs_hotel_credit_note, invoice_hotel, booking_id, gst_details
            )
            if crs_hotel_credit_note
            else None
        )

    @classmethod
    def _convert_to_hotel_credit_note_dataclass(
        cls, crs_hotel_credit_note_data, invoice_hotel, booking_id, gst_details
    ):
        uid = str(uuid.uuid4())
        bare_credit_note = HotelCreditNoteDataclass(
            credit_note_id=uid,
            hotel_id=invoice_hotel.uid,
            booking_id=booking_id,
            pms_type="crs",
            credit_note_number=crs_hotel_credit_note_data.credit_note_number,
            hotel=invoice_hotel,
            line_items=[],
            gst_details=gst_details,
            source="",
            issued_to_type=crs_hotel_credit_note_data.issued_to_type,
            bill_id=crs_hotel_credit_note_data.bill_id,
        )
        bare_credit_note.created_at = crs_hotel_credit_note_data.created_at
        bare_credit_note.credit_note_date = (
            crs_hotel_credit_note_data.credit_note_date.strftime("%Y-%m-%d")
        )
        return bare_credit_note

    @classmethod
    def get_hotel_credit_note_url(cls, hotel_credit_note_id):
        credit_note_mapping = get_hotel_customer_credit_note_mapping_repository().get(
            hotel_credit_note_id=hotel_credit_note_id
        )
        crs_url = f"{cls.get_domain()}/v1/credit-notes/{credit_note_mapping.customer_credit_note_id}"
        try:
            credit_note = requests.get(crs_url)
            credit_note_data = credit_note.json()["data"]
            return credit_note_data["credit_note_url"]
        except Exception as e:
            msg = f"Not able to fetch customer credit note pdf url due to {str(e)}"
            logger.error(msg)
            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))

    @classmethod
    def get_hotel_credit_note_signed_url(cls, hotel_credit_note_id):
        credit_note_mapping = get_hotel_customer_credit_note_mapping_repository().get(
            hotel_credit_note_id=hotel_credit_note_id
        )
        crs_url = f"{cls.get_domain()}/v1/credit-notes/{credit_note_mapping.customer_credit_note_id}"
        try:
            credit_note = requests.get(crs_url)
            credit_note_data = credit_note.json()["data"]
            return credit_note_data["signed_url"]
        except Exception as e:
            msg = (
                f"Not able to fetch customer credit note pdf signed url due to {str(e)}"
            )
            logger.error(msg)
            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
