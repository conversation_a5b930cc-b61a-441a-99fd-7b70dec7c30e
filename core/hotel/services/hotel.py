from core.hotel.providers.get_hotel.catalogue import CatalogueGetHotel
from core.hotel.providers.get_hotels.catalogue import CatalogueGetHotels
from core.hotel.providers.hotel_seller_type.seller_type import CatalogueGetSellerType

GetHotel = CatalogueGetHotel
GetHotels = CatalogueGetHotels


class HotelSellerTypeService:
    def __init__(self, hotel_id):
        self.hotel_id = hotel_id

    def get_seller_type(self, date):
        return CatalogueGetSellerType.get_seller_type(hotel_id=self.hotel_id, date=date)
