import logging

import requests
from treebo_commons.multitenancy.tenant_client import <PERSON>ant<PERSON><PERSON>
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from core.common.constants import PmsType
from core.common.data_classes.address import Address
from core.common.data_classes.gst_details import GstDetails
from core.hotel.data_classes.account_details import AccountDetails
from core.hotel.data_classes.hotel import Hotel
from core.hotel.exceptions import UnableToGetHotelDetails
from core.hotel.interfaces.get_hotel import GetHotelInterface
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

cache = {}  # pylint: disable=invalid-name
hx_to_cs_id_cache = {}  # pylint: disable=invalid-name

logger = logging.getLogger(__name__)


class CatalogueGetHotel(GetHotelInterface):
    CATALOG_HOST = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.CATALOG_SERVICE_URL
    )

    class HxHotel:
        def __init__(self, hotel_id):
            self.hotel_id = hotel_id

        @classmethod
        def get_hotel(cls, hotel_hx_id):
            url = f"{CatalogueGetHotel.CATALOG_HOST}/cataloging-service/api/v1/properties/{hotel_hx_id}/pms/hx"
            data = CatalogueGetHotel._make_request(
                url
            )  # pylint: disable=protected-access
            hotel = CatalogueGetHotel.convert_to_dataclass(data)

            hx_to_cs_id_cache[hotel_hx_id] = hotel.uid
            cache[hotel.uid] = hotel
            return hotel

    pms_map = {"hx": HxHotel}

    def _get_hotel(self):
        if self.hotel_id in cache:
            return cache[self.hotel_id]

        url = f"{CatalogueGetHotel.CATALOG_HOST}/cataloging-service/api/v1/properties/{self.hotel_id}"
        try:
            data = self._make_request(url)
        except UnableToGetHotelDetails:
            msg = f"Hotel details not found {self.hotel_id}"
            logger.info(msg)
            raise

        hotel = self.convert_to_dataclass(data)

        cache[self.hotel_id] = hotel

        return hotel

    @classmethod
    def from_pms_hotel_id(cls, hotel_pms_id, pms_type=PmsType.CRS):
        return cls.pms_map.get(pms_type).get_hotel(hotel_pms_id)

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        if not headers.get("X-Tenant-Id"):
            headers["X-Tenant-Id"] = TenantClient.get_default_tenant()
        return headers

    @classmethod
    def _make_request(cls, url):
        try:
            response = requests.get(url, headers=cls.get_headers())
            response.raise_for_status()
            json = response.json()
            if "id" not in json:
                msg = f"Invalid response from cs for url: {url} => {json}"
                logger.error(msg)
                raise RuntimeError(msg)
            return json
        except Exception as e:
            logger.exception(e)
            raise UnableToGetHotelDetails(str(e))

    @classmethod
    def convert_to_dataclass(cls, data):
        address = cls._address(data)

        property_details = data["property_details"]

        gstin = property_details["gstin"] or ""
        navision_code = property_details["navision_code"] or ""
        state_code = cls._get_legal_state_code(data)
        legal_name = str(data["name"]["legal_name"])
        owner = {}
        owners = [owner for owner in data["owners"] if owner.get("is_primary_owner")]
        if owners:
            owner = owners[0]

        bank_details = property_details["bank_details"]
        account_details = None
        if bank_details:
            account_details = AccountDetails(
                account_name=bank_details["account_name"],
                account_number=bank_details["account_number"],
                account_type=bank_details["type"],
                bank_name=bank_details["bank"],
                ifsc_code=bank_details["ifsc_code"],
                pan_number=property_details["pan"],
            )

        hotel = Hotel(
            uid=str(data["id"]),
            name=str(data["name"]["new_name"]),
            email=owner.get("email", ""),
            phone_number=owner.get("phone_number", ""),
            account_details=account_details,
            gst_details=GstDetails(
                legal_name=legal_name,
                gstin_number=gstin,
                address=address,
                has_lut=property_details["has_lut"],
            ),
            legal_state_code=state_code,
            legal_signature_url=data["property_details"]["legal_signature"],
            navision_code=navision_code,
            has_lut=property_details["has_lut"],
            msme_number=property_details.get("msme_number"),
        )
        hotel.status = data["status"]
        hotel.add_pms_id(PmsType.HX, str(data["hx_id"]))
        hotel.add_pms_id(PmsType.CRS, str(data["id"]))

        return hotel

    @classmethod
    def _address(cls, data):
        if (
            data["location"]["legal_address"]
            and data["location"]["legal_pincode"]
            and data["location"]["legal_city"]
        ):
            field1 = data["location"]["legal_address"]
            field2 = ""
            city = data["location"]["legal_city"]["name"]
            state = data["location"]["legal_state"]["name"]
            pincode = str(data["location"]["legal_pincode"])

        else:
            field1 = data["location"]["postal_address"]
            field2 = data["location"]["locality"]["name"]
            city = data["location"]["city"]["name"]
            state = data["location"]["state"]["name"]
            pincode = str(data["location"]["pincode"])

        country = "India"

        for field in [field2, city, state, pincode, country]:
            # clean field1 as it is a string of address representation
            field1 = field1.lower().replace(field.lower(), "").strip().rstrip(",")

        address = Address(
            field1=field1,
            field2=field2,
            city=city,
            state=state,
            country=country,
            pincode=pincode,
        )
        return address

    @classmethod
    def _get_legal_state_code(cls, data):
        if data["location"]["legal_state"]:
            return data["location"]["legal_state"]["code"] or ""

        return data["location"]["state"]["code"] or ""
