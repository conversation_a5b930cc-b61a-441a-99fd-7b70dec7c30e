import logging

import requests
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from core.common.data_classes.hotel_owner import HotelOwner
from core.hotel.exceptions import GetHotelOwnerFailureException
from core.hotel.interfaces.get_hotel_owner import GetHotelOwnerInterface
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

logger = logging.getLogger(__name__)


class ProwlGetHotelOwner(GetHotelOwnerInterface):
    PROWL_SERVICE_URL = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.PROWL_SERVICE_URL
    )

    @classmethod
    def get_hotel_owner(cls, cs_hotel_id):
        url = f"{cls.PROWL_SERVICE_URL}/prowl/rest/v3/hotelowner'/{cs_hotel_id}"
        all_hotel_owner_data = ProwlGetHotelOwner._make_request(url)
        hotel_owner = [
            ProwlGetHotelOwner.convert_to_dataclass(data)
            for data in all_hotel_owner_data["data"]
        ]
        return hotel_owner

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        if not headers.get("X-Tenant-Id"):
            headers["X-Tenant-Id"] = TenantClient.get_default_tenant()
        return headers

    @classmethod
    def _make_request(cls, url):
        try:
            response = requests.get(url, headers=cls.get_headers())
            response.raise_for_status()
            json = response.json()
            if "data" not in json:
                msg = f"Invalid response from prowl for url: {url} => {json}"
                logger.error(msg)
                raise RuntimeError(msg)
            return json
        except Exception as e:
            logger.exception(e)
            raise GetHotelOwnerFailureException(str(e))

    @classmethod
    def convert_to_dataclass(cls, data):
        hotel_owner = HotelOwner(
            first_name=data["first_name"],
            last_name=data["last_name"],
            email_id=data["email_id"],
            contact_number=data["contact_number"],
        )
        return hotel_owner
