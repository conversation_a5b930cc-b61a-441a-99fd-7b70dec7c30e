from core.hotel.interfaces.get_hotels import GetHotelsInterface
from core.hotel.providers.get_hotel.catalogue import CatalogueGetHotel
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)


class CatalogueGetHotels(GetHotelsInterface):
    CATALOG_HOST = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.CATALOG_SERVICE_URL
    )

    @classmethod
    def get_hotels(cls, reseller_hotels_only=True):
        url = f"{cls.CATALOG_HOST}/cataloging-service/api/v1/properties"
        if reseller_hotels_only:
            url = f"{url}?sold_as=reseller"

        all_hotel_data = CatalogueGetHotel._make_request(
            url
        )  # pylint: disable=protected-access
        hotels = [
            CatalogueGetHotel.convert_to_dataclass(data) for data in all_hotel_data
        ]
        return hotels
