import logging

import requests
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from core.hotel.data_classes.hotel_detail import HotelDetail
from core.hotel.exceptions import UnableToGetHotelDetails
from object_registry import register_instance
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

logger = logging.getLogger(__name__)


@register_instance()
class CatalogueGetHotelDetails:
    CATALOG_HOST = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.CATALOG_SERVICE_URL
    )

    @classmethod
    def get_hotels(cls, hotel_ids=None):
        url = f"{cls.CATALOG_HOST}/cataloging-service/api/v2/properties"
        if hotel_ids:
            ids = ",".join(hotel_ids).replace("'", "")
            url = f"{url}?property_id={ids}"

        all_hotel_data = cls._make_request(url)
        hotels = [HotelDetail.from_json(data) for data in all_hotel_data]
        return hotels

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        if not headers.get("X-Tenant-Id"):
            headers["X-Tenant-Id"] = TenantClient.get_default_tenant()
        return headers

    @classmethod
    def _make_request(cls, url):
        try:
            response = requests.get(url, headers=cls.get_headers())
            response.raise_for_status()
            json = response.json()
            if (isinstance(json, list) and not json[0].get("id")) or (
                not isinstance(json, list) and not json.get("id")
            ):
                msg = f"Invalid response from cs for url: {url} => {json}"
                logger.error(msg)
                raise RuntimeError(msg)
            return json
        except Exception as e:
            logger.exception(e)
            raise UnableToGetHotelDetails(str(e))
