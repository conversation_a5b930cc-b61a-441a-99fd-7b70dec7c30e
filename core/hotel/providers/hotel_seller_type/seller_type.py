import logging

import requests
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

logger = logging.getLogger()


class CatalogueGetSellerType:
    CATALOG_HOST = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.CATALOG_SERVICE_URL
    )

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        if not headers.get("X-Tenant-Id"):
            headers["X-Tenant-Id"] = TenantClient.get_default_tenant()
        return headers

    @classmethod
    def get_seller_type(cls, hotel_id, date):
        try:
            logger.info(
                f"Getting seller type from catalog for hotel {hotel_id} and date {date}"
            )
            base_url = f"{CatalogueGetSellerType.CATALOG_HOST}/cataloging-service/api/v1/seller-type"
            response = requests.get(
                url=base_url,
                params={"property_id": hotel_id, "date": date},
                headers=cls.get_headers(),
            )
            response.raise_for_status()
            json = response.json()
            return json.get("seller_type", None)
        except Exception as e:
            logger.exception(
                f"Failed to get seller type for hotel {hotel_id} due to: {str(e)}"
            )
            raise
