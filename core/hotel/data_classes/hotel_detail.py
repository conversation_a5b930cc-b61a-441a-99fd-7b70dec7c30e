from dataclasses import dataclass


@dataclass
class HotelOwner:
    first_name: str
    last_name: str
    email: str
    is_primary_owner: bool
    phone_number: str

    def to_dict(self):
        return dict(
            first_name=self.first_name,
            last_name=self.last_name,
            email=self.email,
            is_primary_owner=self.is_primary_owner,
            phone_number=self.phone_number,
        )

    @staticmethod
    def from_json(owner_json):
        return HotelOwner(
            first_name=owner_json.get("first_name"),
            last_name=owner_json.get("last_name"),
            email=owner_json.get("email"),
            is_primary_owner=owner_json.get("is_primary_owner"),
            phone_number=owner_json.get("phone_number"),
        )


@dataclass
class HotelDetail:
    hotel_id: str
    hotel_name: str
    hotel_owners: [HotelOwner]

    def to_dict(self):
        return dict(
            hotel_id=self.hotel_id,
            hotel_name=self.hotel_name,
            hotel_owners=[hotel_owner.to_dict() for hotel_owner in self.hotel_owners],
        )

    @staticmethod
    def from_json(hotel_data_json):
        return HotelDetail(
            hotel_id=hotel_data_json.get("id"),
            hotel_name=hotel_data_json.get("name").get("new_name"),
            hotel_owners=[
                HotelOwner.from_json(owner) for owner in hotel_data_json.get("owners")
            ]
            if hotel_data_json.get("owners")
            else [],
        )

    @staticmethod
    def from_db_json(hotel_data_json):
        return HotelDetail(
            hotel_id=hotel_data_json.get("hotel_id"),
            hotel_name=hotel_data_json.get("hotel_name"),
            hotel_owners=[
                HotelOwner.from_json(ho) for ho in hotel_data_json.get("hotel_owners")
            ],
        )
