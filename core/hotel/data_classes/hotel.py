# -*- coding: utf-8 -*-
from dataclasses import dataclass, field  # pylint: disable=wrong-import-order

from core.common.data_classes.abstract_hotel import AbstractHotel


@dataclass
class Hotel(AbstractHotel):
    _pms_ids: dict = field(default_factory=dict)

    def add_pms_id(self, pms_type, pms_id):
        self._pms_ids[pms_type] = pms_id

    def pms_id(self, pms_type):
        try:
            return self._pms_ids[pms_type]
        except KeyError:
            raise ValueError(f"Invalid pms type: {pms_type}")
