import abc

from core.common.constants import PmsType
from core.hotel.data_classes.hotel import Hotel


class GetHotelInterface(metaclass=abc.ABCMeta):
    def __init__(self, hotel_id):
        self.hotel_id = hotel_id

    def get_hotel(self) -> Hotel:
        hotel = self._get_hotel()
        assert isinstance(hotel, Hotel)
        return hotel

    @abc.abstractmethod
    def _get_hotel(self) -> Hotel:
        pass

    @classmethod
    @abc.abstractmethod
    def from_pms_hotel_id(cls, hotel_pms_id, pms_type=PmsType.HX) -> Hotel:
        pass
