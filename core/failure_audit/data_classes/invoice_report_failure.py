from dataclasses import dataclass
from datetime import date, datetime


@dataclass
class InvoiceReportFailure:
    process_name: str
    hotel_id: str
    invoice_id: str
    invoice_number: str
    entity_type: str
    status: str
    failure_description: str
    failure_trace: str
    failed_on: str
    created_at: datetime = None
    updated_at: datetime = None
    last_retry_at: datetime = None

    def to_dict(self):
        return dict(
            process_name=self.process_name,
            hotel_id=self.hotel_id,
            invoice_id=self.invoice_id,
            invoice_number=self.invoice_number,
            entity_type=self.entity_type,
            status=self.status,
            failure_description=self.failure_description,
            failure_trace=self.failure_trace,
            failed_on=self.failed_on,
            created_at=self.created_at,
            updated_at=self.updated_at,
            last_retry_at=self.last_retry_at,
        )

    @staticmethod
    def from_json(invoice_report_failure_json):
        return InvoiceReportFailure(
            process_name=invoice_report_failure_json.get("process_name"),
            hotel_id=invoice_report_failure_json.get("hotel_id"),
            invoice_id=invoice_report_failure_json.get("invoice_id"),
            invoice_number=invoice_report_failure_json.get("invoice_number"),
            entity_type=invoice_report_failure_json.get("entity_type"),
            status=invoice_report_failure_json.get("status"),
            failure_description=invoice_report_failure_json.get("failure_description"),
            failure_trace=invoice_report_failure_json.get("failure_trace"),
            failed_on=invoice_report_failure_json.get("failed_on"),
            created_at=invoice_report_failure_json.get("created_at"),
            updated_at=invoice_report_failure_json.get("updated_at"),
            last_retry_at=invoice_report_failure_json.get("last_retry_at"),
        )
