from dataclasses import dataclass
from datetime import date, datetime


@dataclass
class IngestionFailure:
    resource_id: str
    resource_type: str
    status: str
    failure_description: str
    failed_on: str
    created_at: datetime = None
    updated_at: datetime = None
    last_retry_at: datetime = None

    def to_dict(self):
        return dict(
            resource_id=self.resource_id,
            resource_type=self.resource_type,
            status=self.status,
            failure_description=self.failure_description,
            failed_on=self.failed_on,
            created_at=self.created_at,
            updated_at=self.updated_at,
            last_retry_at=self.last_retry_at,
        )

    @staticmethod
    def from_json(owner_json):
        return IngestionFailure(
            resource_id=owner_json.get("resource_id"),
            resource_type=owner_json.get("resource_type"),
            status=owner_json.get("status"),
            failure_description=owner_json.get("failure_description"),
            failed_on=owner_json.get("failed_on"),
            created_at=owner_json.get("created_at"),
            updated_at=owner_json.get("updated_at"),
            last_retry_at=owner_json.get("last_retry_at"),
        )
