import logging

from core.common.integrations.file_storage.backends import S3FileStorage

logger = logging.getLogger(__name__)


class FileStorageService:
    def __init__(self, backend):
        self.backend = backend

    def upload_file(self, filepath, file_key):
        """
        Upload local file to a data store
        :param filepath: local file path that is to be uploaded
        """
        logger.debug(f"Uploading file {filepath} to file storage")
        self.backend.upload_file(filepath, file_key)

    def download_file(self, filepath):
        """
        Download file from a data store
        :param filepath: data store file path
        """
        logger.debug(f"Downloading file {filepath} from file storage")
        self.backend.download_file(filepath)

    def get_signed_url(self, filepath, expires_in=None):
        """
        Get signed url for a file to download from data store
        :param filepath: data store file path
        :param expires_in: time in seconds for which url should be valid
        :return: signed url for a file
        """
        logger.debug(f"Getting signed url for file {filepath} from file storage")
        signed_url = self.backend.get_signed_url(filepath, expires_in)
        return signed_url

    def sign_url(self, url, expires_in=None):
        logger.debug(f"Getting signed url for {url} from file storage")
        signed_url = self.backend.sign_url(url, expires_in)
        return signed_url

    def get_download_url(self, filepath):
        logger.debug(f"Getting downloadable url for file {filepath} from file storage")
        downloadable_url = self.backend.get_download_url(filepath)
        return downloadable_url

    def delete_file(self, filepath):
        """
        Delete file from data store
        :param filepath: data store file path
        """
        logger.debug(f"Deleting file {filepath} from file storage")
        self.backend.delete_file(filepath)


file_storage_service = FileStorageService(
    S3FileStorage()
)  # pylint: disable=invalid-name
