from ths_common.constants.base_enum import BaseEnum


class ResellerError(BaseEnum):
    @property
    def error_code(self):
        return self.values[0]

    @property
    def message(self):
        return self.values[1]


class IngestionErrors(ResellerError):
    GENERIC_INGESTION_ERROR = (
        "0001",
        "Invoice Ingestion failed. Please contact escalations team",
    )
    MISSING_ADDRESS = "0002", "Missing address, should be greater than 3 characters"
    INVALID_GSTIN = "0003", "Invalid GSTIN"
