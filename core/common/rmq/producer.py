# pylint: disable=invalid-name,too-few-public-methods
import json
import logging
import uuid

from flask_log_request_id import current_request_id
from kombu import Connection
from kombu.entity import PERSISTENT_DELIVERY_MODE, Exchange
from kombu.pools import connections, producers

from reseller import app

logger = logging.getLogger(__name__)


class Producer:
    delivery_mode = PERSISTENT_DELIVERY_MODE
    priority = 0
    exchange_arguments = None
    retry_enabled = True
    retry_policy = dict(
        max_retries=3, interval_start=0, interval_step=30, interval_max=300
    )
    durable = True
    RMQ_URL = app.config["RABBITMQ_URL"]
    default_header = {}

    def __init__(self, exchange_name, routing_key, exchange_type="topic", **kwargs):
        if "exchange_arguments" in kwargs:
            self.exchange_arguments = kwargs["exchange_arguments"]
        if "priority" in kwargs:
            self.priority = kwargs["priority"]
        self.exchange_name = exchange_name
        self.exchange_type = exchange_type
        self.routing_key = routing_key
        self.exchange = Exchange(
            name=exchange_name,
            type=self.exchange_type,
            durable=self.durable,
            arguments=self.exchange_arguments,
        )
        self.connection = Connection(
            self.RMQ_URL, transport_options={"confirm_publish": True}
        )

    def publish(self, body, **kwargs):
        try:
            _headers = self.default_header.copy()
            _headers["api_request_headers"] = {"X-Request-ID": current_request_id()}

            _headers.update(kwargs.get("headers", {}))

            data = dict(
                headers=_headers,
                body=json.dumps(body),
                routing_key=self.routing_key,
                priority=self.priority,
                exchange=self.exchange,
                declare=[self.exchange],
                delivery_mode=self.delivery_mode,
                retry=self.retry_enabled,
                retry_policy=self.retry_policy,
                uuid=str(uuid.uuid4()),
            )
            with connections[self.connection].acquire(block=True, timeout=300) as conn:
                with producers[conn].acquire(block=True, timeout=30) as producer:
                    producer.publish(**data)
        except Exception as e:
            logger.exception(
                "Exception in producer, body: {b}, error: {e}".format(b=body, e=e)
            )
            raise e
