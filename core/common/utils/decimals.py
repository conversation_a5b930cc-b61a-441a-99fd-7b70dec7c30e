import decimal


def quantize_and_round_off(
    amount, decimal_places_to_round_off=2, round_off_strategy=decimal.ROUND_HALF_UP
):
    """
    The Standard for quantizing decimals/ floats / ints.
    This converts input to decimals and applies quantize.
    This is to provide consistency where we round off through out the code base
    """

    if not isinstance(amount, decimal.Decimal):
        amount = decimal.Decimal(amount)

    _round_off_string = "0." + "0" * decimal_places_to_round_off
    exponent_round_off = decimal.Decimal(_round_off_string)

    return amount.quantize(exponent_round_off, rounding=round_off_strategy)
