# pylint: disable=unidiomatic-typecheck
import datetime

import pytz
from dateutil import parser
from dateutil.relativedelta import relativedelta


def is_naive_datetime(date):
    return date.tzinfo is None or date.tzinfo.utcoffset(date) is None


def maybe_convert_string_to_datetime(date, ignoretz=False):
    if isinstance(date, str):
        return parser.parse(date, ignoretz=ignoretz)
    return date


def set_time_zone(timestamp, time_zone="utc"):
    """
    Sets time zone on a naive date time. Uses utc by default
    Use this instead of timestamp.replace(tzinfo=pytz.timezone(settings.TIME_ZONE))
    """
    tzone = pytz.timezone(time_zone)
    return tzone.localize(timestamp)


def get_dates_between_dates(date1, date2):
    date1 = maybe_convert_string_to_datetime(date1)
    date2 = maybe_convert_string_to_datetime(date2)
    delta = date1 - date2  # timedelta
    return [date2 + datetime.timedelta(i) for i in range(delta.days + 1)]


def utc_to_ist(timestamp):
    """
    convert UTC time to IST time
    :param: timestamp - A naive python datetime object
    :return: IST time as datetime object
    """
    utc_tz = pytz.utc
    ist_tz = pytz.timezone("Asia/Kolkata")
    timestamp = utc_tz.localize(timestamp)
    timestamp_ist = timestamp.astimezone(ist_tz)
    return timestamp_ist


def ist_to_utc(timestamp):
    local = pytz.timezone("Asia/Kolkata")
    local_dt = local.localize(timestamp)
    utc_dt = local_dt.astimezone(pytz.utc)
    return utc_dt


class Date:
    def __init__(self, _date):
        _date = maybe_convert_string_to_datetime(_date)
        if type(_date) != datetime.date:
            _date = _date.date()
        self._date = _date

    def __str__(self):
        return str(self._date)

    def date(self):
        return self._date

    def get_previous_month_date(self):
        prev_month_date = self._date - relativedelta(months=1)
        return Date(prev_month_date)

    def get_next_month_date(self):
        prev_month_date = self._date + relativedelta(months=1)
        return Date(prev_month_date)

    def get_first_day_of_month(self):
        first_day_date = self._date - relativedelta(days=self._date.day - 1)
        return Date(first_day_date)

    def get_last_day_of_month(self):
        last_day = (
            self.get_first_day_of_month().get_next_month_date().date()
            - relativedelta(days=1)
        )
        return Date(last_day)
