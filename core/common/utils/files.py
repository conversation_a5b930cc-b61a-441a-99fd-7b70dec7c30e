import csv
import logging
import os

logger = logging.getLogger(__name__)


def write_to_csv(file_name, list_of_rows):
    os.makedirs(os.path.dirname(file_name), exist_ok=True)
    file = open(file_name, "w")
    try:
        csvwriter = csv.writer(file, delimiter=",")
        for row in list_of_rows:
            csvwriter.writerow(row)
    except Exception as e:
        logger.exception(f"Error in creating CSV, {e}")
        raise e
    finally:
        file.close()
