from abc import ABC, abstractmethod


class FileStorage(ABC):
    @abstractmethod
    def upload_file(self, filepath, file_key):
        pass

    @abstractmethod
    def download_file(self, filepath):
        pass

    @abstractmethod
    def get_signed_url(self, filepath, expires_in):
        pass

    @abstractmethod
    def delete_file(self, filepath):
        pass

    @abstractmethod
    def get_download_url(self, filepath):
        pass
