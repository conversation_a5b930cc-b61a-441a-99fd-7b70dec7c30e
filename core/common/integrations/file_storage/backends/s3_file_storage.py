import logging
import mimetypes
from urllib.parse import unquote, urlparse

import boto3

from core.common.integrations.file_storage.backends.file_storage import FileStorage
from core.common.integrations.file_storage.exceptions import S3Error
from reseller import app

logger = logging.getLogger(__name__)


class S3FileStorage(FileStorage):
    file_storage_config = app.config["FILE_STORAGE_CONFIG"]
    expires_in = int(app.config["SIGNED_URL_EXPIRY_DURATION"])

    # pylint: disable=invalid-name
    region_name = file_storage_config["s3"]["region"]
    bucket = file_storage_config["s3"]["bucket"]
    s3 = boto3.client("s3", region_name=region_name)

    def upload_file(self, filepath, file_key):
        try:
            logger.info(f"Uploading file {filepath} to bucket {self.bucket} in s3")
            self.s3.upload_file(
                Filename=filepath,
                Bucket=self.bucket,
                Key=file_key,
                ExtraArgs={
                    "ContentType": mimetypes.MimeTypes().guess_type(filepath)[0]
                },
            )
        except Exception as e:
            message = f"Unable to upload file {filepath} to bucket {self.bucket} due to: {str(e)}"
            logger.exception(message)
            raise S3Error(message)

    def download_file(self, filepath):
        try:
            logger.info(f"Downloading file {filepath} from bucket {self.bucket} in s3")
            self.s3.download_file(Bucket=self.bucket, Key=filepath, Filename=filepath)
        except Exception as e:
            message = f"Unable to download file {filepath} to bucket {self.bucket} due to: {str(e)}"
            logger.exception(message)
            raise S3Error(message)

    def get_download_url(self, filepath):
        download_url = (
            f"https://{self.bucket}.s3.{self.region_name}.amazonaws.com/{filepath}"
        )
        return download_url

    def sign_url(self, url, expires_in=None):
        if expires_in is None:
            expires_in = self.expires_in
        url_path = urlparse(url).path[1:]
        file_path = unquote(url_path)
        return self.get_signed_url(file_path, expires_in)

    def get_signed_url(self, filepath, expires_in=None):
        try:
            logger.info(
                f"Getting signed url for file {filepath} from bucket {self.bucket} in s3"
            )
            return self.s3.generate_presigned_url(
                "get_object",
                Params={"Bucket": self.bucket, "Key": filepath},
                ExpiresIn=expires_in,
            )
        except Exception as e:
            message = f"Unable to get signed url for file {filepath} to bucket {self.bucket} due to: {str(e)}"
            logger.exception(message)
            raise S3Error(message)

    def delete_file(self, filepath):
        try:
            logger.info(f"Deleting file {filepath} from bucket {self.bucket} in s3")
            self.s3.delete_object(Bucket=self.bucket, Key=filepath)
        except Exception as e:
            message = f"Unable to delete file {filepath} to bucket {self.bucket} due to: {str(e)}"
            logger.exception(message)
            raise S3Error(message)
