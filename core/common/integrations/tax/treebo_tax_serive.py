from decimal import Decimal

from core.common.data_classes.charge import Charge
from core.common.data_classes.customer import Customer
from core.common.data_classes.tax_breakup import TaxBreakup
from core.hotel.data_classes.room import Room
from core.hotel_invoice.data_classes.line_item import LineItem


class TreeboTax:
    def create_hotel_invoice_line_item_json(self, hotel_invoice):
        line_items = []
        for line_item in hotel_invoice.line_items:
            data = {
                "charge": self.create_charge_json(line_item.charge),
                "customer": self.create_customer_json(line_item.customers),
                "room": self.create_room_json(line_item.room),
            }
            line_items.append(data)
        return line_items

    def create_hotel_credit_note_line_item_json(
        self, hotel_credit_note, customer_invoice_id_to_hotel_invoice_id_mapping
    ):
        line_items = []
        for line_item in hotel_credit_note.line_items:
            data = {
                "charge": self.create_charge_json(line_item.charge),
                "customer": [],
                "room": dict(
                    number=line_item.room.number,
                    room_type_name=line_item.room.room_type_name,
                )
                if line_item.room
                else None,
                "original_invoice_id": customer_invoice_id_to_hotel_invoice_id_mapping.get(
                    line_item.original_invoice_id
                ),
            }
            line_items.append(data)
        return line_items

    def create_charge_json(self, charge):
        data = {
            "uid": charge.uid,
            "name": charge.name,
            "quantity": charge.quantity,
            "applicable_date": charge.applicable_date,
            "pre_tax": charge.pre_tax,
            "tax": charge.tax,
            "tax_breakup": self.create_tax_breakup_json(charge.tax_breakup),
            "hsn_code": charge.hsn_code,
            "smart_description": charge.smart_description,
            "tax_code": charge.tax_code,
        }
        return data

    def create_tax_breakup_json(self, tax_breakup):
        result = []
        for tax in tax_breakup:
            breakup = {"code": tax.code, "amount": tax.amount, "percent": tax.percent}
            result.append(breakup)
        return result

    def create_customer_json(self, customers):
        result = []
        for customer in customers:
            data = {
                "uid": customer.uid,
                "name": customer.name,
                "email": customer.email,
                "phone_number": customer.phone_number,
            }
            result.append(data)
        return result

    def create_room_json(self, room):
        data = {"number": room.number, "room_type_name": room.room_type_name}
        return data

    def create_tax_breakup(self, charge):
        result = []
        for tax_breakup in charge["tax_breakup"]:
            data = TaxBreakup(
                code=tax_breakup["code"],
                amount=tax_breakup["amount"],
                percent=tax_breakup["percent"],
            )

            result.append(data)
        return result

    def create_charge(self, charge):
        data = Charge(
            uid=charge["uid"],
            name=charge["name"],
            quantity=charge["quantity"],
            applicable_date=charge["applicable_date"],
            pre_tax=charge["pre_tax"],
            tax=charge["tax"],
            tax_breakup=self.create_tax_breakup(charge),
            hsn_code=charge["hsn_code"],
            smart_description=charge["smart_description"],
            tax_code=charge.get("tax_code", ""),
        )
        return data

    def create_room(self, room):
        room = Room(number=room["number"], room_type_name=room["room_type_name"])
        return room

    def create_customer(self, customers):
        result = []
        for customer in customers:
            data = Customer(
                uid=customer["uid"],
                name=customer["name"],
                email=customer["email"],
                phone_number=customer["phone_number"],
            )
            result.append(data)
        return result

    def create_line_items_object(self, new_line_items):
        line_items = []
        for new_line_item in new_line_items:
            charge = self.create_charge(new_line_item["charge"])
            room = (
                self.create_room(new_line_item["room"])
                if new_line_item["room"]
                else Room(number="", room_type_name="")
            )
            customers = (
                self.create_customer(new_line_item["customer"])
                if new_line_item["customer"]
                else []
            )
            original_invoice_id = new_line_item.get("original_invoice_id")
            line_item = LineItem(
                charge=charge,
                room=room,
                customers=customers,
                original_invoice_id=original_invoice_id,
            )
            line_items.append(line_item)
        return line_items

    @staticmethod
    def map_response_data_with_line_item_json(line_items_json, response_data):
        for line_item in line_items_json:
            charges = line_item["charge"]
            for response in response_data["skus"]:
                if response["index"] == charges["sku_index"]:
                    for new_charge in response["prices"]:
                        if new_charge["index"] == charges["price_index"]:
                            charges.update({"pretax_price": new_charge["pretax_price"]})
                            charges.update(
                                {"posttax_price": new_charge["posttax_price"]}
                            )
                            result = []
                            for tax in new_charge["tax_breakup"]:
                                breakup = {
                                    "code": tax["tax_code"].upper(),
                                    "amount": Decimal(tax["tax_amount"])
                                    if charges["tax"] >= 0
                                    else -Decimal(tax["tax_amount"]),
                                    "percent": tax["tax_value"],
                                }
                                result.append(breakup)
                            charges.update({"tax_breakup": result})
                            charges.update(
                                {
                                    "tax": Decimal(new_charge["tax_amount"])
                                    if charges["tax"] >= 0
                                    else (-Decimal(new_charge["tax_amount"]))
                                }
                            )
                            response_data["skus"].remove(response)
                break
        return line_items_json
