# pylint: disable=invalid-name
class HsnCodes:
    RoomRent = "996311"
    Food = "996331"
    Food2 = "996332"
    Laundry = "9997"
    Taxi = "996601"
    AlcoholicBeverage = "2207"
    Banquet = "996334"
    OtherAccommodationServices = "996329"


class InvoiceStatus:
    INITIATED = "initiated"
    GENERATED = "generated"
    LOCKED = "locked"
    CANCELLED = "cancelled"


class CreditNoteStatus:
    INITIATED = "initiated"
    GENERATED = "generated"
    LOCKED = "locked"
    CANCELLED = "cancelled"


class PmsType:
    HX = "hx"
    CRS = "crs"


class InvoiceSource:
    MANUAL = "manual"
    CRS = "crs"
    MANUAL_RESYNC = "manual_resync"


TREEBO_LEGAL_NAME = "Treebo Hospitality Ventures Private Limited (formerly known as Ruptub Solutions Private Limited)"


class IssuedToTypes:
    RESELLER = "reseller"
    CUSTOMER = "customer"


class IssuedByTypes:
    RESELLER = "reseller"
    HOTEL = "hotel"


FLOOD_CESS_TAX = "kerala_flood_cess"


class IngestionStatus:
    FAILED = "failed"
    SUCCESS_ON_RETRY = "success_on_retry"
    FAILED_ON_RETRY = "failed_on_retry"


class InvoiceReportStatus:
    FAILED = "failed"
    SUCCESS_ON_RETRY = "success_on_retry"
    FAILED_ON_RETRY = "failed_on_retry"


class EntityType:
    CreditNote = "credit_note"
    Invoice = "invoice"


class PurchaseInvoiceReportStatus:
    NOT_APPLICABLE = "not_applicable"
    PENDING = "pending"
    GENERATED = "generated"
    FAILED = "failed"
    MANUAL_ACTION_NEEDED = "manual_action_needed"
