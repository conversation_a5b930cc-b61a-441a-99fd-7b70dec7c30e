from core.common.errors import IngestionErrors


class ServiceException(RuntimeError):
    pass


class NonSupportedChannelType(Exception):
    def __init__(self, booking_id, hotel_id):
        msg = f"Channel types not supported for the request {hotel_id}, {booking_id}"
        super(NonSupportedChannelType, self).__init__(msg)


class MultipleBillIdsForABooking(Exception):
    pass


class NoBillIdsForABooking(Exception):
    pass


class ResellerGenericException(Exception):
    error_code = "0001"
    message = "Something went wrong. Please contact escalations team"

    def __init__(self, description=None, extra_payload=None, message=None):
        self.description = description
        self.extra_payload = extra_payload
        if message is not None:
            self.message = message

    def __str__(self):
        return str(
            dict(
                error_code=self.error_code,
                message=self.message,
                description=self.description,
                extra_payload=self.extra_payload,
            )
        )

    def with_description(self, description):
        self.description = description
        return self


class ResellerIngestionException(ResellerGenericException):
    error = IngestionErrors.GENERIC_INGESTION_ERROR

    def __init__(
        self, error=None, description=None, extra_payload=None, format_dict=None
    ):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error.error_code
            self.message = (
                self.error.message.format(**format_dict)
                if format_dict
                else self.error.message
            )
        super().__init__(description=description, extra_payload=extra_payload)
