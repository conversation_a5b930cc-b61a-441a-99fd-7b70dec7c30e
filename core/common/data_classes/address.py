# -*- coding: utf-8 -*-
# pylint: disable=too-many-arguments
import dataclasses
from dataclasses import dataclass

from core.common.data_classes.base import BaseDataClass


@dataclass(repr=True, eq=True, unsafe_hash=True)
class Address(BaseDataClass):
    field1: str
    field2: str
    city: str
    state: str
    country: str
    pincode: str

    @classmethod
    def empty(cls):
        return cls("", "", "", "", "", "")

    def pretty_string(self):
        return f"{self.field1},{self.field2}\n {self.city}{self.state} - {self.pincode}\n {self.country}"

    def __str__(self):
        return self.pretty_string()

    def __bool__(self):
        return any([getattr(self, field.name) for field in dataclasses.fields(self)])

    __nonzero__ = __bool__
