import decimal
from dataclasses import InitVar, dataclass  # pylint: disable=wrong-import-order
from typing import Any

from bson import Decimal128

from core.common.utils.decimals import quantize_and_round_off


@dataclass(unsafe_hash=True)
class TaxBreakup:
    code: str
    amount: Any
    percent: int = None
    pre_tax_for_percent: InitVar[Any] = None

    def __post_init__(self, pre_tax_for_percent):
        if isinstance(self.amount, Decimal128):
            self.amount = self.amount.to_decimal()
        self.amount = decimal.Decimal(self.amount or 0)
        self.amount = quantize_and_round_off(self.amount)

        if self.percent is not None:
            if isinstance(self.percent, Decimal128):
                self.percent = self.percent.to_decimal()
            self.percent = decimal.Decimal(self.percent)
        elif pre_tax_for_percent is not None:
            pre_tax_for_percent = decimal.Decimal(pre_tax_for_percent)
            if pre_tax_for_percent == 0:
                self.percent = 0
            else:
                self.percent = self.amount / pre_tax_for_percent * 100
        else:
            raise RuntimeError("percent and pre_tax_for_percent can't be null")

        self.percent = quantize_and_round_off(self.percent, 1)
