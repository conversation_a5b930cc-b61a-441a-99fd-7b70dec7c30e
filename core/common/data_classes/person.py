# -*- coding: utf-8 -*-
import logging
import uuid
from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import Any

from core.common.data_classes.base import BaseDataClass

logger = logging.getLogger(__name__)


@dataclass
class Person(BaseDataClass):
    uid: str
    name: Any
    email: str
    phone_number: str

    def __post_init__(self):
        if not self.uid:
            self.uid = uuid.uuid4()

        # pylint: disable=attribute-defined-outside-init
        self.first_name, self.last_name = Person.get_name_breakup(self.name)

        self.name = f"{self.first_name} {self.last_name}"

    @staticmethod
    def get_name_breakup(name):
        """Takes name as string or an iterable. For eg: tuple('<PERSON>', '<PERSON>) or ['<PERSON>', '<PERSON>']"""
        if not name:
            name = "guest"

        if not isinstance(name, str):
            try:
                name = " ".join([str(char) for char in name if char])
            except TypeError:
                raise ValueError(
                    "name: {n} must be a string or iterable".format(n=name)
                )

        name = name.strip()
        name = name.split(" ", 1)

        if not name:
            raise RuntimeError(
                "Unable to split name: {n}. This should not happen.".format(n=name)
            )

        if len(name) > 1:
            fname, lname = name[0], name[1]
        else:
            fname, lname = name[0], ""

        return fname, lname
