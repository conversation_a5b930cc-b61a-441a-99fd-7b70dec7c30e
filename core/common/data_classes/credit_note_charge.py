# -*- coding: utf-8 -*-
import decimal
import logging
from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import Any, Sequence

from bson import Decimal128

from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.tax_breakup import TaxBreakup
from core.common.utils.date import (
    is_naive_datetime,
    maybe_convert_string_to_datetime,
    set_time_zone,
)
from core.common.utils.decimals import quantize_and_round_off

logger = logging.getLogger(__name__)


@dataclass(unsafe_hash=True, eq=True)
class Charge(BaseDataClass):
    uid: str
    name: str
    quantity: int
    applicable_date: Any
    pre_tax: Any
    tax: Any
    tax_breakup: Sequence[TaxBreakup]
    hsn_code: str = ""
    smart_description: str = ""
    tax_code: str = ""

    def __post_init__(self):
        self.applicable_date = maybe_convert_string_to_datetime(self.applicable_date)
        if is_naive_datetime(self.applicable_date):
            self.applicable_date = set_time_zone(self.applicable_date)

        if not isinstance(self.pre_tax, decimal.Decimal):
            if isinstance(self.pre_tax, Decimal128):
                self.pre_tax = self.pre_tax.to_decimal()
            else:
                self.pre_tax = decimal.Decimal(self.pre_tax)

        if not isinstance(self.tax, decimal.Decimal):
            if isinstance(self.tax, Decimal128):
                self.tax = self.tax.to_decimal()
            else:
                # since tax may come off as None sometimes we use or 0.
                self.tax = decimal.Decimal(self.tax or 0)

        self.pre_tax = quantize_and_round_off(self.pre_tax)
        self.tax = quantize_and_round_off(self.tax)

        self.tax_breakup = frozenset(self.tax_breakup)

    def get_tax_breakup_for_tax_code(self, tax_code):
        try:
            tax_breakups = [tx for tx in self.tax_breakup if tx.code == tax_code]

            if len(tax_breakups) == 1:
                return tax_breakups[0]

            # try to add the breakups with same tax code.
            logger.warning(
                f"More than one tax breakup with same tax code found for {self}"
            )
            total_amount = sum([breakup.amount for breakup in tax_breakups])
            tax_breakup = TaxBreakup(
                code=tax_code, amount=total_amount, pre_tax_for_percent=self.pre_tax
            )
            return tax_breakup

        except IndexError:
            logger.warning(f"Invalid tax code: {tax_code}")
            return TaxBreakup(code=tax_code, amount=0, percent=0)

    def sanitize_tax_codes(self):
        for tax in self.tax_breakup:
            # removes digits and spaces..Eg: CGST2 -> CGST
            tax.code = "".join([char for char in tax.code if not char.isdigit()])

    def __str__(self):
        return self.__repr__()
