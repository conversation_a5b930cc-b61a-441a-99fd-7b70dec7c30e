# -*- coding: utf-8 -*-
from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import ClassVar

from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.gst_details import GstDetails
from core.hotel.data_classes.account_details import AccountDetails


@dataclass
class AbstractHotel(BaseDataClass):
    uid: str
    name: str
    email: str
    phone_number: str
    gst_details: GstDetails
    legal_state_code: str
    navision_code: str
    msme_number: str = None
    has_lut: bool = False
    status: ClassVar[str] = ""

    legal_signature_url: str = ""
    account_details: AccountDetails = None

    def __repr__(self):
        return f"{self.name} - {self.uid}"
