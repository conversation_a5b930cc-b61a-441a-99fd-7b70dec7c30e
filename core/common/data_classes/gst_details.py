# -*- coding: utf-8 -*-
from dataclasses import dataclass

from core.common.data_classes.address import Address
from core.common.data_classes.base import BaseDataClass


@dataclass(eq=True, unsafe_hash=True)
class GstDetails(BaseDataClass):
    address: Address
    has_lut: bool = False
    is_sez: bool = False
    legal_name: str = ""
    gstin_number: str = ""

    @classmethod
    def empty(cls):
        address = Address.empty()
        return cls(address, "", "")
