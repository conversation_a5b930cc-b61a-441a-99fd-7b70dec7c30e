from typing import List

from core.common.api.dtos.tenant_config_dto import TenantConfigDto
from object_registry import register_instance
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)
from reseller.reporting.nav_purchase_report.external_client.core.base_client import (
    BaseExternalClient,
)


@register_instance()
class CatalogServiceClient(BaseExternalClient):
    page_map = {
        "get_tenant_configs": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/tenant-configs",
        ),
        "get_all_test_property_ids": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/get-test-properties",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_service_url(
            ServiceEndPointNames.CATALOG_SERVICE_URL
        )

    def get_tenant_configs(self, property_id=None) -> List[TenantConfigDto]:
        page_name = "get_tenant_configs"
        optional_url_params = dict(property_id=property_id) if property_id else None
        response = self.make_call(
            page_name=page_name, optional_url_params=optional_url_params
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            TenantConfigDto(
                config.get("config_name"),
                config.get("config_value"),
                config.get("value_type"),
            )
            for config in response.json_response
        ]

    def get_all_test_property_ids(self):
        page_name = "get_all_test_property_ids"
        response = self.make_call(page_name)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response.get("property_ids", [])
