# -*- coding: utf-8 -*-
from __future__ import division

import logging
import time

from flask import Flask, Response, jsonify, request
from flask_restful import Resource

from core.common.api.exception_handler import APIExceptionHandler

logger = logging.getLogger(__name__)


# pylint: disable=too-many-ancestors
class TreeboResponse(Response):
    def __init__(self, response, **kwargs):
        if "mimetype" not in kwargs and "contenttype" not in kwargs:
            if response.startswith("<?xml"):
                kwargs["mimetype"] = "application/xml"
        super().__init__(response, **kwargs)

    @classmethod
    def force_type(cls, response, environ=None):
        if isinstance(response, dict):
            response = jsonify(response)
        return super(TreeboResponse, cls).force_type(response, environ)


class TreeboExceptionHandler(APIExceptionHandler):
    @classmethod
    def make_response(cls, data, status_code, headers=None):
        """Returning tuple because _make_response of Treebo API Handler because
        it will do the same thing as APIExceptionhandler"""
        return data, status_code, headers


class TreeboBaseAPI(Resource):
    exception_handler = TreeboExceptionHandler
    response_class = TreeboResponse

    def dispatch_request(self, *args, **kwargs):
        stime = time.time()
        logger.info(
            f"Received API request: {request.url} {request.method} DATA:{request.data}"
        )
        dispatcher_response = super(TreeboBaseAPI, self).dispatch_request(
            *args, **kwargs
        )
        response = dispatcher_response
        response = self.make_response(response)
        etime = time.time()
        logger.info(
            f"API: {self.__class__.__name__}, response time: {etime - stime:.2f} seconds"
            f" with status code: {response.status_code}"
        )
        return response

    def make_response(self, response):
        return Flask.make_response(self, response)
