# -*- coding: utf-8 -*-
# from rest_framework.response import Response
import warnings

from flask import jsonify
from flask.helpers import make_response
from ths_common.utils.common_utils import make_jsonify_ready


class APIResponse:
    """
    base class for generating success and error responses
    helps keep the format consistent
    """

    @classmethod
    def _prep_error_response(
        cls, error_code, message, response_code
    ):  # pylint: disable=unused-argument
        """
        prepares a Response object in the standard format
        :param error_code: the error code to be returned to the user
        :param message: message explaining/accompanying the error
        :param response_code: HTTP response code
        :return: Response object
        """
        error_response = {
            "errors": [{"code": error_code, "message": message}],
        }
        return jsonify(error_response)

    @classmethod
    def error_response(cls, message, resp_code, error_code=10000):
        warnings.warn("Use exception handler instead", DeprecationWarning)
        return cls._prep_error_response(
            error_code=error_code, message=message, response_code=resp_code
        )

    @classmethod
    def _prep_success_response(cls, message, response_code):
        """
        vanilla Response object on success
        :param message: what message to embed as part of the response
        :param response_code: what http response code to use
        :return: Response object
        """
        response = {"status": response_code, "message": message}
        return jsonify(response)

    @classmethod
    def success_response(cls, message, resp_code):
        warnings.warn("Use exception handler instead", DeprecationWarning)
        return cls._prep_success_response(message=message, response_code=resp_code)

    @staticmethod
    def build(status_code, data=None, errors=None, meta=None, resource_version=None):
        if not meta:
            meta = dict()
        if data is None:
            data = dict()
        if not errors:
            errors = list()
        response = dict(data=data, errors=errors, meta=meta)
        if resource_version:
            response["resource_version"] = resource_version
        response = make_jsonify_ready(response)
        return make_response(jsonify(response), status_code)
