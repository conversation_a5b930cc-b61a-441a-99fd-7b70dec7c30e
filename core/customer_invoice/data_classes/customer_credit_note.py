from dataclasses import dataclass
from typing import Sequence  # pylint: disable=wrong-import-order

from core.common.constants import CreditNoteStatus, IssuedByTypes
from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.gst_details import GstDetails
from core.common.utils.date import maybe_convert_string_to_datetime
from core.customer_invoice.data_classes.line_item import LineItem


@dataclass(unsafe_hash=True)
class CustomerCreditNote(BaseDataClass):
    booking_id: str
    hotel_id: str
    pms_type: str

    credit_note_id: str
    credit_note_number: str
    reference_invoice_numbers: Sequence[str]

    gst_details: GstDetails
    line_items: Sequence[LineItem]

    status: str = CreditNoteStatus.INITIATED
    issued_by_type: str = (
        IssuedByTypes.RESELLER
    )  # reseller or customer, Assuming default value as hotel
    source: str = ""
    source_created_at: str = ""
    bill_id: str = None
    credit_note_date: str = None

    irn: str = None
    qr_code: str = None
    is_einvoice: bool = False

    def __post_init__(self):
        self.line_items = frozenset(self.line_items)
        self.source_created_at = maybe_convert_string_to_datetime(
            self.source_created_at
        )

    def get_linked_invoice_ids(self):
        return [line_item.original_invoice_id for line_item in self.line_items or []]
