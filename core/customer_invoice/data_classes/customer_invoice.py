from dataclasses import dataclass
from typing import Sequence  # pylint: disable=wrong-import-order

from core.common.constants import InvoiceStatus, IssuedByTypes
from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.gst_details import GstDetails
from core.common.utils.date import maybe_convert_string_to_datetime
from core.customer_invoice.data_classes.line_item import LineItem
from core.customer_invoice.data_classes.payment import Payment


@dataclass(unsafe_hash=True)
class CustomerInvoice(BaseDataClass):
    booking_id: str
    hotel_id: str
    pms_type: str

    invoice_id: str
    invoice_number: str

    gst_details: GstDetails
    line_items: Sequence[LineItem]
    payments: Sequence[Payment]

    status: str = InvoiceStatus.INITIATED
    source: str = ""
    source_created_at: str = ""
    bill_id: str = None
    invoice_date: str = None
    check_in: str = None
    check_out: str = None
    irn: str = None
    qr_code: str = None
    is_einvoice: bool = False
    is_locked_in_crs: bool = False

    issued_by_type: str = (
        IssuedByTypes.RESELLER
    )  # treebo or customer, Assuming default value as hotel
    # for backward compatibility

    btt_legal_names = {
        "TREEBO OFFICIAL",
        "RUPTUB SOLUTIONS",
        "TREEBO PARTNER REDEMPTIONS",
        "TREEBO FOT",
        "BTT - MARKETING",
        "BTT - FREE GUEST STAY - CORP PROMOTIONAL RNS",
    }

    def __post_init__(self):
        self.line_items = tuple(self.line_items)
        self.source_created_at = maybe_convert_string_to_datetime(
            self.source_created_at
        )

    def is_btt_invoice(self):
        return any(
            btt_keyword.lower() in self.gst_details.legal_name.lower()
            for btt_keyword in self.btt_legal_names
        )
