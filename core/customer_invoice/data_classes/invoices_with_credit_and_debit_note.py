from dataclasses import dataclass
from typing import Sequence

from core.common.data_classes.base import BaseDataClass
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice


@dataclass
class InvoicesWithCreditAndDebitNote(BaseDataClass):
    credit_notes: Sequence[CustomerCreditNote]
    invoices: Sequence[CustomerInvoice]
