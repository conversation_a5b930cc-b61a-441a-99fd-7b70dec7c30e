from dataclasses import dataclass
from typing import Optional, Sequence

from core.common.data_classes.base import BaseDataClass
from core.common.data_classes.charge import Charge
from core.common.data_classes.customer import Customer
from core.hotel.data_classes.room import Room


@dataclass(eq=True, unsafe_hash=True)
class LineItem(BaseDataClass):
    charge: Charge
    room_id: str
    guest_stay_id: str
    room: Room = None
    original_invoice_id: Optional[str] = None
    customers: Sequence[Customer] = frozenset()

    def __post_init__(self):
        self.customers = frozenset(self.customers)
