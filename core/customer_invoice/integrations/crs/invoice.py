import logging
from collections import defaultdict

from core.booking.data_classes.booking import Booking
from core.common.constants import HsnCodes, InvoiceStatus, PmsType
from core.common.data_classes.address import Address
from core.common.data_classes.charge import Charge
from core.common.data_classes.customer import Customer
from core.common.data_classes.gst_details import GstDetails
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice
from core.customer_invoice.data_classes.line_item import LineItem
from core.hotel.data_classes.hotel import Hotel
from core.hotel.data_classes.room import Room
from core.hotel_invoice.services.smart_charge_description import SmartChargeDescription
from integrations.crs.treebo_crs.invoice.get_invoice import CRSGetInvoice
from integrations.crs.treebo_crs.invoice.get_invoices import CRSGetInvoices

logger = logging.getLogger(__name__)


class Invoice:
    pms_type = PmsType.CRS

    @classmethod
    def get_invoice(
        cls, booking: Booking, invoice_id, hotel: Hotel, thsc_invoice_data=None
    ):
        thsc_invoice = thsc_invoice_data or CRSGetInvoice(invoice_id=invoice_id).get()
        logger.info(
            f"Received following invoice response {thsc_invoice} from {invoice_id}"
        )
        return cls.convert_to_dataclass(booking, hotel, thsc_invoice)

    @classmethod
    def get_invoices(cls, booking: Booking, hotel: Hotel):
        thsc_invoices = CRSGetInvoices(booking_id=booking.booking_id).get()
        invoice_ids = [invoice.invoice_id for invoice in thsc_invoices]
        logger.info(
            f'Got invoices from booking with invoice_ids {",".join(invoice_ids)}'
        )
        return [
            Invoice.get_invoice(
                booking=booking, invoice_id=invoice.invoice_id, hotel=hotel
            )
            for invoice in thsc_invoices
        ]

    @classmethod
    def convert_to_dataclass(cls, booking, hotel, invoice_data):
        def convert_gst_details(gst_details):
            if not gst_details.get("address"):
                address = Address.empty()
            else:
                address = Address(
                    field1=gst_details["address"]["field_1"],
                    field2=gst_details["address"]["field_2"],
                    city=gst_details["address"]["city"],
                    state=gst_details["address"]["state"],
                    pincode=gst_details["address"]["pincode"],
                    country=gst_details["address"]["country"],
                )
            return GstDetails(
                gstin_number=gst_details["gstin_num"],
                legal_name=gst_details["name"],
                address=address,
                has_lut=gst_details["has_lut"],
                is_sez=gst_details["is_sez"],
            )

        def convert_line_items(invoice_charges, guests):
            grouped_invoice_charges_by_rate_plan = defaultdict(list)
            non_rate_plan_charges = []
            for invoice_charge in invoice_charges:
                if invoice_charge.get("charge_item").get("details").get(
                    "rate_plan_code"
                ) and invoice_charge.get("charge_item").get("details").get(
                    "rate_plan_reference_id"
                ):
                    grouped_invoice_charges_by_rate_plan.setdefault(
                        (
                            invoice_charge.get("applicable_date")[:10],
                            invoice_charge.get("charge_item")
                            .get("details")
                            .get("rate_plan_code"),
                            invoice_charge.get("charge_item")
                            .get("details")
                            .get("rate_plan_reference_id"),
                            invoice_charge.get("charge_item")
                            .get("details")
                            .get("room_no"),
                            invoice_charge.get("charge_item")
                            .get("details")
                            .get("room_type_code"),
                        ),
                        [],
                    ).append(invoice_charge)
                else:
                    non_rate_plan_charges.append(invoice_charge)

            for value in grouped_invoice_charges_by_rate_plan.values():
                if len(value) > 1:
                    grouped_charges_by_hsn = defaultdict(list)
                    for charge in value:
                        grouped_charges_by_hsn.setdefault(
                            charge["charge_item"]["item_code"]["value"], []
                        ).append(charge)
                    if HsnCodes.RoomRent in grouped_charges_by_hsn.keys():
                        logger.info(f"Clubbing inclusion charges: {value}")
                        non_rate_plan_charges.append(
                            cls._merge_charges(grouped_charges_by_hsn)
                        )
                    else:
                        logger.info(
                            f"Charges will not be clubbed as HSN: {HsnCodes.RoomRent} is missing from charges: "
                            f"{value}"
                        )
                        non_rate_plan_charges.extend(value)
                else:
                    non_rate_plan_charges.append(value[0])

            line_items = []
            for invoice_charge in non_rate_plan_charges:
                hsn_code = (
                    invoice_charge["charge_item"]["item_code"]["value"]
                    if invoice_charge["charge_item"]["item_code"].get("code_type")
                    == "HSN"
                    else ""
                )

                pre_tax = invoice_charge["pretax_amount"]

                tax_breakups = [
                    TaxBreakup(
                        tax_detail["tax_type"].upper(),
                        tax_detail["amount"],
                        percent=tax_detail["percentage"],
                    )
                    for tax_detail in invoice_charge["tax_details"]
                ]

                charge = Charge(
                    uid=invoice_charge.get("charge_id")
                    or hsn_code,  # hsn_code to be removed from here
                    name=invoice_charge["charge_item"]["name"],
                    quantity=1,
                    applicable_date=invoice_charge["applicable_date"],
                    pre_tax=pre_tax,
                    tax=round(
                        float(invoice_charge["posttax_amount"])
                        - float(invoice_charge["pretax_amount"]),
                        2,
                    ),
                    tax_breakup=tax_breakups,
                    hsn_code=hsn_code,
                    smart_description=SmartChargeDescription.get_smart_description(
                        hsn_code, invoice_charge["charge_item"]["name"]
                    ),
                    tax_code=invoice_charge["charge_item"]["sku_category_id"],
                )
                line_item = LineItem(
                    charge=charge,
                    room_id="",
                    guest_stay_id="",
                    room=_get_room_info(invoice_charge["charge_item"]["details"]),
                    customers=_get_guest_info(guests),
                )
                line_items.append(line_item)
            return line_items

        def _get_room_info(charge_item_room_info):
            return Room(
                number=charge_item_room_info["room_no"],
                room_type_name=charge_item_room_info["room_type"],
            )

        def _get_guest_info(guests):
            return [
                Customer(
                    uid=guest["customer_id"],
                    name=guest["name"]["full_name"],
                    email="",
                    phone_number="",
                )
                for guest in guests
            ]

        def _status(status):
            if status == "cancelled":
                return InvoiceStatus.CANCELLED
            if status == "preview":
                return InvoiceStatus.GENERATED
            return InvoiceStatus.LOCKED

        parent_info = invoice_data["parent_info"]
        raw_status = invoice_data["status"]
        updated_status = _status(raw_status)

        return CustomerInvoice(
            booking_id=booking.booking_id,
            hotel_id=hotel.uid,
            pms_type=cls.pms_type,
            invoice_id=invoice_data["invoice_id"],
            invoice_number=invoice_data["invoice_number"],
            gst_details=convert_gst_details(invoice_data["bill_to"]),
            line_items=convert_line_items(
                invoice_data["invoice_charges"], invoice_data["allowed_bill_tos"]
            ),
            status=updated_status,
            source_created_at=invoice_data["invoice_date"],
            payments=(),
            issued_by_type=invoice_data["issued_by_type"],
            bill_id=invoice_data["bill_id"],
            invoice_date=invoice_data.get("invoice_date"),
            check_in=parent_info.get("actual_checkin_date"),
            check_out=parent_info.get("actual_checkout_date"),
            irn=invoice_data.get("irn"),
            qr_code=invoice_data.get("qr_code"),
            is_einvoice=invoice_data.get("is_einvoice"),
            is_locked_in_crs=(updated_status == InvoiceStatus.LOCKED),
        )

    @classmethod
    def _merge_charges(cls, grouped_charges_by_hsn):
        if len(grouped_charges_by_hsn.get(HsnCodes.RoomRent)) > 1:
            merged_charges = grouped_charges_by_hsn.pop(HsnCodes.RoomRent)
            merged_charge = merged_charges[0]
            for charge in merged_charges[1:]:
                merged_charge["pretax_amount"] = str(
                    float(merged_charge.get("pretax_amount"))
                    + float(charge.get("pretax_amount"))
                )
                merged_charge["posttax_amount"] = str(
                    float(merged_charge.get("posttax_amount"))
                    + float(charge.get("posttax_amount"))
                )
                merged_charge["tax_details"] = cls._merge_tax_details(
                    charge.get("tax_details"), merged_charge.get("tax_details")
                )
        else:
            merged_charge = grouped_charges_by_hsn.pop(HsnCodes.RoomRent)[0]
        for invoice_charge in grouped_charges_by_hsn.values():
            for charge in invoice_charge:
                merged_charge["pretax_amount"] = str(
                    float(merged_charge.get("pretax_amount"))
                    + float(charge.get("pretax_amount"))
                )
                merged_charge["posttax_amount"] = str(
                    float(merged_charge.get("posttax_amount"))
                    + float(charge.get("posttax_amount"))
                )
                merged_charge["tax_details"] = cls._merge_tax_details(
                    charge.get("tax_details"), merged_charge.get("tax_details")
                )
        return merged_charge

    @staticmethod
    def _merge_tax_details(tax_details, merged_tax_details):
        grouped_tax_details = defaultdict(list)
        for tax_detail in tax_details + merged_tax_details:
            grouped_tax_details.setdefault(tax_detail.get("tax_type"), []).append(
                float(tax_detail.get("amount"))
            )
        for tax_type in grouped_tax_details.keys():
            for tax_detail in merged_tax_details:
                if tax_detail.get("tax_type") == tax_type:
                    tax_detail["amount"] = str(sum(grouped_tax_details.get(tax_type)))
        return merged_tax_details
