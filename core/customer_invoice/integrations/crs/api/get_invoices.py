from flask import jsonify, request
from flask.views import MethodView
from logger import logger
from marshmallow import ValidationError

from core.common.api.api_response import APIResponse
from core.common.constants import PmsType
from core.customer_invoice.integrations.crs.dto.invoices_request import (
    InvoicesRequestSchema,
)
from core.customer_invoice.integrations.crs.invoice import Invoice
from core.hotel.services.hotel import GetHotel
from reseller.services.booking.get_booking import GetBooking


class GetInvoicesAPI(MethodView):
    """
    calls getinvoices api on HX for a given booking-id and returns the json as received

    WARNING: FOR INTERNAL USE ONLY
    """

    def get(self):
        request_data = jsonify(request.args).json
        try:
            InvoicesRequestSchema().load(data=request_data)

            logger.info(
                "received {req} request for booking-id: {bid}".format(
                    bid=request_data["booking_id"], req=self.__class__.__name__
                )
            )

            booking_id = request_data["booking_id"]
            hotel_id = request_data["hotel_id"]

            hotel = GetHotel(hotel_id=hotel_id).get_hotel()

            booking = GetBooking(
                booking_id=booking_id, hotel=hotel, pms_type=PmsType.CRS
            ).from_pms()
            get_invoices_json = Invoice.get_invoices(booking=booking, hotel=hotel)

        except ValidationError as e:
            logger.error(f"Validation failed due to: {str(e)}")
            return APIResponse.error_response(message=str(e), resp_code=400)
        except Exception as e:
            msg = "error running getinvoices api on hx for group-booking-id: {bid}".format(
                bid=request_data["booking_id"]
            )
            logger.exception(msg)

            return APIResponse.error_response(message=msg, resp_code=500)

        return APIResponse.success_response(
            message=get_invoices_json.__dict__, resp_code=200
        )
