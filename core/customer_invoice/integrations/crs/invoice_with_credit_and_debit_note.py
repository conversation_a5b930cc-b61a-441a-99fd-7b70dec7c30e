import logging

from core.booking.data_classes.booking import Booking
from core.common.constants import PmsType
from core.common.exceptions import MultipleBillIdsForABooking
from core.customer_invoice.data_classes.invoices_with_credit_and_debit_note import (
    InvoicesWithCreditAndDebitNote as InvoicesWithCNandDN,
)
from core.customer_invoice.integrations.crs.credit_note import CreditNote
from core.customer_invoice.integrations.crs.invoice import Invoice
from core.hotel.data_classes.hotel import Hotel

logger = logging.getLogger(__name__)


class InvoicesWithCreditAndDebitNote:
    pms_type = PmsType.CRS

    @classmethod
    def get_invoices_with_credit_and_debit_note(cls, booking: Booking, hotel: Hotel):
        invoices = Invoice.get_invoices(booking, hotel)
        invoice_ids = [invoice.invoice_id for invoice in invoices]
        logger.info(f'Got invoices with invoice_ids {",".join(invoice_ids)}')
        credit_notes = []
        bill_ids = cls.distinct_bill_ids(invoices)
        if not bill_ids:
            logger.error(f"No bill id found for {booking.booking_id}")
        if len(bill_ids) > 1:
            logger.error(f"Multiple bill id found for {booking.booking_id}")
            raise MultipleBillIdsForABooking
        if len(bill_ids) == 1:
            credit_notes = CreditNote.get_credit_notes(booking, hotel, bill_ids[0])
        return cls.convert_to_invoices_with_credit_note_and_debit_note_dataclasses(
            invoices=invoices, credit_notes=credit_notes
        )

    @classmethod
    def convert_to_invoices_with_credit_note_and_debit_note_dataclasses(
        cls, invoices, credit_notes  # pylint: disable=invalid-name
    ):
        invoices_with_cn_dn = InvoicesWithCNandDN(
            invoices=invoices, credit_notes=credit_notes
        )
        return invoices_with_cn_dn

    @classmethod
    def distinct_bill_ids(cls, invoices):
        bill_ids = [invoice.bill_id for invoice in invoices]
        return list(set(bill_ids))
