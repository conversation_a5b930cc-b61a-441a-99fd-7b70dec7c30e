import logging
from collections import defaultdict

from ths_common.value_objects import TaxDetail
from treebo_commons.money import Money
from treebo_commons.utils.dateutils import date_to_ymd_str

from core.booking.data_classes.booking import Booking
from core.common.constants import CreditNoteStatus, HsnCodes, PmsType
from core.common.data_classes.address import Address
from core.common.data_classes.charge import Charge
from core.common.data_classes.gst_details import GstDetails
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.customer_invoice.data_classes.line_item import LineItem
from core.hotel.data_classes.hotel import Hotel
from core.hotel.data_classes.room import Room
from core.hotel_invoice.services.smart_charge_description import SmartChargeDescription
from integrations.crs.treebo_crs.invoice.get_credit_note import CRSGetCreditNote
from integrations.crs.treebo_crs.invoice.get_credit_notes import CRSGetCreditNotes

logger = logging.getLogger(__name__)


class CreditNote:
    pms_type = PmsType.CRS

    @classmethod
    def get_credit_note(
        cls,
        booking: Booking,
        credit_note_id,
        hotel: Hotel,
        bill_id,
        thsc_credit_note_data=None,
    ):
        thsc_credit_note = (
            thsc_credit_note_data
            or CRSGetCreditNote(bill_id=bill_id, credit_note_id=credit_note_id).get()
        )
        return cls.convert_to_dataclass(booking, hotel, thsc_credit_note)

    @classmethod
    def get_credit_notes(cls, booking: Booking, hotel: Hotel, bill_id):
        thsc_credit_note = CRSGetCreditNotes(bill_id=bill_id).get()
        return [
            CreditNote.get_credit_note(
                booking=booking,
                credit_note_id=credit_note.credit_note_id,
                hotel=hotel,
                bill_id=bill_id,
            )
            for credit_note in thsc_credit_note
        ]

    @classmethod
    def convert_to_dataclass(cls, booking, hotel, thsc_credit_note):
        def convert_gst_details(gst_details):
            if not gst_details.address:
                address = Address.empty()
            else:
                address = Address(
                    field1=gst_details.address.field1,
                    field2=gst_details.address.field2,
                    city=gst_details.address.city,
                    state=gst_details.address.state,
                    pincode=gst_details.address.pincode,
                    country=gst_details.address.country,
                )
            return GstDetails(
                gstin_number=gst_details.gstin_num,
                legal_name=gst_details.name,
                address=address,
                has_lut=gst_details.has_lut,
                is_sez=gst_details.is_sez,
            )

        def build_tax_breakup(tax_details):
            tax_breakup = []
            for tax_detail in tax_details:
                tax = TaxBreakup(
                    code=tax_details[tax_detail].tax_type.upper(),
                    amount=-tax_details[tax_detail].amount.amount,
                    percent=tax_details[tax_detail].percentage,
                )
                tax_breakup.append(tax)
            return tax_breakup

        def convert_line_items(charge_items):
            grouped_charges_by_rate_plan = defaultdict(list)
            non_rate_plan_charges = []
            for charge in charge_items:
                if charge.charge_item_detail.get(
                    "rate_plan_code"
                ) and charge.charge_item_detail.get("rate_plan_reference_id"):
                    grouped_charges_by_rate_plan.setdefault(
                        (
                            charge.applicable_date.date(),
                            charge.charge_item_detail.get("rate_plan_code"),
                            charge.charge_item_detail.get("rate_plan_reference_id"),
                            charge.charge_item_detail.get("room_no"),
                            charge.charge_item_detail.get("room_type_code"),
                        ),
                        [],
                    ).append(charge)
                else:
                    non_rate_plan_charges.append(charge)

            for value in grouped_charges_by_rate_plan.values():
                if len(value) > 1:
                    grouped_charges_by_hsn = defaultdict(list)
                    for charge in value:
                        grouped_charges_by_hsn.setdefault(
                            charge.item_code.value, []
                        ).append(charge)

                    if HsnCodes.RoomRent in grouped_charges_by_hsn.keys():
                        logger.info(f"Clubbing inclusion charges: {value}")
                        non_rate_plan_charges.append(
                            cls._merge_charges(grouped_charges_by_hsn)
                        )
                    else:
                        non_rate_plan_charges.extend(value)
                        logger.info(
                            f"Charges will not be clubbed as HSN: {HsnCodes.RoomRent} is missing from charges: "
                            f"{value}"
                        )
                else:
                    non_rate_plan_charges.append(value[0])

            line_items = []
            for charge_item in non_rate_plan_charges:
                hsn_code = (
                    charge_item.item_code.value
                    if charge_item.item_code.code_type == "HSN"
                    else ""
                )
                charge = Charge(
                    uid=charge_item.credit_note_line_item_id
                    or charge_item.item_code.value,
                    # hsn_code to be removed from here
                    name=charge_item.item_name,
                    quantity=1,
                    applicable_date=charge_item.applicable_date,
                    pre_tax=-charge_item.pretax_amount.amount,
                    tax=-round(
                        float(charge_item.posttax_amount.amount)
                        - float(charge_item.pretax_amount.amount),
                        2,
                    ),
                    tax_breakup=build_tax_breakup(charge_item.tax_details),
                    hsn_code=hsn_code,
                    smart_description=SmartChargeDescription.get_smart_description(
                        hsn_code, charge_item.item_name
                    ),
                    tax_code=charge_item.sku_category_id,
                )
                line_item = LineItem(
                    charge=charge,
                    room_id="",
                    guest_stay_id="",
                    original_invoice_id=charge_item.invoice_id,
                    room=_get_room_info(charge_item.charge_item_detail),
                )
                line_items.append(line_item)
            return line_items

        def _get_room_info(charge_item_room_info):
            return Room(
                number=charge_item_room_info["room_no"],
                room_type_name=charge_item_room_info["room_type"],
            )

        def reference_invoice_numbers(invoice_details):
            return [invoice.invoice_number for invoice in invoice_details]

        def _status(status):
            if status == "cancelled":
                return CreditNoteStatus.CANCELLED
            if status == "preview":
                return CreditNoteStatus.GENERATED
            return CreditNoteStatus.LOCKED

        customer_credit_note = CustomerCreditNote(
            booking_id=booking.booking_id,
            hotel_id=hotel.uid,
            pms_type=cls.pms_type,
            credit_note_id=thsc_credit_note.credit_note.credit_note_id,
            credit_note_number=thsc_credit_note.credit_note.credit_note_number,
            gst_details=convert_gst_details(thsc_credit_note.credit_note.issued_to),
            line_items=convert_line_items(
                charge_items=thsc_credit_note.credit_note.credit_note_line_items
            ),
            status=_status(thsc_credit_note.credit_note.status),
            source_created_at=thsc_credit_note.credit_note.credit_note_date,
            reference_invoice_numbers=reference_invoice_numbers(
                thsc_credit_note.invoice_details
            ),
            issued_by_type=thsc_credit_note.credit_note.issued_by_type,
            bill_id=thsc_credit_note.credit_note.bill_id,
            credit_note_date=thsc_credit_note.credit_note.credit_note_date.strftime(
                "%Y-%m-%d"
            ),
        )
        return customer_credit_note

    @classmethod
    def _merge_charges(cls, grouped_charges_by_hsn):
        if len(grouped_charges_by_hsn.get(HsnCodes.RoomRent)) > 1:
            merged_charges = grouped_charges_by_hsn.pop(HsnCodes.RoomRent)
            merged_charge = merged_charges[0]
            for charge in merged_charges[1:]:
                merged_charge.pretax_amount = Money(
                    str(
                        float(merged_charge.pretax_amount.amount)
                        + float(charge.pretax_amount.amount)
                    )
                )
                merged_charge.posttax_amount = Money(
                    str(
                        float(merged_charge.posttax_amount.amount)
                        + float(charge.posttax_amount.amount)
                    )
                )
                merged_charge.tax_details = cls._merge_tax_details(
                    charge.tax_details, merged_charge.tax_details
                )
        else:
            merged_charge = grouped_charges_by_hsn.pop(HsnCodes.RoomRent)[0]
        for invoice_charge in grouped_charges_by_hsn.values():
            for charge in invoice_charge:
                merged_charge.pretax_amount = Money(
                    str(
                        float(merged_charge.pretax_amount.amount)
                        + float(charge.pretax_amount.amount)
                    )
                )
                merged_charge.posttax_amount = Money(
                    str(
                        float(merged_charge.posttax_amount.amount)
                        + float(charge.posttax_amount.amount)
                    )
                )
                merged_charge.tax_details = cls._merge_tax_details(
                    charge.tax_details, merged_charge.tax_details
                )
        return merged_charge

    @staticmethod
    def _merge_tax_details(tax_details, parent_tax_details):
        merged_tax_details = dict()
        for tax_type in parent_tax_details.keys():
            merged_tax_details[tax_type] = TaxDetail(
                amount=Money(
                    parent_tax_details.get(tax_type).amount
                    + tax_details.get(tax_type).amount
                ),
                tax_type=tax_type,
                percentage=parent_tax_details.get(tax_type).percentage,
            )
        return merged_tax_details
