from dataclasses import dataclass
from datetime import datetime


@dataclass
class RetryIngestionEvent:
    module_name: str
    resource_id: str
    resource_type: str
    hotel_id: str
    status: str
    eta: datetime

    def to_dict(self):
        return dict(
            module_name=self.module_name,
            resource_id=self.resource_id,
            resource_type=self.resource_type,
            hotel_id=self.hotel_id,
            status=self.status,
            eta=self.eta,
        )

    @staticmethod
    def from_json(owner_json):
        return RetryIngestionEvent(
            module_name=owner_json.get("module_name"),
            resource_id=owner_json.get("resource_id"),
            resource_type=owner_json.get("resource_type"),
            hotel_id=owner_json.get("hotel_id"),
            status=owner_json.get("status"),
            eta=owner_json.get("eta"),
        )
