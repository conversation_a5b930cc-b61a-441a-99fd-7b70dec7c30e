from dataclasses import dataclass


@dataclass
class IngestionLock:
    resource_type: str
    resource_id: str

    def to_dict(self):
        return dict(resource_type=self.resource_type, resource_id=self.resource_id)

    @staticmethod
    def from_json(owner_json):
        return IngestionLock(
            resource_type=owner_json.get("resource_type"),
            resource_id=owner_json.get("resource_id"),
        )
