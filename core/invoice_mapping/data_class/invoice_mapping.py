# -*- coding: utf-8 -*-
from dataclasses import dataclass

from core.common.data_classes.base import BaseDataClass


@dataclass(unsafe_hash=True, eq=True)
class InvoiceMapping(BaseDataClass):
    hotel_id: str
    pms_type: str
    booking_id: str

    customer_invoice_id: str
    customer_invoice_pi_id: str

    hotel_invoice_id: str
    hotel_invoice_number: str

    customer_invoice_pi_number: str = ""
    customer_invoice_number: str = ""
