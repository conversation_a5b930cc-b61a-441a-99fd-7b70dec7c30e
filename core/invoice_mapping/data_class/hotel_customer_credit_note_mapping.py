# -*- coding: utf-8 -*-
from dataclasses import dataclass

from core.common.data_classes.base import BaseDataClass


@dataclass(unsafe_hash=True, eq=True)
class HotelCustomerCreditNoteMapping(BaseDataClass):
    hotel_id: str
    pms_type: str
    booking_id: str

    hotel_credit_note_number: str
    hotel_credit_note_id: str
    customer_credit_note_id: str
    customer_credit_note_number: str = ""
