import abc


class NotificationHandlerInterface:
    __metaclass__ = abc.ABCMeta

    @abc.abstractmethod
    def __init__(self, *args, **kwargs):
        pass

    @abc.abstractmethod
    def email(self):
        pass

    @abc.abstractmethod
    def sms(self):
        pass

    @abc.abstractmethod
    def slack(self):
        pass

    def __str__(self):
        return str(self.__class__.__name__)

    def __repr__(self):
        return self.__str__()
