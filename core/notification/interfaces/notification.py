import abc

from reseller import app


class EmailNotificationInterface:
    __metaclass__ = abc.ABCMeta

    provider_options = {
        # to be used as a hook for providers custom options
    }

    def __init__(self, item):
        self.item = item

        env = app.config.get("ENV")
        if env != "production":
            item.mangle_emails()
            item.mangle_subject(env)
            item.to_list = item.to_list + app.config.get("DEVELOPERS_EMAILS", [])

        self.provider_options.update(**item.provider_options)

    @abc.abstractmethod
    def send(self, fail_silently=False, **kwargs):
        pass


class SmsNotificationInterface:
    __metaclass__ = abc.ABCMeta

    provider_options = {
        # to be used as a hook for providers custom options
    }

    def __init__(self, item):
        self.item = item

    @abc.abstractmethod
    def send(self, fail_silently=False, **kwargs):
        pass


class SlackNotificationInterface:
    __metaclass__ = abc.ABCMeta

    provider_options = {
        # to be used as a hook for providers custom options
    }

    def __init__(self, item):
        self.item = item

    @abc.abstractmethod
    def send(self, fail_silently=False, **kwargs):
        pass
