import logging

from marshmallow import ValidationError, fields

from .base import BaseNotifyItem

logger = logging.getLogger(__name__)


# pylint: disable=too-many-instance-attributes
class Email(BaseNotifyItem):
    def __init__(self, subject, to_list, cc_list=None, bcc_list=None, **options):
        self.subject = subject

        self._to_list = []
        self._cc_list = []
        self._bcc_list = []
        self.options = options

        self.to_list = to_list
        self.cc_list = cc_list
        self.bcc_list = bcc_list
        self.text = ""
        self.html = ""

        self.attachments = []

    @property
    def to_list(self):
        return [eml for eml in self._to_list if self.is_valid_email(eml)]

    @property
    def cc_list(self):
        return [eml for eml in self._cc_list if self.is_valid_email(eml)]

    @property
    def bcc_list(self):
        return [eml for eml in self._bcc_list if self.is_valid_email(eml)]

    @to_list.setter
    def to_list(self, value):
        if value is None:
            return
        self._to_list = set(value)

    @cc_list.setter
    def cc_list(self, value):
        if value is None:
            return

        value = set(email for email in value if email not in self.to_list)
        self._cc_list = set(value)

    @bcc_list.setter
    def bcc_list(self, value):
        if value is None:
            return

        value = set(
            email
            for email in value
            if (email not in self.to_list and email not in self.cc_list)
        )
        self._bcc_list = set(value)

    def mangle_emails(self):
        self.to_list = self.mangle_email(self.to_list)
        self.cc_list = self.mangle_email(self.cc_list)
        self.bcc_list = self.mangle_email(self.bcc_list)

    def mangle_subject(self, env):
        self.subject = "[{e}] {s}".format(e=env, s=self.subject)

    @classmethod
    def is_valid_email(cls, email):
        try:
            fields.Email(email)
            return True
        except ValidationError:
            logger.warning("Discarding invalid email: {e}".format(e=email))

    @classmethod
    def mangle_email(cls, email_addresses):
        return ["{e}.mangled.com".format(e=email) for email in email_addresses]
