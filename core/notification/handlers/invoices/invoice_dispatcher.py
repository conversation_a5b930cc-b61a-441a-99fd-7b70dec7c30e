from core.notification import Email
from core.notification.interfaces.handlers import NotificationHandlerInterface
from core.notification.providers.email import TreeboEmailNotify
from core.notification.providers.templating.flask import FlaskTemplating


class InvoiceDispactherHandler(NotificationHandlerInterface):
    email_template = ""

    def __init__(self, file_url, hotel_name, hotel_owner_emails, date):
        self.file_url = file_url
        self.hotel_name = hotel_name
        self.hotel_owner_emails = hotel_owner_emails
        self.date = date
        super(InvoiceDispactherHandler, self).__init__(
            file_url, hotel_name, hotel_owner_emails, date
        )

    def email(self):
        subject = "{hotel_name} - Invoices for {date}".format(
            date=self.date, hotel_name=self.hotel_name
        )

        to_list = self.hotel_owner_emails

        cc_list = ["<EMAIL>", "<EMAIL>"]

        email = Email(subject=subject, to_list=to_list, cc_list=cc_list)

        email.provider_options["priority"] = TreeboEmailNotify.normal_priority

        template = FlaskTemplating(
            template_name="invoice_dispatch.html",
            data=dict(url=self.file_url, date=self.date),
        )

        email.html = template.render()

        return email

    def sms(self):
        raise NotImplementedError

    def slack(self):
        raise NotImplementedError
