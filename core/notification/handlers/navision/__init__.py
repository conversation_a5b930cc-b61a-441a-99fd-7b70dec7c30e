import logging

from core.notification import Em<PERSON>, Slack, notify
from core.notification.constants import Slack as SlackConstants
from core.notification.providers.email import TreeboEmailNotify
from reseller import app

logger = logging.getLogger(__name__)


class NavNotificationEmailIds:
    NOREPLY = "<EMAIL>"


def send_navision_error_report(msg, date):
    try:
        send_email_notification(msg, date)
        send_slack_notification(
            "Navision Purchase report push has failed with error {0}".format(msg)
        )
    except Exception as e:
        logger.exception(str(e))
        send_slack_notification(
            "Navision Purchase report push has failed with error {0}".format(str(e))
        )


def send_slack_notification(msg):
    slack = Slack(hook_url=SlackConstants.NAV_ALERTS_SLACK_WEBHOOK_URL, message=msg)
    notify(slack)


def send_email_notification(msg, date):
    subject = (
        "Navision Push Error report for date {0}".format(date if date else "N/A")
        if date
        else "Navision Push Error report for records"
    )
    to_list = app.config["NAVISION_ERROR_REPORT_RECEIVER_LIST"]
    email = Email(
        subject=subject,
        to_list=to_list,
        sender=NavNotificationEmailIds.NOREPLY,
        sender_name="noreply",
    )
    email.html = "Hi,<br/><br/>{0}".format(msg)
    email.provider_options["priority"] = TreeboEmailNotify.normal_priority
    tracking_id = notify(email)
    logger.info(f"Tracking id  email is {tracking_id}, on date: {date}")
