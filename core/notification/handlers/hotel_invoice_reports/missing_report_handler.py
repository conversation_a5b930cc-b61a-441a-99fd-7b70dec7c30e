from core.notification import Slack
from core.notification.constants import <PERSON>lack as SlackConstants
from core.notification.interfaces.handlers import NotificationHandlerInterface


class MissingReportHandler(NotificationHandlerInterface):
    def __init__(self, message):
        self.failure_message = message
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(message)

    def email(self):
        raise NotImplementedError

    def sms(self):
        raise NotImplementedError

    def slack(self):
        return Slack(
            hook_url=SlackConstants.B2B_APP_ALERTS, message=self.failure_message
        )
