from core.notification import Email
from core.notification.interfaces.handlers import NotificationHandlerInterface
from core.notification.providers.email import TreeboEmailNotify


class HotelInvoiceMonthlyReportDispatcher(NotificationHandlerInterface):
    def __init__(
        self, hotel_owner_emails, hotel_name, hotel_city, report_date, csv_file_url
    ):
        self.csv_file_url = csv_file_url
        self.hotel_owner_emails = hotel_owner_emails
        self.hotel_city = hotel_city
        self.hotel_name = hotel_name
        self.report_date = report_date
        super(HotelInvoiceMonthlyReportDispatcher, self).__init__(
            hotel_owner_emails, hotel_name, hotel_city, report_date, csv_file_url
        )

    def email(self):
        subject = f"Monthly Invoice Report for {self.hotel_name}, {self.hotel_city}: {self.report_date}"
        to_list = self.hotel_owner_emails
        cc_list = ["<EMAIL>", "<EMAIL>"]
        email = Email(subject=subject, to_list=to_list, cc_list=cc_list)
        email.provider_options["priority"] = TreeboEmailNotify.normal_priority
        email.html = (
            f"Dear Partner,<br><br>Please find attached the monthly sales invoice report.<br><br>"
            f"*Kindly note that it is mandatory for the hotel to use the invoice details as mentioned in "
            f"this report for the GST return filings.*<br><br>*All corporate invoices with corporate GSTIN "
            f"(whether from same state as hotel or different state) are to be shown in B2B business and not "
            f"B2C.*<br><br>*Use of any other invoicing system or invoice numbers or not putting proper "
            f"GSTIN or improper tax amounts in return filings will result in input tax credit loss to "
            f"Treebo.*<br><br>Regards,<br>Treebo Team"
        )
        email.attachments = [
            {
                "filename": f"monthly_report_{self.report_date}.csv",
                "url": self.csv_file_url,
            }
        ]
        return email

    def sms(self):
        raise NotImplementedError

    def slack(self):
        raise NotImplementedError
