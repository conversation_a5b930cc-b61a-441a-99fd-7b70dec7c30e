import logging

import requests

from core.notification.interfaces.notification import EmailNotificationInterface
from reseller import app
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

logger = logging.getLogger(__name__)


def get_sender_and_name_from_server_email(email_string_with_name):
    """
    :param email_string_with_name: looks like "B2B Admin  <<EMAIL>>"
    :return: "B2B Admin", "<EMAIL>"
    """
    name, email = email_string_with_name.split("<")
    name = name.strip()
    email = email.replace(">", "")
    return name, email


class TreeboEmailNotify(EmailNotificationInterface):
    normal_priority = 1
    high_priority = 2

    notification_host = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.NOTIFICATION_SERVICE_URL
    )
    email = app.config["SERVER_EMAIL"]
    url = "v1/notification/email/"
    provider_options = {
        "timeout": 30,
        "sender": get_sender_and_name_from_server_email(email)[1],
        "sender_name": get_sender_and_name_from_server_email(email)[0],
        "reply_to": email,
        "consumer": "b2b",
        "url": f"{notification_host}/{url}",
        "priority": normal_priority,
    }

    def send(self, fail_silently=False, **kwargs):
        try:
            response = requests.post(
                self.provider_options["url"],
                json=self._payload(),
                timeout=self.provider_options["timeout"],
            )

            response.raise_for_status()
            json_response = response.json()
            if not json_response["data"]["status"] == "success":
                raise RuntimeError(
                    "Notif response status {s} is not success".format(
                        s=json_response["data"]["status"]
                    )
                )
            tracking_id = json_response["data"]["data"]["notification_id"]
            return tracking_id
        except Exception as e:
            logger.exception(
                "Error sending email: {em} due to {e}".format(em=self.item, e=e)
            )
            if not fail_silently:
                raise

    def _payload(self):
        return {
            "data": self._data(),
            "attributes": {"message_priority": self.provider_options["priority"]},
        }

    def _data(self):
        return {
            "subject": self.item.subject,
            "body_text": self.item.text,
            "body_html": self.item.html,
            "sender": self.item.options.get("sender")
            or self.provider_options["sender"],
            "sender_name": self.item.options.get("sender_name")
            or self.provider_options["sender_name"],
            "reply_to": self.item.options.get("reply_to")
            or self.provider_options["reply_to"],
            "receivers": {
                "to": self.item.to_list,
                "cc": self.item.cc_list,
                "bcc": self.item.bcc_list,
            },
            "consumer": self.provider_options["consumer"],
            "attachments": self.item.attachments,
        }
