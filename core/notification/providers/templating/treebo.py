import json

import requests

from core.notification.interfaces.templating import TemplatingInterface


class TreeboTemplating(TemplatingInterface):
    provider_options = {
        "url": "https://vortex.treebo.com/api/v1/generate/",
        "timeout": 30,
    }

    def render(self):
        return self._request()

    def _request(self):
        data = self._data()
        headers = {"Content-type": "application/json"}
        response = requests.post(
            url=self.provider_options["url"],
            data=json.dumps(data),
            timeout=self.provider_options["timeout"],
            headers=headers,
        )
        response.raise_for_status()
        return response.text

    def _data(self):
        return {
            "template": self.template_name,
            "context": self.data,
        }
