import json
import logging

import requests

from core.notification.interfaces.notification import SlackNotificationInterface

logger = logging.getLogger(__name__)


class DefaultSlackNotify(SlackNotificationInterface):
    provider_options = {"timeout": 30}

    def send(self, fail_silently=False, **kwargs):

        try:
            response = requests.post(
                self.item.hook_url,
                data=json.dumps(self._payload),
                headers=self.item.headers,
                timeout=self.provider_options["timeout"],
            )
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.exception(e)
            if not fail_silently:
                raise

    @property
    def _payload(self):
        return {"text": self.item.message}
