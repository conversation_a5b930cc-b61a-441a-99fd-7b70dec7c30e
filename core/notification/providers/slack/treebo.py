import json
import logging

import requests
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from core.notification.interfaces.notification import SlackNotificationInterface
from reseller import app

logger = logging.getLogger(__name__)


class TreeboSlackNotify(SlackNotificationInterface):
    provider_options = {"timeout": 30}

    def send(self, fail_silently=False, **kwargs):

        try:

            env = app.config.get("ENV")
            if "production" not in env:
                logger.info("As Env is {e} no slack alerts".format(e=env))
                return None
            response = requests.post(
                self.item.hook_url,
                data=json.dumps(self._payload),
                headers=self.item.headers,
                timeout=self.provider_options["timeout"],
            )
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.exception(e)
            if not fail_silently:
                raise

    @property
    def _payload(self):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        return {"text": f"Tenant: {tenant_id} - {self.item.message}"}
