from typing import Type

from core.notification.data_classes.base import BaseNotifyItem
from reseller import app

from . import providers
from .data_classes import Email, Slack, Sms


class Notify:
    email_provider = getattr(
        app.config, "NOTIFY_EMAIL_BACKEND", providers.email.TreeboEmailNotify
    )
    sms_provider = getattr(app.config, "NOTIFY_SMS_BACKEND", None)
    slack_provider = getattr(
        app.config, "NOTIFY_SLACK_BACKEND", providers.slack.TreeboSlackNotify
    )

    def dispatch(self, item: Type[BaseNotifyItem], **kwargs):
        if isinstance(item, Email):
            tracking_id = self.email_provider(item).send(**kwargs)
        elif isinstance(item, Slack):
            tracking_id = self.slack_provider(item).send(**kwargs)
        elif isinstance(item, Sms):
            tracking_id = self.sms_provider(item).send(
                **kwargs
            )  # pylint: disable=not-callable
        else:
            raise RuntimeError(
                "Unsupported notify item received: {i}".format(i=repr(item))
            )
        return tracking_id


notify = Notify().dispatch  # pylint: disable=invalid-name
