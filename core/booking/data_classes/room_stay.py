from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import Sequence

from core.booking.data_classes.guest_stay import GuestStay


@dataclass
class RoomStay:
    room_booking_id: str
    type: str
    number: str
    status: str
    pms_id: str

    guests: Sequence[GuestStay]

    @property
    def checkin(self):
        return min(guest.actual_stay_start or guest.stay_start for guest in self.guests)

    @property
    def checkout(self):
        return max(guest.actual_stay_end or guest.stay_end for guest in self.guests)
