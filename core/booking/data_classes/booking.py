from dataclasses import dataclass  # pylint: disable=wrong-import-order
from typing import Sequence

from core.booking.data_classes.room_stay import RoomStay


@dataclass
class Booking:
    booking_id: str
    hotel_id: str
    pms_type: str
    pms_id: str
    source_created_date: str
    source: str
    sub_source: str
    owner: str
    rooms: Sequence[RoomStay]
    order_id: str
    status: str

    @property
    def checkin(self):
        checkins = [li.checkin for li in self.rooms]
        return min(checkins)

    @property
    def checkout(self):
        checkouts = [li.checkout for li in self.rooms]
        return max(checkouts)
