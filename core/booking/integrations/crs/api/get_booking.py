import logging

from flask import jsonify, request
from flask.views import MethodView
from marshmallow import ValidationError

from core.booking.integrations.crs.booking import Booking
from core.booking.integrations.crs.dto.booking_request import BookingRequestSchema
from core.common.api.api_response import APIResponse
from core.hotel.services.hotel import GetHotel


class GetBookingAPI(MethodView):
    """
    calls getbooking api on HX for a given room_booking code and returns the json as received

    WARNING: FOR INTERNAL USE ONLY
    """

    def get(self):
        logger = logging.getLogger(self.__class__.__name__)
        request_data = jsonify(request.args).json

        try:
            import json

            request_data["pms_meta"] = json.loads(request_data["pms_meta"])
            BookingRequestSchema().load(data=request_data)

            booking_id = request_data["booking_id"]
            logger.info(
                "received {req} request for booking-id: {bid}".format(
                    bid=booking_id, req=self.__class__.__name__
                )
            )
            hotel = GetHotel(hotel_id=request_data["hotel_id"]).get_hotel()
            get_booking_json = Booking.get_booking(
                booking_id=booking_id,
                hotel=hotel,
            )

        except ValidationError as e:
            logger.error(f"Validation failed due to: {str(e)}")
            return APIResponse.error_response(message=str(e), resp_code=400)
        except Exception as e:
            msg = "error running get booking api on crs for booking-id: {bid}".format(
                bid=request_data.get("booking_id")
            )
            logger.exception(msg)

            return APIResponse.error_response(message=msg, resp_code=500)

        return APIResponse.success_response(message=get_booking_json, resp_code=200)
