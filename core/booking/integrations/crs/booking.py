import logging
from datetime import datetime

from core.booking.data_classes.booking import Booking as BookingDataClass
from core.booking.data_classes.guest_stay import GuestStay
from core.booking.data_classes.room_stay import RoomStay
from core.common.constants import PmsType
from integrations.crs.treebo_crs.booking.get_booking import TreeboCRSGetBooking

logger = logging.getLogger(__name__)


class Booking:
    pms_type = PmsType.CRS

    @classmethod
    def get_booking(cls, booking_id, hotel):
        treebo_crs_booking = TreeboCRSGetBooking(booking_id).get()
        return cls.crs_booking_to_dataclass(treebo_crs_booking, hotel)

    @classmethod
    def crs_booking_to_dataclass(cls, treebo_crs_booking, hotel):
        booking_id = treebo_crs_booking.booking_id
        pms_id = treebo_crs_booking.booking_id
        logger.info(f"Ingesting booking with bill_id {treebo_crs_booking.bill_id}")

        def booking_owner():
            owner = [
                customer
                for customer in treebo_crs_booking.customers
                if customer.customer_id == treebo_crs_booking.booking_owner_id
            ][0]
            return "".join(filter(None, (owner.first_name, owner.last_name)))

        def _convert_guest_data_class(guest):
            customer_id_for_guests = [
                {"first_name": customer.first_name, "last_name": customer.last_name}
                for customer in treebo_crs_booking.customers
                if guest.guest_id == customer.customer_id
            ]
            customer_id_for_guest = {}
            if customer_id_for_guests:
                customer_id_for_guest = customer_id_for_guests[0]

            return GuestStay(
                stay_id=guest.guest_stay_id,
                title="",
                first_name=customer_id_for_guest.get("first_name"),
                last_name=customer_id_for_guest.get("last_name"),
                stay_start=guest.checkin_date,
                stay_end=guest.checkout_date,
                actual_stay_start=guest.actual_checkin_date,
                actual_stay_end=guest.actual_checkout_date,
            )

        def _convert_rooms_data_class(room):
            guest_stays = [
                _convert_guest_data_class(guest_data) for guest_data in room.guests
            ]
            return RoomStay(
                room_booking_id=room.room_stay_id,
                type=room.type.value,
                number=room.room_id,
                status=room.status.value,
                guests=guest_stays,
                pms_id=room.room_stay_id,
            )

        booking = BookingDataClass(
            booking_id=booking_id,
            source_created_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            owner=booking_owner(),
            hotel_id=hotel.uid,
            pms_type=cls.pms_type,
            pms_id=pms_id,
            rooms=[
                _convert_rooms_data_class(booking_data)
                for booking_data in treebo_crs_booking.rooms
            ],
            order_id=treebo_crs_booking.reference_number,
            status=treebo_crs_booking.status.value,
            source=treebo_crs_booking.source.channel_code,
            sub_source=treebo_crs_booking.source.subchannel_code,
        )
        return booking

    @classmethod
    def get_booking_id_from_bill_id(cls, bill_id):
        treebo_crs_booking = TreeboCRSGetBooking.get_from_bill_id(bill_id)
        logger.info(f"treebo crs booking response payload: {treebo_crs_booking}")
        return treebo_crs_booking.booking_id

    @classmethod
    def get_booking_from_bill_id(cls, bill_id, hotel):
        treebo_crs_booking = TreeboCRSGetBooking.get_from_bill_id(bill_id)
        return cls.crs_booking_to_dataclass(treebo_crs_booking, hotel)
