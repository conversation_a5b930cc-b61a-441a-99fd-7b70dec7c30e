import sys
import os
import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Migrate for a collection')
    parser.add_argument('--database', type=str, help='Name of the database')
    parser.add_argument('--collection', type=str,
                        help='Name of the collection')

    arguments = parser.parse_args()
    try:
        database_migrations = [file_name for file_name in os.listdir('.') if (
            os.path.isdir(file_name) and file_name == arguments.database)]

        if not database_migrations:
            raise Exception(
                'No migrations-folder found for the database --{d}--'.format(d=arguments.database))
        database_migrations = database_migrations[0]

        collection_migrations = [file_name for file_name in os.listdir(database_migrations) if (os.path.isdir(
            "{folder}/{file}".format(folder=arguments.database, file=file_name)) and file_name == arguments.collection)]

        if not collection_migrations:
            raise Exception(
                'No migrations-folder found for collection --{c}--'.format(c=arguments.collection))
        collection_migrations = collection_migrations[0]

    except Exception as e:
        print(e)
