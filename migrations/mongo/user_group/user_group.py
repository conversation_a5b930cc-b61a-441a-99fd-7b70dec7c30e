def add_auth_users(client):
    db = client['reseller'] # pylint: disable=invalid-name
    collection = db['user_group']

    documents = [
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "role": "admin"
        }
    ]

    collection.remove({})
    collection.insert_many(documents)
