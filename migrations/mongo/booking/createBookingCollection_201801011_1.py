from pymongo.errors import CollectionInvalid
from pymongo.write_concern import WriteConcern


# <intent>_<date>_<serial_number>

def func(mongo_client):
    db = mongo_client.reseller

    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["check_in",
                         "check_out",
                         "hotel_id",
                         "booking_id",
                         "pms_meta",
                         "pms_type",
                         ],
            "properties": {
                "version": {"bsonType": "string"},
                "check_in": {"bsonType": "date"},
                "check_out": {"bsonType": "date"},
                "created_at": {"bsonType": "date"},
                "updated_at": {"bsonType": "date"},
                "booking_id": {"bsonType": "string"},
                "hotel_id": {"bsonType": "string"},
                "pms_type": {"bsonType": "string"},
                "pms_meta": {"bsonType": "object"},
                "status": {"bsonType": "string"},
                "rooms": {
                    "bsonType": ["array"],
                    "minItems": 1,
                    "items": {
                        "bsonType": "object",
                        "required": ["type", "number", "guests"],
                        "properties": {
                            "type": {"bsonType": "string"},
                            "number": {"bsonType": "string"},
                            "room_booking_id": {"bsonType": "string"},
                            "status": {"bsonType": "string"},
                            "guests": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "properties": {
                                    "stay_id": {"bsonType": "string"},
                                    "title": {"bsonType": "string"},
                                    "first_name": {"bsonType": "string"},
                                    "last_name": {"bsonType": "string"},
                                    "stay_start": {"bsonType": "date"},
                                    "stay_end": {"bsonType": "date"},
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    try:
        db.create_collection(name="bookings",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "bookings",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
