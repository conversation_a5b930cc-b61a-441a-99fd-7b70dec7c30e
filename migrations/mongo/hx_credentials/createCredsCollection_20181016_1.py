from pymongo.write_concern import WriteConcern
from pymongo.errors import CollectionInvalid


def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["key",
                         "secret",
                         "hx_id",
                         ],
            "properties": {
                "version": {"bsonType": "string"},
                "created_at": {"bsonType": "string"},
                "updated_at": {"bsonType": "string"},
                "key": {"bsonType": "string"},
                "secret": {"bsonType": "string"},
                "hx_id": {"bsonType": "string"},
                "token": {"bsonType": "string"},
            }
        }
    }

    try:
        db.create_collection(name="hx_credentials",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "hx_credentials",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
