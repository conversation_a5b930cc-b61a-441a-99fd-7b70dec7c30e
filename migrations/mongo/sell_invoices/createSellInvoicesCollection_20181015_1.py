from pymongo.errors import CollectionInvalid
from pymongo.write_concern import WriteConcern


# <intent>_<date>_<serial_number>


def func(mongo_client):
    db = mongo_client.reseller

    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": [
                "sell_invoice_id",
                "sell_invoice_number",
                "pms_type",
                "hotel_id",
                "booking_id"
            ],
            "properties": {
                "version": {"bsonType": "string"},
                "created_at": {
                    "bsonType": "date"
                },
                "updated_at": {
                    "bsonType": "date"
                },
                "wrid": {
                    "bsonType": "string"
                },
                "status": {
                    "bsonType": "string"
                },
                "sell_invoice_number": {
                    "bsonType": "string"
                },
                "pms_type": {
                    "bsonType": "string",
                    "description": "The PMS (HX/CRS) on which the booking was created :: REQUIRED"
                },
                "pms_meta": {
                    "bsonType": "object"
                },
                "line_items": {
                    "bsonType": ["array"],
                    "minItems": 1,
                    "items": {
                        "bsonType": "object",
                        "required": ["date",
                                     "quantity",
                                     "room_booking_id",
                                     "pre_tax_amount",
                                     "tax",
                                     "tax_breakup"],
                        "properties": {
                            "date": {"bsonType": "date"},
                            "quantity": {"bsonType": "int"},
                            "charge_id": {"bsonType": "string"},
                            "room_booking_id": {"bsonType": "string"},
                            "guest_stay_id": {"anyOf": [
                                {"type": "null"},
                                {"type": "string"}
                            ]},
                            "name": {"bsonType": "string"},
                            "codes": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "items": {
                                    "bsonType": "object"
                                }
                            },
                            "pre_tax_amount": {
                                "bsonType": "double",
                                "minimum": 0,
                            },
                            "tax": {
                                "bsonType": "double",
                                "minimum": 0,
                            },
                            "tax_breakup": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "items": {
                                    "bsonType": "object",
                                    "properties": {
                                        "code": {"bsonType": "string"},
                                        "amount": {"bsonType": "double"}
                                    }
                                }
                            }
                        }
                    }
                },
                "gst_details": {
                    "bsonType": "object",
                    "properties": {
                        "address": {
                            "bsonType": "object",
                            "required": ["city",
                                         "country",
                                         "pincode",
                                         "state"],
                            "properties": {
                                "city": {"bsonType": "string"},
                                "country": {"bsonType": "string"},
                                "field1": {"bsonType": "string"},
                                "field2": {"bsonType": "string"},
                                "pincode": {"bsonType": "string"},
                                "state": {"bsonType": "string"},
                            }
                        },
                        "number": {"bsonType": "string"},
                        "legal_name": {"bsonType": "string"}
                    }
                },
                "sell_invoice_id": {
                    "bsonType": "string"
                },
                "hotel_id": {
                    "bsonType": "string"
                },
                "booking_id": {
                    "bsonType": "string"
                }
            }
        }
    }

    try:
        db.create_collection(name="sell_invoices",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "sell_invoices",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
