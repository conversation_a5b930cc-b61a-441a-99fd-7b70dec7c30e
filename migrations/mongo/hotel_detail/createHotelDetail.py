from pymongo.errors import CollectionInvalid
from pymongo.write_concern import WriteConcern


# <intent>_<date>_<serial_number>

def func(mongo_client):
    db = mongo_client.reseller

    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["hotel_id"],
            "properties": {
                "hotel_id": {"bsonType": "string"},
                "hotel_name": {"bsonType": "date"},
                "hotel_owners": {
                    "bsonType": ["array"],
                    "minItems": 0,
                    "properties": {
                        "first_name": {"bsonType": "string"},
                        "last_name": {"bsonType": "string"},
                        "email": {"bsonType": "date"},
                        "phone_number": {"bsonType": "date"},
                        "is_primary_owner": {"bsonType": "boolean"},
                    }
                }
            }
        }
    }

    try:
        db.create_collection(name="hotel_detail",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "hotel_detail",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
