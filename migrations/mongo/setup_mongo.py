# pylint: disable=import-error

from pymongo import MongoClient
from booking.createBookingCollection_201801011_1 import apply_migration as booking_migration
from buy_invoices.createBuyInvoiceCollection_20181010_1 import apply_migration as buy_invoice_migration
from sell_invoices.createSellInvoicesCollection_20181015_1 import apply_migration as sell_invoice_migration
from buy_sell_mapping.createBuySellMapping_20181110_1 import apply_migration as mapping_migration
from treebo_gst.treebo_gst import ingest_treebo_gst
from indexes import add_indexes
from user_group.user_group import add_auth_users

if __name__ == "__main__":
    mongo_client = MongoClient("localhost", 27017)
    # booking_migration(mongo_client)
    # buy_invoice_migration(mongo_client)
    # sell_invoice_migration(mongo_client)
    # mapping_migration(mongo_client)

    add_indexes(mongo_client)
    ingest_treebo_gst(mongo_client)
    add_auth_users(mongo_client)
