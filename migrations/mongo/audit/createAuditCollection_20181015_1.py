# from reseller import mongo_client
from pymongo.write_concern import WriteConcern
from pymongo.errors import CollectionInvalid
import json


def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["version",
                         "resource",
                         "action",
                         "created_at",
                         "created_by",
                         "description",
                         "identifier"],
            "properties": {
                "version": {"bsonType": "string"},
                "created_at": {"bsonType": "timestamp"},
                "updated_at": {"bsonType": "timestamp"},
                "resource": {"bsonType": "string"},
                "resource_identifier": {"bsonType": "string"},
                "action": {"bsonType": "string"},
                "description": {"bsonType": "string"},
            }
        }
    }

    try:
        db.create_collection(name="audit",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "audit",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
