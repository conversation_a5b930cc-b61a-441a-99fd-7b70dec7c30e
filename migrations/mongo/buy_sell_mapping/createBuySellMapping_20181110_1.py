from pymongo.errors import CollectionInvalid
from pymongo.write_concern import Write<PERSON>oncern


def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["created_at",
                         "buy_invoice_id",
                         "buy_invoice_number",
                         "pms_type",
                         "hotel_id",
                         "booking_id"],
            "properties": {
                "version": {"bsonType": "string"},
                "created_at": {"bsonType": "date"},
                "updated_at": {"bsonType": "date"},
                "buy_invoice_id": {"bsonType": "string"},
                "buy_invoice_number": {"bsonType": "string"},
                "sell_invoice_created_time": {"bsonType": "date"},
                "sell_invoice_number": {"bsonType": "string"},
                "sell_invoice_id": {"bsonType": "string"},
                "sell_invoice_locked": {"bsonType": "bool"},
                "pms_type": {"bsonType": "string"},
                "pms_meta": {"bsonType": "object"},
                "hotel_id": {"bsonType": "string"},
                "booking_id": {"bsonType": "string"}
            }
        }
    }

    try:
        db.create_collection(name="invoice_mapping",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "invoice_mapping",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
