from pymongo.errors import CollectionInvalid
from pymongo.write_concern import WriteConcern


# <intent>_<date>_<serial_number>

def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["created_at",
                         "booking_id",
                         "invoice_number",
                         "status",
                         "pms_type",
                         "invoice_id",
                         "hotel_id"],
            "properties": {
                "version": {"bsonType": "string"},
                "created_at": {
                    "bsonType": "date"
                },
                "updated_at": {
                    "bsonType": "date"
                },
                "invoice_number": {
                    "bsonType": "string"
                },
                "invoice_id": {"bsonType": "string"},
                "gst_details": {
                    "bsonType": "object",
                    "properties": {
                        "address": {
                            "bsonType": "object",
                            "properties": {
                                "city": {"bsonType": "string"},
                                "country": {"bsonType": "string"},
                                "field1": {"bsonType": "string"},
                                "field2": {"bsonType": "string"},
                                "pincode": {"bsonType": "string"},
                                "state": {"bsonType": "string"},
                            }
                        },
                        "number": {"bsonType": "string"},
                        "legal_name": {"bsonType": "string"}
                    }
                },
                "status": {
                    "enum": ["initiated", "generated", "locked", "cancelled"],
                    "description": "current status of the invoice :: REQUIRED"
                },
                "pms_type": {
                    "bsonType": "string",
                    "description": "The PMS (HX/CRS) on which the booking was created :: REQUIRED"
                },
                "pms_meta": {
                    "bsonType": "object"
                },
                "pre_tax_amount": {
                    "bsonType": "double",
                    "minimum": 0,
                },
                "tax": {
                    "bsonType": "double",
                    "minimum": 0,
                },
                "payments": {
                    "bsonType": "double",
                    "minimum": 0,
                },
                "line_items": {
                    "bsonType": ["array"],
                    "minItems": 1,
                    "items": {
                        "bsonType": "object",
                        "properties": {
                            "date": {"bsonType": "date"},
                            "quantity": {"bsonType": "int"},
                            "codes": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "items": {
                                    "bsonType": "object"
                                }
                            },
                            "name": {"bsonType": "string"},
                            "pre_tax_amount": {
                                "bsonType": "double",
                                "minimum": 0,
                            },
                            "tax": {
                                "bsonType": "double",
                                "minimum": 0,
                            },
                            "tax_breakup": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "items": {
                                    "bsonType": "object",
                                    "properties": {
                                        "code": {"bsonType": "string"},
                                        "amount": {"bsonType": "double"}
                                    }
                                }
                            },
                            "room": {
                                "bsonType": "object",
                                "properties": {
                                    "number": {"bsonType": "string"},
                                    "type": {"bsonType": "string"}
                                }
                            },
                            "customers": {
                                "bsonType": ["array"],
                                "minItems": 1,
                                "items": {
                                    "bsonType": "object",
                                    "properties": {
                                        "id": {"bsonType": "string"},
                                        "first_name": {"bsonType": "string"},
                                        "last_name": {"bsonType": "string"}
                                    }
                                }
                            }
                        }
                    }
                },
                "hotel_id": {"bsonType": "string"},
                "booking_id": {"bsonType": "string"}
            }
        }
    }

    try:
        db.create_collection(name="buy_invoices",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "buy_invoices",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
