# from reseller import mongo_client
from pymongo.write_concern import WriteConcern
from pymongo.errors import CollectionInvalid


def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["booking_id",
                         "hotel_id",
                         "status",
                         ],
            "properties": {
                "booking_id": {"bsonType": "string"},
                "hotel_id": {"bsonType": "string"},
                "status": {"bsonType": "string"},
                "failure_description": {"bsonType": "string"},
                "created_at": {"bsonType": "timestamp"},
                "updated_at": {"bsonType": "timestamp"},
                "last_retry_at": {"bsonType": "timestamp"},
            }
        }
    }

    try:
        db.create_collection(name="ingestion_failure",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "ingestion_failure",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
