# pylint: disable=invalid-name

import pymongo


def add_indexes(client):
    db = client['reseller']
    add_buy_invoice_indexes(db)
    add_customer_invoice_indexes(db)
    add_booking_indexes(db)
    add_invoice_mapping_indexes(db)
    add_hotel_sequence_indexes(db)
    add_hotel_credit_notes_indexes(db)
    add_customer_credit_notes_indexes(db)
    add_hotel_customer_credit_note_mapping_indexes(db)
    add_booking_queue_index(db)
    add_invoices_to_be_sent_index(db)
    add_hx_credentials_index(db)
    add_hotel_invoice_report_index(db)
    add_invoice_ingestion_failure_index(db)
    add_hotel_detail_index(db)


def add_buy_invoice_indexes(db):
    buy_invoice_collection = db['hotel_invoices']
    buy_invoice_collection.create_index("invoice_id", unique=True)
    buy_invoice_collection.create_index("invoice_number", unique=True)


def add_customer_invoice_indexes(db):
    customer_invoice_collection = db['customer_invoices']
    customer_invoice_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING)])
    customer_invoice_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("invoice_id", pymongo.ASCENDING)],
        unique=True)


def add_booking_indexes(db):
    booking_invoice_collection = db['bookings']
    booking_invoice_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING)],
        unique=True)


def add_invoice_mapping_indexes(db):
    invoice_mapping_collection = db['invoice_mapping']
    invoice_mapping_collection.create_index("hotel_invoice_id", unique=True)
    invoice_mapping_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("customer_invoice_id", pymongo.ASCENDING)],
        partialFilterExpression={"customer_invoice_id": {'$gt': ""}}, unique=True)
    invoice_mapping_collection.create_index("customer_invoice_number", pymongo.ASCENDING)


def add_hotel_sequence_indexes(db):
    hotel_sequence_collection = db['hotel_invoice_sequence']
    hotel_sequence_collection.create_index([("gstin", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING)], unique=True)
    hotel_sequence_collection.create_index("hotel_seq_id", unique=True)
    hotel_sequence_collection.create_index("hotel_id", partialFilterExpression={"active": {'$eq': True}}, unique=True)


def add_hotel_invoice_report_index(db):
    hotel_invoice_report_collection = db['hotel_invoice_reports']
    hotel_invoice_report_collection.create_index([("invoice_number", pymongo.ASCENDING),
                                                  ("room_number", pymongo.ASCENDING),
                                                  ("sac_code", pymongo.ASCENDING),
                                                  ("cgst_percent", pymongo.ASCENDING)
                                                  ], unique=True)
    hotel_invoice_report_collection.create_index(
        [("report_date", pymongo.DESCENDING), ("invoice_date", pymongo.DESCENDING)])
    hotel_invoice_report_collection.create_index(
        [("hotel_id", pymongo.ASCENDING), ("report_date", pymongo.DESCENDING), ("invoice_date", pymongo.DESCENDING)])
    hotel_invoice_report_collection.create_index(
        [("hotel_id", pymongo.ASCENDING), ("invoice_number", pymongo.DESCENDING)])


def add_hotel_credit_notes_indexes(db):
    hotel_credit_notes_collection = db['hotel_credit_note']
    hotel_credit_notes_collection.create_index("credit_note_id", unique=True)
    hotel_credit_notes_collection.create_index("credit_note_number", unique=True)


def add_customer_credit_notes_indexes(db):
    customer_credit_notes_collection = db['customer_credit_note']
    customer_credit_notes_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING)])
    customer_credit_notes_collection.create_index(
        [("pms_type", pymongo.ASCENDING), ("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING),
         ("credit_note_id", pymongo.ASCENDING)], unique=True)


def add_hotel_customer_credit_note_mapping_indexes(db):
    hotel_customer_credit_note_mapping_collection = db['hotel_customer_credit_note_mapping']
    hotel_customer_credit_note_mapping_collection.create_index("hotel_credit_note_id", unique=True)
    hotel_customer_credit_note_mapping_collection.create_index("customer_credit_note_number", pymongo.ASCENDING)


def add_booking_queue_index(db):
    booking_queue_collection = db['booking_queue']
    booking_queue_collection.create_index(
        [("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING), ("pms_type", pymongo.ASCENDING)])
    booking_queue_collection.create_index("status", pymongo.ASCENDING)


def add_invoices_to_be_sent_index(db):
    invoices_to_be_sent_collection = db["invoices_to_be_sent"]
    invoices_to_be_sent_collection.create_index([("dispatch_date", pymongo.DESCENDING)])
    invoices_to_be_sent_collection.create_index([("dispatch_date", pymongo.DESCENDING), ("hotel_id", pymongo.ASCENDING)])
    invoices_to_be_sent_collection.create_index("invoice_id", pymongo.ASCENDING)


def add_hx_credentials_index(db):
    hx_credentials_index = db["hx_credentials"]
    hx_credentials_index.create_index("hx_id", unique=True)


def add_invoice_ingestion_failure_index(db):
    invoice_ingestion_failure = db['ingestion_failure']
    invoice_ingestion_failure.create_index([("hotel_id", pymongo.ASCENDING), ("booking_id", pymongo.ASCENDING)],
                                           unique=True)


def add_hotel_detail_index(db):
    hx_credentials_index = db["hotel_detail"]
    hx_credentials_index.create_index("hotel_id", unique=True)


def add_invoice_report_failure_index(db):
    invoice_ingestion_failure = db['invoice_report_failure']
    invoice_ingestion_failure.create_index([("failed_on", pymongo.DESCENDING), ("hotel_id", pymongo.ASCENDING)])


def add_hotel_invoices_indexes(db):
    hotel_invoices_collection = db['hotel_invoices']
    hotel_invoices_collection.create_index(
        [("hotel_id", pymongo.ASCENDING), ("invoice_date", pymongo.DESCENDING)])


def add_hotel_credit_note_indexes(db):
    hotel_invoices_collection = db['hotel_credit_note']
    hotel_invoices_collection.create_index(
        [("hotel_id", pymongo.ASCENDING), ("credit_note_date", pymongo.DESCENDING)])
