# from reseller import mongo_client
from pymongo.write_concern import WriteConcern
from pymongo.errors import CollectionInvalid


def func(mongo_client):
    db = mongo_client.reseller
    schema_validator = {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["process_name",
                         "hotel_id",
                         "failed_on",
                         ],
            "properties": {
                "failed_on":  {"bsonType": "string"},
                "process_name": {"bsonType": "string"},
                "hotel_id": {"bsonType": "string"},
                "invoice_id": {"bsonType": "string"},
                "status": {"bsonType": "string"},
                "failure_description": {"bsonType": "string"},
                "failure_trace": {"bsonType": "string"},
                "created_at": {"bsonType": "timestamp"},
                "updated_at": {"bsonType": "timestamp"},
            }
        }
    }

    try:
        db.create_collection(name="invoice_report_failure",
                             write_concern=WriteConcern(w="majority", wtimeout=1000))
    except CollectionInvalid as err:
        print("{err} :: CONTINUING".format(err=err))

    db.command({
        "collMod": "invoice_report_failure",
        "validator": schema_validator,
        "validationLevel": "moderate",
        "validationAction": "error"
    })


def apply_migration(mongo_client):
    func(mongo_client)
