#!/bin/bash

export MODE="$1"
APP_DIR=/usr/src/reseller

# default new relic env
NR_ENV=staging
echo $APP_DIR
mkdir -p $APP_DIR

if [[ ${FLASK_CONFIGURATION} == "Staging" ]];then
  NR_ENV=staging
  RESELLER_SETTINGS='reseller.config.staging.StagingConf'
elif [[ ${FLASK_CONFIGURATION} == "Prod" ]];then
  NR_ENV=production
  RESELLER_SETTINGS='reseller.config.prod.ProdConfig'
elif [[ ${FLASK_CONFIGURATION} == "Dev" ]];then
  NR_ENV=development
  RESELLER_SETTINGS='reseller.config.develop.DevConfig'
elif [[ ${FLASK_CONFIGURATION} == "ProdSpot" ]];then
  NR_ENV=production
  RESELLER_SETTINGS='reseller.config.develop.ProdSpotConfig'
fi

NEW_RELIC_ENVIRONMENT=$NR_ENV

NEW_RELIC_CONFIG_FILE=$APP_DIR/newrelic.ini

export NEW_RELIC_CONFIG_FILE
export NEW_RELIC_ENVIRONMENT
export RESELLER_SETTINGS

PROJECT_BASE=/usr/src/reseller
export PYTHONPATH=$PROJECT_BASE:$PYTHONPATH

echo "Starting Flask"
cd $APP_DIR

if [[ "$MODE" == "reporting" ]]; then
      echo "Running Reporting Setup"
      exec newrelic-admin run-program gunicorn reseller:app -c reseller/config/gunireportconfig.py
    else
      echo "Running Regular Setup"
      exec newrelic-admin run-program gunicorn reseller:app -c reseller/config/guniconfig.py
    fi
