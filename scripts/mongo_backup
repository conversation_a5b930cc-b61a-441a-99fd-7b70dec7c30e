#!/bin/bash
# Author - Ainesh
# Description - To push nightly reseller mongodb dump to s3
set -e

MONGOHOST="reseller-mongo-instance-03.treebo.pr"
MONGOPORT="27017"
echo "Taking DB  dump from ${MONGOHOST}:${MONGOPORT} and storing at /opt/reseller/dump"
mongodump --host ${MONGOHOST} --port ${MONGOPORT} -u resellerRead -p UfLa\!srv6uxlJ8Ah --authenticationDatabase admin --out /opt/reseller/dump
echo 'Starting S3 Upload...'
BUCKET_NAME="b2b-p-reseller"
DATE="`date +%Y%m%d`"
/usr/bin/s3cmd put -r /opt/reseller/dump s3://${BUCKET_NAME}/mongo_dump/${MONGOHOST}-${DATE}/
