FROM python:3.7.15-buster

ARG req_file=requirements.txt
RUN mkdir -p /usr/src/reseller \
    && mkdir -p /ebs1/logs \
    && mkdir -p /var/log/reseller \
    && mkdir -p /usr/src/scripts \
    && mkdir -p /gunicorn-tmp

WORKDIR /usr/src/reseller

COPY requirements /usr/src/reseller/requirements

RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
  echo "deb http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list && \
  apt-get update && \
  apt-get install -y google-chrome-stable vim && \
  rm -rf /var/lib/apt/lists/*

RUN pip install -r $req_file

COPY . /usr/src/reseller/
RUN chmod +x /usr/src/reseller/scripts/start_ingestion_event_consumer.sh
RUN chmod +x /usr/src/reseller/scripts/start_catalog_events_consumer.sh
RUN chmod +x /usr/src/reseller/scripts/reporting_start.sh
RUN chmod +x /usr/src/reseller/scripts/start_crs_events_consumer.sh
EXPOSE 8001