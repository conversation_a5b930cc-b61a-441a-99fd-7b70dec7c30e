name: release/main PR Workflow

on:
  push:
    branches: [release, main, master]
  pull_request:
    branches: [release, main, master]

jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up SSH agent
      uses: webfactory/ssh-agent@v0.5.4
      with:
        ssh-private-key: |
          ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
          ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}
          ${{ secrets.SSH_THSC_PRIVATE_KEY }}

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.7'

    - name: Install dependencies
      run: |
        pip install --upgrade pip==21.3.1
        cat << EOF > requirements/deploy.txt
        git+ssh://**************/treebo-noss/flaskhealthcheck.git@main#egg=flaskhealthcheck
        git+ssh://**************/treebo-noss/treebo-common.git@v3.0.0rc1#egg=treebo-commons
        git+ssh://**************/treebo-noss/prometheus.git@main#egg=thsc
        -r base.txt
        EOF
        pip install -r requirements/dev.txt

    - name: Black formatter check
      run: |
        black reseller --check
        black core --check
        black integrations --check
        black workers --check

    - name: Isort check
      run: |
        isort reseller --profile=black --check
        isort core --profile=black --check
        isort integrations --profile=black --check
        isort workers --profile=black --check
