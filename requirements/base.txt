#------------ STYLE GUIDE ------------#
# 1.Requirements should be always lower cased
# 2.Requirements should be sorted in alphabetical order
# 3.Requirements should have a version unless we use pip env
# 4.It is preferred to have stable versions than development/beta versions
#-------------------------------------#
werkzeug==0.16.0
beautifulsoup4==4.6.3
boto3==1.9.171
flask-pymongo==2.1.0
flask-log-request-id==0.10.0
flask-marshmallow==0.9.0
flask==1.0.2
flask-restful==0.3.6
Flask-Admin==1.5.2
flask-login==0.4.1
Flask-WTF==0.14.2
gevent==22.10.1
#goonj==0.1.9
gunicorn==19.9.0
kombu==4.2.1
logger==1.4
logstash-formatter==0.5.17
lxml==4.2.5
marshmallow==2.13.5
newrelic
num2words==0.5.7
pdfkit==0.6.1
psycogreen==1.0
pylint==2.1.1
pymongo==3.7.2
pyopenssl==18.0.0
pytest==3.9.1
requests==2.18.4
requests-oauthlib==1.0.0
simplejson==3.17.0
cryptography==3.3.1
WTForms==2.3.3
Jinja2==3.0.3
itsdangerous==2.0.1

