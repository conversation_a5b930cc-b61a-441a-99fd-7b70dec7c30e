# README #

This README file contains details about the new invoice and recievables tracking solution in Treebo.

### What is this repository for? ###

*Quick Summary
This repository contains source code for the buy invoice generation tool called Reseller.

* Version: 0.1.0

### How do I get set up? ###

Run the following steps from the project root


Configuration
```bash
python3 -m venv env
source env/bin/activate
```
 Dependencies
```bash
pip install -r requirements/dev.txt
pre-commit install
```
Database configuration
```mongo
python migrations/mongo/setup_mongo.py
```
How to run tests
```bash

```
Running instructions
```
python runserver.py
```
If during commit, you see the issue regarding locale `en_US.UTF-8` run the following:
```bash
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
```
### Contribution guidelines ###

* Code
    * Please adhere to PEP-8 standards for naming conventions
    * Run pylint regularly on code to ensure compatibility
    *

* Writing tests
    * Create package and files in parallel to the main src code
    * Name your tests starting with `test_`
    * **Do not write** tests that require database to be initialized
    * A testing unit should focus on one tiny bit of functionality and prove it correct. Good way to achieve this is to try hard to make only one assert per unit test
    * Each test should be fully independent.
    * Run unit tests before pushing code. Fix all tests if they are breaking before push.
    * Use descriptive names for tests. BDD derived test names are often helpful. E.g. even a name like `test_should_store_awaiting_hx_confirmation_before_confirm_api_call()` is acceptable.


* Code review
    * All pull requests should be accompanied by unit-tests which are passing and covering most of the newly written code.
    * New branch should be forked for every new feature, and it should be forked from and merged into develop branch

* Git Hygiene
    * Before you start working on a new feature
        - Do a git pull on develop branch to ensure it's up to date
        - Fork a new branch for the feature from develop branch
            - Never start coding in some sundry branch just because it's there
            - Branches are free in git, use them.
        - Push your feature branch upstream so that everyone else is aware of what's cooking

    * While you are working on a feature
        - Keep committing your changes every day (if not multiple times per day).
        - Don't commit a big chunk. Git's power is in smaller working commits that can then be reverted or fallen back to if required.
        - Push when you know the things worked on so far are not going to change within next hour.
            - This is like a checkpoint.
            - This also gives others a chance to merge your branch into their related-feature branch, to reduce conflicts later
            - You should be pushing at least once a day (if not multiple times)
        - It's a good practice to keep merging develop branch every day, so that merge conflicts can be kept at bay.
        - Also, if you know someone else working on the same set of files as you, see if you can merge in advance.
            - You can keep syncing with that feature-branch and resolving any code conflicts as soon as they occur.
            - Fresh conflicts are always easy to resolve than stale ones where the context may not be very clear.
            - This is the best approach to avoid merge conflicts at the last minute before you push.

    * Once you are done coding the feature
        - Ensure you pull and merge develop branch before you raise a review request
        - Push the branch and raise a code review request (pull-request)
        - If there is no more work expected on that feature (in the near term), you mark the PR to close the branch once it's merged.
        - Ensure any review comments are addressed on the same branch
        - Keep merging develop every day until the PR is approved and the branch merged.

* PR Review Guidelines

	* Code Structure
		- Abide by different layers in the system
			* API layer
				- What it's for -
					- Request data unmarshalling
					- DTO handling/verification
					- exception handling
					- service calls
					- response generation
				- Some minor DB lookups are fine as long as it's not some heavy activity conducting business logic

			* Service Layer
				- What it's for -
					- all business logic
					- service/backend orchestration
					- providing different services via APIs
				- Guidelines -
					- The service should be generic enough to cater to various activities/needs/work under the domain it covers. For example -
						- Activities like creating/cancelling/updating a booking should be carried out under the purview of a BookingService
						- Activities like invalidating invoices based on a criteria, generating invoices and PDFs etc, should be covered under InvoicingService
					- Service layer shouldn't talk to external dependencies directly. Instead it should talk to an interface.
					- All the communication between different services or between a service and an external dependency should be via a DTO
					- All APIs should be named consistently.
						- Usually the names beginning with a verb and ending with an object are effective - like confirm_booking(...), cancel_booking(...) etc
						- APIs are like a command to the service, so name them appropriately.
					- Think about the API visibility requirements.
						- If an service API is to be used by other services or the API layer, it's fine to expose that as a public API
						- But if it's just an extension of an existing public-API for offloading some part and making it modular, better to mark it as a private/protected service API.
					- Consistently add docstrings to all service-APIs denoting what it does, and what its parameters are (incluing the exptected type)
					- If needed, use assertions to ensure correct type.
					- Liberally add comments across the code to point out assumptions, expectations, possible issues, etc.
					- Try to reduce dependency on other services as far as possible. A loosely coupled service layer is easier to maintain over long period.

			* External Dependencies Layer
				- What it's for -
					- Encapsulating external dependencies
					- Allowing rest of the system to communicate with external deps in a consistent manner
					- Capturing extension points of a system in a modular manner
					- Allowing for change in dependency without affecting rest of the system
				- Guidelines
					- Define an interface to which the external dependency will conform to. For example -
						- If the dependency is on getting tax values, define the request and response interfaces as per what the system needs.
						- They don't have to conform with what the external tax service provides.
						- As long as there is a way to conform the external tax service response to the interface, it should be fine.
					- The APIs on the interface should be generic enough. For example -
						- If we are interfacing with HX for booking related operations, the APIs should denote the operations like confirm_booking, save_booking, cancel_booking etc.
						- Hx APIs like load_card, save_card et al shouldn't leak into the dependency interface.
					- All in all, when defining an external dependency, build a generic interface - data + operations - and conform the external depdency to it.

	* Coding Guidelines
		- One class per file
			- There shouldn't be more than one class in a file
			- This applies to models as well. There should always be only one model per file; all imported into the __init__.py of the models package.
			- Exceptions can be made in rare cases like: when DTOs have sub-DTOs that are not useful as seperate file otherwise; or for collecting a group of exceptions that will otherwise add a lot of noise as seperate files.
		- Naming Conventions
			- Follow python naming conventions as per PIP-8
				- Use snake_cases for variable names; CamelCases for class names.
			- Follow the naming conventions generally followed in the project
				- If it's a service, append the name with *Service
				- If it's an API or a View, append the name accordingly.
		- Space out the code better; dont cram it all together in a big ball of mud
			- Put related chunks of code together and add a blank line between such chunks.
			- Seggregate different blocks like if, else, try, catch using blank lines to add the visual seperation.
		- Add docstrings to methods and classes
		- Add ample comments around in the code.

* Other guidelines
    * TODO: <link to coding guidelines>

### Who do I talk to? ###

* Invoicing Team
    * EM: [Abhinav Nigam](mailto:<EMAIL>)
    * PM: [Abhinav Agrawal](mailto:<EMAIL>)
    * Architect: [Arvind Batra]((mailto:<EMAIL>)

    * QA: [Ainesh Patidar](mailto:<EMAIL>)
    * Devs:
        * [Prashant Kumar](mailto:<EMAIL>)
        * [Utsav Tiwary](mailto:<EMAIL>)
        * [Rohit Nagmoti](mailto:<EMAIL>)
        * [Aryan Gupta](mailto:<EMAIL>)
