import json
import os

import requests

from workers.constants import CONNECTION_TIMEOUT, RESPONSE_TIMEOUT, SLACK_ENVIRONMENT


def send_msg_to_slack(msg, url):
    if os.environ.get("DJANGO_CONFIGURATION") == SLACK_ENVIRONMENT:
        headers = {"content-type": "application/json"}
        payload = {"text": msg}
        requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=(CONNECTION_TIMEOUT, RESPONSE_TIMEOUT),
        )
