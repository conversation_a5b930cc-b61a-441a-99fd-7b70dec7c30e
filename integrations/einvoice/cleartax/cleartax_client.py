import logging
import os
from collections import namedtuple

from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from core.hotel_settings.configs.einvoice_config import EinvoiceConfig
from core.hotel_settings.tenant_settings import TenantSettings
from integrations.einvoice.cleartax.einvoice_dto import EInvoiceDTO, IrpDetailsDTO
from integrations.einvoice.cleartax.errors import EInvoicingException
from integrations.einvoice.cleartax.schema import ClearTaxSchemaV1
from object_registry import register_instance
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)
from reseller.reporting.nav_purchase_report.external_client.core.base_client import (
    BaseExternalClient,
)

logger = logging.getLogger(__name__)

_schema = ClearTaxSchemaV1()

_Error = namedtuple("Error", ["error_code", "message"])


@register_instance(dependencies=[TenantSettings])
class ClearTaxClient(BaseExternalClient):
    page_map = {}

    def __init__(self, tenant_settings: TenantSettings):
        self.tenant_settings: TenantSettings = tenant_settings
        super().__init__()

    def get_page_map(self):
        self.page_map["generate_irn"] = dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/v1/govt/api/Invoice/",
        )
        return self.page_map

    @staticmethod
    def get_einvoicing_auth_token(hotel_id, config: EinvoiceConfig):
        if os.environ.get("APP_ENV") == "testing":
            auth_token = os.environ.get("CLEARTAX_AUTH_TOKEN")
        else:
            secret_name = (
                "{}/cleartax-hotel-auth".format(hotel_id)
                if config.is_hotel_wise_clear_tax_account_configured()
                else "cleartax-hotel-auth"
            )
            secret = AwsSecretManager.get_secret(
                tenant_id=get_current_tenant_id() or TenantClient.get_default_tenant(),
                secret_name=secret_name,
            )
            auth_token = secret["auth_token"]
        return auth_token

    def get_domain(self):
        return ServiceRegistryClient.get_service_url(
            ServiceEndPointNames.CLEAR_TAX_SERVICE_URL
        )

    def generate_irn(self, hotel_id, einvoice: EInvoiceDTO):
        config: EinvoiceConfig = self.tenant_settings.get_einvoicing_config(hotel_id)
        gstin = einvoice.supplier_information.get("gstin")
        auth_token = self.get_einvoicing_auth_token(hotel_id, config)
        custom_headers = self.build_custom_headers_headers(auth_token, gstin)

        page_name = "generate_irn"
        post_data, errors = _schema.dump(einvoice)
        response = self.make_call(
            page_name, data=post_data, custom_headers=custom_headers
        )

        if not response.is_success():
            message = (
                response.errors.get("error_message")
                or "Cleartax Client Failed, contact escalations"
            )
            extra = {
                "client_name": "cleartax",
                "client_comment": "Http code: {}".format(response.status_code),
                "client_response": response.errors,
            }
            raise EInvoicingException(
                error=_Error(error_code="EINV-0316", message=message),
                extra_payload=extra,
            )

        data = response.json_response

        if data.get("ErrorDetails"):
            error_details = data.get("ErrorDetails") or []
            raise EInvoicingException(
                error=_Error(
                    error_code="EINV-0317",
                    message=" | ".join(
                        [e.get("error_message", None) for e in error_details]
                    ),
                ),
                extra_payload=dict(
                    errors=[e.get("error_message", None) for e in error_details]
                ),
            )
        return IrpDetailsDTO(
            data["Irn"],
            data["SignedQRCode"],
            data["AckNo"],
            data["AckDt"],
            data["SignedInvoice"],
        )

    @staticmethod
    def build_custom_headers_headers(auth_token, gstin):
        headers = dict()
        headers["X-Cleartax-Auth-Token"] = auth_token
        headers["x-cleartax-product"] = "EInvoice"
        headers["gstin"] = gstin
        return headers
