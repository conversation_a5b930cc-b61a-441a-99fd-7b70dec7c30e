from decimal import Decimal
from typing import List

from ths_common.constants.billing_constants import TaxTypes
from ths_common.utils.common_utils import extract_state_code_from_gstin

from core.common.data_classes.gst_details import GstDetails
from core.common.errors import IngestionErrors
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.data_classes.line_item import LineItem
from integrations.einvoice.cleartax.errors import EInvoicingError
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)


class EInvoiceSerializable:
    def as_dict(self):
        return vars(self)


class EInvoiceGSTInfo(EInvoiceSerializable):
    MAX_LINE_LENGTH = 60

    def __init__(self, gst_details: GstDetails):
        self.legal_name = gst_details.legal_name
        self.is_sez = gst_details.is_sez
        self.has_lut = gst_details.has_lut

        self.gstin = gst_details.gstin_number
        self.phone_number = ""
        self.email_id = ""
        self.place_of_supply = None

        address = gst_details.address
        if not address:
            raise EInvoicingError(IngestionErrors.MISSING_ADDRESS)
        self._split_and_distribute_address(address)

        if self._is_cleartax_sandbox_gstin():
            self.state_code = self.gstin[0:2]
        else:
            self.state_code = extract_state_code_from_gstin(self.gstin)

        if not self.state_code:
            raise EInvoicingError(IngestionErrors.INVALID_GSTIN)

    def _split_and_distribute_address(self, address):
        if address.field1 and address.city and address.pincode:
            address_field = address.field1 + (address.field2 or "")
            self.location = address.city[:50]
            self.pincode = address.pincode
        else:
            address_parts = []
            if address.field1:
                address_parts.append(address.field1)
            if address.field2:
                address_parts.append(address.field2)
            if address.city:
                address_parts.append(address.city)
            address_line = ", ".join(address_parts)
            address_splits = address_line.replace("-", ",").split(",")
            if len(address_splits) == 1:
                address_splits = address_line.split()
            pincode = (
                address_splits[-1].strip()
                if address_splits[-1].strip().isdigit()
                else None
            )
            location = (
                ",".join(address_splits[-3:-1])
                if pincode
                else ",".join(address_splits[-2:])
            )
            address_field = (
                ",".join(address_splits[:-3])
                if pincode
                else ",".join((address_splits[:-2]))
            )
            self.pincode = address.pincode or pincode or ""
            self.location = address.city or location or ""
        self.address_field1 = address_field
        self.address_field2 = None
        if len(address_field) > 50:
            self.address_field1 = address_field[:50]
            self.address_field2 = address_field[50:100]
            self.address_field2 = (
                self.address_field2 if len(self.address_field2) > 3 else None
            )

    def _is_cleartax_sandbox_gstin(self):
        """
        Adding this check to enable einvoicing demos using clear tax sandbox
        gstin which does not comply with official gstin regex
        :return:
        """
        return self.gstin == "29AAFCD5862R000"


class EInvoiceItem(EInvoiceSerializable):
    def __init__(self, serial_number, line_item: LineItem):
        charge = line_item.charge

        self.product_name = charge.name
        self.hsn_code = charge.hsn_code
        self.serial_number = serial_number

        self.is_service = "Y"

        self.quantity = 1
        self.unit_of_measure = "UNT"

        self.unit_price = charge.pre_tax
        self.assessable_amount = charge.pre_tax
        self.total_amount = charge.pre_tax * self.quantity
        self.discount = Decimal(0)
        self.other_charge = Decimal(0)
        self.total_item_value = self.total_amount + charge.tax

        self.cgst = Decimal(0)
        self.sgst = Decimal(0)
        self.igst = Decimal(0)
        self.cess = Decimal(0)
        self.state_cess = Decimal(0)
        self.cess_non_advalorem = Decimal(0)

        self.cgst_amount = Decimal(0)
        self.sgst_amount = Decimal(0)
        self.igst_amount = Decimal(0)
        self.cess_amount = Decimal(0)
        self.state_cess_amount = Decimal(0)
        self.cess_non_advalorem_amount = Decimal(0)

        for td in charge.tax_breakup:
            if td.code == TaxTypes.CGST:
                self.cgst += td.percent
                self.cgst_amount += td.amount
            elif td.code == TaxTypes.SGST:
                self.sgst += td.percent
                self.sgst_amount += td.amount
            elif td.code == TaxTypes.IGST:
                self.igst += td.percent
                self.igst_amount += td.amount
            else:
                self.cess += td.percent
                self.cess_amount += td.amount
        self.gst = self.igst if self.igst else sum([self.cgst, self.sgst])


class EInvoiceTotal(EInvoiceSerializable):
    def __init__(self, einvoice_items: List[EInvoiceItem]):
        self.assessable_amount = Decimal(0)
        self.other_charge = Decimal(0)
        self.discount = Decimal(0)
        self.final_invoice_value = Decimal(0)

        self.cgst = Decimal(0)
        self.igst = Decimal(0)
        self.sgst = Decimal(0)
        self.cess = Decimal(0)
        self.state_cess = Decimal(0)
        self.cess_non_advalorem = Decimal(0)

        for item in einvoice_items:
            self.assessable_amount += item.assessable_amount
            self.discount += item.discount
            self.other_charge += item.other_charge
            self.final_invoice_value += item.total_item_value

            self.cgst += item.cgst_amount
            self.sgst += item.sgst_amount
            self.cess += item.cess_amount
            self.igst += item.igst_amount
            self.state_cess += item.state_cess_amount
            self.cess_non_advalorem += item.cess_non_advalorem_amount


class EInvoiceDTO:
    def __init__(self, invoice_or_creditnote):
        self.tax_schema = "GST"
        self.version = "1.1"
        self._entity = invoice_or_creditnote

        if isinstance(self._entity, HotelInvoice):
            issued_by = self._entity.hotel
            issued_to = self._entity.gst_details
            self.document_number = self._entity.invoice_number.lstrip("0")
            self.document_type = "INV"
            self.document_date = self._entity.invoice_date
            self._line_items = [
                EInvoiceItem(serial_number, charge)
                for (serial_number, charge) in enumerate(self._entity.line_items)
            ]

        elif isinstance(self._entity, HotelCreditNote):
            issued_by = self._entity.hotel
            issued_to = self._entity.gst_details
            self.document_number = self._entity.credit_note_number.lstrip("0")
            self.document_type = "CRN"
            self.document_date = self._entity.credit_note_date
            invoice_ids = self._entity.get_linked_invoice_ids()
            creditnote_invoices = (
                get_hotel_invoice_reports_repository().get_invoices_by_ids(invoice_ids)
                if invoice_ids
                else []
            )
            self.original_invoice_number = ",".join(
                [i.invoice_number for i in creditnote_invoices]
            )
            self._line_items = [
                EInvoiceItem(index, charge)
                for index, charge in enumerate(self._entity.line_items)
            ]
        else:
            raise RuntimeError("Unknown type for Einvoice")

        self._issued_by = EInvoiceGSTInfo(
            gst_details=issued_by.gst_details,
        )
        self._issued_to = EInvoiceGSTInfo(gst_details=issued_to)
        self._total = EInvoiceTotal(self._line_items)

        self._issued_to.place_of_supply = self._issued_by.state_code

    @property
    def transaction_details(self):
        return dict(supply_type=self.supply_type, tax_schema=self.tax_schema)

    @property
    def document_details(self):
        return dict(
            document_type=self.document_type,
            document_number=self.document_number,
            document_date=self.document_date,
        )

    @property
    def supplier_information(self):
        return self._issued_by.as_dict()

    @property
    def buyer_information(self):
        return self._issued_to.as_dict()

    @property
    def total_value_details(self):
        return self._total

    @property
    def item_details(self):
        return self._line_items

    @property
    def supply_type(self):
        if self._issued_to.is_sez and not self._issued_to.has_lut:
            return "SEZWP"
        if self._issued_to.is_sez and self._issued_to.has_lut:
            return "SEZWOP"
        return "B2B"


class IrpDetailsDTO:
    def __init__(self, irn, qr_code, ack_no, acked_on, signed_invoice):
        self.irn = irn
        self.qr_code = qr_code
        self.ack_no = ack_no
        self.acked_on = acked_on
        self.signed_invoice = signed_invoice

    @classmethod
    def dummy(cls):
        import datetime

        return cls(
            irn=None,
            qr_code=None,
            acked_on=datetime.datetime.today(),
            ack_no=None,
            signed_invoice=None,
        )
