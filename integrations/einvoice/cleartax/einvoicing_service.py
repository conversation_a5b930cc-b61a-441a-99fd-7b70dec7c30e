import abc
import logging
from typing import Sequence, Union

from ths_common.constants.billing_constants import IssuedByType

from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.data_classes.line_item import LineItem
from core.hotel_settings.tenant_settings import TenantSettings
from integrations.einvoice.cleartax.cleartax_client import ClearTaxClient
from integrations.einvoice.cleartax.einvoice_dto import EInvoiceDTO, IrpDetailsDTO
from integrations.einvoice.cleartax.errors import (
    EInvoicingException,
    EInvoicingValidationError,
)
from object_registry import register_instance
from reseller.repository.hotel_invoice import get_hotel_invoice_repository

logger = logging.getLogger(__name__)


IST = "Asia/Kolkata"


class EInvoicingApplicability:
    def __init__(self, is_einvoicing_applicable):
        self.is_applicable = is_einvoicing_applicable

    def __bool__(self):
        return self.is_applicable

    def should_submit_to_irp(self):
        if self.is_applicable:
            return True
        return False

    @classmethod
    def from_data(
        cls,
        bill_to,
        issued_by,
        issued_by_type,
        einvoicing_config,
        invoice_charges: Sequence[LineItem] = None,
        creditnote_line_items=None,
        creditnote_invoices=None,
    ):
        data = {
            "bill_to": bill_to,
            "issued_by": issued_by,
            "issued_by_type": issued_by_type,
            "invoice_charges": invoice_charges,
            "creditnote_line_items": creditnote_line_items,
            "einvoicing_config": einvoicing_config,
            "creditnote_invoices": creditnote_invoices,
        }
        einvoicing_applicable = (
            einvoice_live_date_validator(data)
            and gstin_applicablity_validator(data)
            and config_validator(data)
            and creditnote_invoices_validator(data)
        )
        return cls(einvoicing_applicable)


class EInvoiceValidator(metaclass=abc.ABCMeta):
    def __init__(self, field_names: Sequence[str], _raise=True):
        self.field_names = (
            [field_names] if isinstance(field_names, str) else field_names
        )
        self.message = f"{self.__class__} failed"
        self._raise = _raise

    @abc.abstractmethod
    def validate(self, *args, **kwargs):
        pass

    def __call__(self, data, _raise=None):
        data = {fn: data.get(fn) for fn in self.field_names}
        _raise = _raise if _raise is not None else self._raise
        if self.validate(**data):
            return True
        if _raise:
            raise EInvoicingValidationError(description=self.message)
        return False


class EInvoiceLiveDateValidator(EInvoiceValidator):
    def __init__(self, field_names=None, _raise=False):
        if field_names is None:
            field_names = ["invoice_charges", "creditnote_line_items"]
        super().__init__(field_names, _raise)

    def validate(self, invoice_charges, creditnote_line_items):
        return bool(invoice_charges) != bool(creditnote_line_items)


class GstinApplicabilityValidator(EInvoiceValidator):
    def __init__(self, field_names="bill_to", _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, bill_to):
        return bool(bill_to.gstin_number)


class GstinValidator(EInvoiceValidator):
    def __init__(self, field_names="gstin", _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, gstin):
        return len(gstin) == 15


class CreditnoteInvoicesValidator(EInvoiceValidator):
    def __init__(self, field_names="creditnote_invoices", _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, creditnote_invoices):
        if not creditnote_invoices:
            return True
        invoice_irn = [invoice.irn for invoice in creditnote_invoices]
        if None in invoice_irn:
            return False
        return True


class ConfigValidator(EInvoiceValidator):
    def __init__(self, field_names=None, _raise=False):
        if field_names is None:
            field_names = ["einvoicing_config", "issued_by"]
        super().__init__(field_names, _raise)

    def validate(self, einvoicing_config, issued_by):
        gstin = issued_by.gst_details.gstin_number
        if not einvoicing_config:
            return False
        if einvoicing_config.is_gstin_black_listed(gstin):
            return False
        return einvoicing_config.is_enabled()


def validate(validators: Sequence[EInvoiceValidator], data, _raise=False):
    validations = [validator(data, _raise) for validator in validators]
    return all(validations)


einvoice_live_date_validator = EInvoiceLiveDateValidator()
gstin_applicablity_validator = GstinApplicabilityValidator()
config_validator = ConfigValidator()
creditnote_invoices_validator = CreditnoteInvoicesValidator()


@register_instance(
    dependencies=[
        ClearTaxClient,
        TenantSettings,
    ]
)
class EInvoicingService(object):
    def __init__(
        self,
        client,
        tenant_settings,
    ):
        self.client: ClearTaxClient = client
        self.tenant_settings: TenantSettings = tenant_settings

    def is_einvoicing_applicable(
        self, entity: Union[HotelInvoice, HotelCreditNote]
    ) -> EInvoicingApplicability:
        if isinstance(entity, HotelInvoice):
            return self._is_einvoicing_applicable_for_invoice(entity)
        elif isinstance(entity, HotelCreditNote):
            return self._is_einvoicing_applicable_for_creditnote(entity)
        else:
            raise RuntimeError("unknown entity for EInvoice applicability check")

    def _is_einvoicing_applicable_for_invoice(self, invoice: HotelInvoice):
        bill_to = invoice.gst_details
        issued_by = invoice.hotel
        issued_by_type = IssuedByType.HOTEL
        charges = invoice.line_items
        hotel_id = invoice.hotel_id
        einvoicing_config = self.tenant_settings.get_einvoicing_config(
            hotel_id=hotel_id
        )
        return EInvoicingApplicability.from_data(
            bill_to,
            issued_by,
            issued_by_type,
            einvoicing_config,
            invoice_charges=charges,
        )

    def _is_einvoicing_applicable_for_creditnote(self, credit_note: HotelCreditNote):
        bill_to = credit_note.gst_details
        issued_by = credit_note.hotel
        issued_by_type = IssuedByType.HOTEL
        line_items = credit_note.line_items
        hotel_id = credit_note.hotel_id
        einvoicing_config = self.tenant_settings.get_einvoicing_config(
            hotel_id=hotel_id
        )
        invoice_ids = credit_note.get_linked_invoice_ids()
        creditnote_invoices = (
            get_hotel_invoice_repository().get_invoices_by_ids(invoice_ids)
            if invoice_ids
            else []
        )
        return EInvoicingApplicability.from_data(
            bill_to,
            issued_by,
            issued_by_type,
            einvoicing_config,
            creditnote_line_items=line_items,
            creditnote_invoices=creditnote_invoices,
        )

    def generate_irn(
        self,
        document: Union[HotelInvoice, HotelCreditNote],
    ) -> IrpDetailsDTO:

        einvoice = EInvoiceDTO(document)
        try:
            irp_response: IrpDetailsDTO = self.client.generate_irn(
                document.hotel_id, einvoice
            )
        except EInvoicingException as e:
            raise e
        except Exception as e:
            raise EInvoicingException(
                description=f"{self.client} failed due to  {e}"
            ) from e
        return irp_response
