from marshmallow import Schema
from marshmallow.fields import Float, Integer, Nested, String


class TransactionDetailsSchema(Schema):
    supply_type = String(dump_to="SupTyp", required=True)
    tax_schema = String(dump_to="TaxSch", required=True)


class DocumentDetailsSchema(Schema):
    document_type = String(dump_to="Typ", required=True)
    document_number = String(dump_to="No", required=True)
    document_date = String(dump_to="Dt", required=True)


class SupplierInformationSchema(Schema):
    gstin = String(dump_to="Gstin", required=True)
    legal_name = String(dump_to="LglNm", required=True)
    address_field1 = String(dump_to="Addr1", required=True)
    address_field2 = String(dump_to="Addr2", required=False)
    location = String(dump_to="Loc", required=True)
    pincode = Integer(dump_to="Pin", required=True)
    state_code = Integer(dump_to="Stcd", required=True)
    phone_number = Integer(dump_to="Ph", required=False)
    email_id = String(dump_to="Em", required=False)


class BuyerInformationSchema(Schema):
    gstin = String(dump_to="Gstin", required=True)
    legal_name = String(dump_to="LglNm", required=True)
    place_of_supply = String(dump_to="Pos", required=True)
    address_field1 = String(dump_to="Addr1", required=True)
    address_field2 = String(dump_to="Addr2", required=False)
    location = String(dump_to="Loc", required=True)
    pincode = Integer(dump_to="Pin", required=True)
    state_code = Integer(dump_to="Stcd", required=True)


class ItemDetailsSchema(Schema):
    serial_number = String(dump_to="SlNo", required=True)
    is_service = String(dump_to="IsServc", required=True)
    product_name = String(dump_to="PrdNm", required=True)
    product_description = String(dump_to="PrdDesc", required=False)
    hsn_code = String(dump_to="HsnCd", required=True)
    quantity = Float(dump_to="Qty", required=True)
    free_quantity = Float(dump_to="FreeQty", required=False)
    unit_of_measure = String(dump_to="Unit", required=True)
    unit_price = Float(dump_to="UnitPrice", required=True)
    total_amount = Float(dump_to="TotAmt", required=False)
    discount = Float(dump_to="Discount", required=False)
    other_charge = Float(dump_to="OthChrg", required=False)
    assessable_amount = Float(dump_to="AssAmt", required=True)
    gst = Float(dump_to="GstRt", required=True)
    cgst = Float(dump_to="CgstRt", required=False)
    sgst = Float(dump_to="SgstRt", required=False)
    igst = Float(dump_to="IgstRt", required=False)
    cess = Float(dump_to="CesRt", required=False)
    cess_non_advalorem = Float(dump_to="CesNonAdval", required=True)
    state_cess = Float(dump_to="StateCesRt", required=True)
    cgst_amount = Float(dump_to="CgstAmt", required=False)
    sgst_amount = Float(dump_to="SgstAmt", required=False)
    igst_amount = Float(dump_to="IgstAmt", required=False)
    cess_amount = Float(dump_to="CesAmt", required=False)
    state_cess_amount = Float(dump_to="StateCesAmt", required=False)
    total_item_value = Float(dump_to="TotItemVal", required=True)


class TotalValueDetailsSchema(Schema):
    assessable_amount = Float(dump_to="AssVal", required=True)
    cgst = Float(dump_to="CgstVal", required=False)
    sgst = Float(dump_to="SgstVal", required=False)
    igst = Float(dump_to="IgstVal", required=False)
    cess = Float(dump_to="CesVal", required=False)
    state_cess = Float(dump_to="StCesVal", required=False)
    discount = Float(dump_to="Disc", required=False)
    other_charge = Float(dump_to="OthChrg", required=False)
    final_invoice_value = Float(dump_to="TotInvVal", required=True)


class ClearTaxSchemaV1(Schema):
    version = String(dump_to="Version", required=True)
    irn = String(dump_to="Irn", required=False)

    transaction_details = Nested(
        TransactionDetailsSchema, required=True, dump_to="TranDtls"
    )
    document_details = Nested(DocumentDetailsSchema, required=True, dump_to="DocDtls")
    supplier_information = Nested(
        SupplierInformationSchema, required=True, dump_to="SellerDtls"
    )
    buyer_information = Nested(
        BuyerInformationSchema, required=True, dump_to="BuyerDtls"
    )
    item_details = Nested(
        ItemDetailsSchema, required=True, dump_to="ItemList", many=True
    )
    total_value_details = Nested(
        TotalValueDetailsSchema, required=True, dump_to="ValDtls"
    )
