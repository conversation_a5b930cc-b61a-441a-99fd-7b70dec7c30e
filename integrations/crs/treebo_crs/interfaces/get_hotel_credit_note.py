# -*- coding: utf-8 -*-
import abc


class GetHotelCreditNoteInterface:
    __metaclass__ = abc.ABCMeta

    def __init__(self, credit_note_id):
        self.credit_note_id = credit_note_id

    def get(self):
        credit_note_id = self._get()
        return credit_note_id

    @abc.abstractmethod
    def _get(self):
        pass

    @classmethod
    @abc.abstractmethod
    def from_customer_credit_note_id(cls, customer_credit_note_id):
        pass
