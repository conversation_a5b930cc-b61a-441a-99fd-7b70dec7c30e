# -*- coding: utf-8 -*-
import abc


class GetHotelInvoiceInterface:
    __metaclass__ = abc.ABCMeta

    def __init__(self, invoice_id):
        self.invoice_id = invoice_id

    def get(self):
        invoice = self._get()
        return invoice

    @abc.abstractmethod
    def _get(self):
        pass

    @classmethod
    @abc.abstractmethod
    def from_customer_invoice(cls, customer_invoice_id):
        pass
