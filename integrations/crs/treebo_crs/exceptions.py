class InvalidHotelInvoiceId(Exception):
    def __init__(self):
        message = "Invalid hotel invoice Id for requesting hotel invoice"
        super(InvalidHotelInvoiceId, self).__init__(message)


class InvalidHotelCreditNoteId(Exception):
    def __init__(self):
        message = "Invalid hotel credit note Id for requesting hotel credit note"
        super(InvalidHotelCreditNoteId, self).__init__(message)
