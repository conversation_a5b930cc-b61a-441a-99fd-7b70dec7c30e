from core.booking.integrations.crs.api.get_booking import (
    GetBookingAPI as CRSGetBookingAPI,
)
from core.customer_invoice.integrations.crs.api.get_invoices import (
    GetInvoicesAPI as CRSGetInvoicesAPI,
)
from core.hotel_invoice.integrations.crs.api.invoice_from_customer_invoice import (
    CRSGetInvoiceFromCustomerInvoiceAPI,
)
from reseller import app

crs_get_invoices = CRSGetInvoicesAPI.as_view("internal_crs_crs_getinvoices")
app.add_url_rule("/crs-getinvoices/", view_func=crs_get_invoices, methods=["GET"])

get_booking = CRSGetBookingAPI.as_view("internal_crs_treebo_getbooking")
app.add_url_rule("/crs-getbooking/", view_func=get_booking, methods=["GET"])

hotel_invoice_from_customer_invoice = CRSGetInvoiceFromCustomerInvoiceAPI.as_view(
    "internal_crs_treebo_get_hotel_invoice_from_customer_invoice"
)
app.add_url_rule(
    "/crs-hotel-invoice-from-customer-invoice/",
    view_func=hotel_invoice_from_customer_invoice,
    methods=["GET"],
)
