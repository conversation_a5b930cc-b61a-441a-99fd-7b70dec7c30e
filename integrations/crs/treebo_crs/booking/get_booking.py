# pylint: disable=unused-variable
import logging
import time

from thsc.crs.entities.booking import Booking as THSCBooking
from thsc.crs.entities.booking import BookingSearchQuery

from integrations.crs.treebo_crs.interfaces.get_booking import GetBookingInterface

logger = logging.getLogger(__name__)


class TreeboCRSGetBooking(GetBookingInterface):
    def _get(self):
        try:
            start_time = time.time()
            thsc_booking = THSCBooking.get(self.booking_id)
            end_time = time.time()
            logger.info(
                f" CRS API Called: {self.__class__.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Booking from thsc: {thsc_booking.__dict__}"
            )
            return thsc_booking

        except Exception as e:
            logger.error(
                f"CRS API Call failed: {self.__class__.__name__} with error: {str(e)}"
            )

    @classmethod
    def get_from_bill_id(cls, bill_id):
        search_query = BookingSearchQuery(bill_id=bill_id)
        try:
            start_time = time.time()
            thsc_booking = THSCBooking.search(search_query).bookings[0]
            end_time = time.time()
            logger.info(
                f" CRS API Called: {cls.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Booking from thsc: {thsc_booking.__dict__}"
            )
            return thsc_booking

        except IndexError as e:
            logger.error(f"CRS API Call failed: {cls.__name__} with error: {str(e)}")
            raise Exception(f"Booking not found for bill id {bill_id}")
