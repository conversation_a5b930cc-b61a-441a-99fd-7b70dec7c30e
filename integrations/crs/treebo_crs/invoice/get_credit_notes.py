# pylint: disable=unused-variable

import logging
import time

from thsc.crs.entities.billing import Bill as THSCBilling

from integrations.crs.exceptions import CrsApiFailed
from integrations.crs.treebo_crs.interfaces.get_credit_notes import (
    GetCreditNotesInterface,
)

logger = logging.getLogger(__name__)


class CRSGetCreditNotes(GetCreditNotesInterface):
    def _get(self):
        try:

            start_time = time.time()
            thsc_credit_notes = THSCBilling(bill_id=self.bill_id).get_credit_notes()
            end_time = time.time()
            logger.info(
                f" CRS API Called: {self.__class__.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Credit notes from thsc: {thsc_credit_notes}"
            )
            return thsc_credit_notes

        except Exception as e:
            logger.error(
                f"CRS API Call failed: {self.__class__.__name__} with error: {str(e)}"
            )
