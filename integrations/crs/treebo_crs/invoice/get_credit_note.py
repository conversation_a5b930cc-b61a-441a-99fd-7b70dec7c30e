import logging
import time

from thsc.crs.entities.billing import Bill as THSCCreditNote

from integrations.crs.treebo_crs.interfaces.get_credit_note import (
    GetCreditNoteInterface,
)

logger = logging.getLogger(__name__)


class CRSGetCreditNote(GetCreditNoteInterface):
    def _get(self):
        try:
            start_time = time.time()
            thsc_credit_note = THSCCreditNote(
                bill_id=self.bill_id
            ).get_credit_note_template(credit_note_id=self.credit_note_id)
            end_time = time.time()
            logger.info(
                f" CRS API Called: {self.__class__.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Credit note from thsc: {thsc_credit_note}"
            )
            return thsc_credit_note

        except Exception as e:
            logger.error(
                f"CRS API Call failed: {self.__class__.__name__} with error: {str(e)}"
            )
