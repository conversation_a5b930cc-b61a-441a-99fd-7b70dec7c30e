from thsc.crs.request_executor import RequestExecutor

from integrations.crs.treebo_crs.exceptions import InvalidHotelInvoiceId
from integrations.crs.treebo_crs.interfaces.get_hotel_invoice import (
    GetHotelInvoiceInterface,
)
from integrations.crs.treebo_crs.invoice.executer import InvoiceExecutorService


class CRSGetHotelInvoice(GetHotelInvoiceInterface):
    def _get(self):
        pass

    @classmethod
    def from_customer_invoice(cls, customer_invoice_id):
        customer_invoice = cls._make_request(customer_invoice_id)
        # if customer_invoice.get('reseller_invoice_id'):
        hotel_invoice_id = customer_invoice.get("hotel_invoice_id")
        if not hotel_invoice_id:
            raise InvalidHotelInvoiceId()
        return cls._make_request(hotel_invoice_id)

    @classmethod
    def _make_request(cls, invoice_id):
        executor = RequestExecutor()
        return InvoiceExecutorService(executor).get_invoice(invoice_id=invoice_id)
