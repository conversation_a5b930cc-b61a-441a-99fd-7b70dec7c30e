import logging
import time

from thsc.crs.request_executor import RequestExecutor

from integrations.crs.treebo_crs.interfaces.get_invoice import GetInvoiceInterface
from integrations.crs.treebo_crs.invoice.executer import InvoiceExecutorService

logger = logging.getLogger(__name__)


class CRSGetInvoice(GetInvoiceInterface):
    def _get(self):
        return CRSGetInvoice._make_request(
            self.invoice_id
        )  # pylint: disable=protected-access

    @classmethod
    def _make_request(cls, invoice_id):
        executor = RequestExecutor()
        try:
            start_time = time.time()
            thsc_invoice = InvoiceExecutorService(executor).get_invoice(invoice_id)
            end_time = time.time()
            logger.info(
                f" CRS API Called: {cls.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Invoice from thsc: {thsc_invoice}"
            )
            return thsc_invoice

        except Exception as e:
            logger.error(f"CRS API Call failed: {cls.__name__} with error: {str(e)}")
