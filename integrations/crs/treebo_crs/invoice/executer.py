from thsc.crs.request_templates.request import Request


class InvoiceExecutorService:
    def __init__(self, executor):
        self.executor = executor

    def get_invoice_template(self, booking_id, invoice_id):
        request = GetTemplateInvoice(booking_id, invoice_id)
        return self.executor.execute(request)

    def get_invoice(self, invoice_id):
        request = GetInvoice(invoice_id)
        return self.executor.execute(request)


class GetTemplateInvoice(Request):
    _payload = False

    # pylint: disable=useless-super-delegation
    def __init__(self, booking_id, invoice_id):
        super(GetTemplateInvoice, self).__init__(
            f"/bookings/{booking_id}/invoices/{invoice_id}/template", "POST"
        )

    def get_uri(self):
        return super(GetTemplateInvoice, self).get_uri()

    def from_dict(self, response):
        return response["template"]


class GetInvoice(Request):
    _payload = False

    # pylint: disable=useless-super-delegation
    def __init__(self, invoice_id):
        super(GetInvoice, self).__init__(f"/invoices/{invoice_id}?show_raw=True", "GET")

    def get_uri(self):
        return super(GetInvoice, self).get_uri()

    def from_dict(self, response):
        return response
