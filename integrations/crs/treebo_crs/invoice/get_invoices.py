# pylint: disable=unused-variable

import logging
import time

from thsc.crs.entities.booking import Booking as THSCBooking

from integrations.crs.treebo_crs.interfaces.get_invoices import GetInvoicesInterface

logger = logging.getLogger(__name__)


class CRSGetInvoices(GetInvoicesInterface):
    def _get(self):
        try:
            start_time = time.time()
            thsc_invoices = THSCBooking(booking_id=self.booking_id).get_invoices()
            end_time = time.time()
            logger.info(
                f" CRS API Called: {self.__class__.__name__}, response time: {end_time - start_time:.2f} seconds"
                f" Got {len(thsc_invoices)} invoice from thsc"
            )
            return thsc_invoices

        except Exception as e:
            logger.error(
                f"CRS API Call failed: {self.__class__.__name__} with error: {str(e)}"
            )
