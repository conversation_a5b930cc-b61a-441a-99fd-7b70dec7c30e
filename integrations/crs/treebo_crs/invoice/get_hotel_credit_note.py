from thsc.crs.entities.billing import CreditNote as THSCCreditNote

from integrations.crs.treebo_crs.exceptions import InvalidHotelCreditNoteId
from integrations.crs.treebo_crs.interfaces.get_hotel_credit_note import (
    GetHotelCreditNoteInterface,
)


class CRSGetHotelCreditNote(GetHotelCreditNoteInterface):
    def _get(self):
        pass

    @classmethod
    def from_customer_credit_note_id(cls, customer_credit_note_id):
        customer_credit_note = THSCCreditNote.get(
            credit_note_id=customer_credit_note_id
        )
        hotel_credit_note_id = customer_credit_note.hotel_credit_note_id
        if not hotel_credit_note_id:
            return None
        return THSCCreditNote.get(credit_note_id=hotel_credit_note_id)
