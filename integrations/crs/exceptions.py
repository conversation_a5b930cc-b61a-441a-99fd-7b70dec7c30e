class MaxRetriesExceeded(RuntimeError):
    def __init__(self, crs, api, num_retries):
        """
        raised when a CRS API fails consecutively for num_retries times
        :param api: api request
        :param num_retries: number of retries attempted
        """
        self.api = api
        self.crs_impl = crs
        self.num_retries = num_retries

        message = "CRS({impl}) API '{n}' failed despite retries. Number of re-tries: ({r})".format(
            n=self.api, impl=self.crs_impl, r=num_retries
        )
        super(MaxRetriesExceeded, self).__init__(message)


class CrsApiFailed(RuntimeError):
    def __init__(self, crs, api, error_message):
        self.crs = crs
        self.api = api
        self.error_message = error_message

        message = "CRS({impl}) API '{a}' failed: {msg}".format(
            impl=crs, a=api, msg=error_message
        )
        super(CrsApiFailed, self).__init__(message)


class HotelNightAuditStartedFailure(RuntimeError):
    def __init__(self, crs, error_message):
        self.crs = crs
        self.error_message = error_message

        message = "CRS({impl}) failed: {msg}".format(impl=crs, msg=error_message)
        super(HotelNightAuditStartedFailure, self).__init__(message)


class CrsAuthenticationFailure(RuntimeError):
    def __init__(self, crs, api, error_message):
        self.crs = crs
        self.api = api
        self.error_message = error_message

        message = "Authentication for CRS({impl}) API '{a}' failed: {msg}".format(
            impl=crs, a=api, msg=error_message
        )
        super(CrsAuthenticationFailure, self).__init__(message)


class CrsInMaintenanceWindow(Exception):
    def __init__(self, crs, error_message):
        self.crs = crs
        self.error_message = error_message

        message = "CRS({impl}) is reportedly in maintenance window: {msg}".format(
            impl=crs, msg=error_message
        )
        super(CrsInMaintenanceWindow, self).__init__(message)


class CrsUnknownError(Exception):
    def __init__(self, crs, api, error_message):
        self.crs = crs
        self.api = api
        self.error_message = error_message

        message = "Error running CRS({impl}) API '{a}': {msg}".format(
            impl=crs, a=api, msg=error_message
        )
        super(CrsUnknownError, self).__init__(message)


class CrsNonRetryableError(Exception):
    def __init__(self, crs, api, error_message):
        self.crs = crs
        self.api = api
        self.error_message = error_message

        message = "Non-retryable error running CRS({impl}) API '{a}': {msg}".format(
            impl=crs, a=api, msg=error_message
        )
        super(CrsNonRetryableError, self).__init__(message)


class CounterCredDoesNotExist(Exception):
    pass
