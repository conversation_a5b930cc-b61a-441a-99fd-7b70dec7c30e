import datetime
import json
import logging

import requests
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from core.common.integrations.tax.constants import ResellerTax
from core.common.utils.date import get_dates_between_dates
from core.hotel.services.hotel import GetHotel
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)

logger = logging.getLogger(__name__)


class TreeboTaxService(object):
    TAX_HOST = ServiceRegistryClient.get_service_url(
        ServiceEndPointNames.TAX_SERVICE_URL
    )

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        if not headers.get("X-Tenant-Id"):
            headers["X-Tenant-Id"] = TenantClient.get_default_tenant()
        return headers

    def _get_tax(self, tax_request, hotel_id):
        """
        Gets the tax details for given room type for given hotels, room config and dates in the dto
        Args:
            tax_request_dto: tax request dto

        Returns: json for tax details for given room type and hotels
        """
        try:
            logger.info(f"Requesting Tax with payload: {tax_request}")
            headers = {"Content-type": "application/json"}
            base_url = f"{self.TAX_HOST}/tax/v5/calculate_tax"
            tax_details_response = requests.post(
                base_url, data=json.dumps(tax_request), headers=self.get_headers()
            )
            response_data = tax_details_response.json()
            logger.info(f"Received Tax Response DATA: {response_data}")
            tax_details_response.raise_for_status()
            return response_data
        except Exception as e:
            logger.exception(
                f"error occurred while getting tax details for hotel id: {hotel_id}: {str(e)}"
            )
            raise

    def get_line_item_with_new_charge(self, line_items_json, hotel_id, gstin):
        tax_request_payload = self._tax_request_payload(
            line_items_json, hotel_id, gstin
        )
        tax = self._get_tax(tax_request_payload, hotel_id)
        return tax

    def _tax_request_payload(self, line_items_json, hotel_id, gstin):
        products = []
        for line_item in line_items_json:
            dates = get_dates_between_dates(
                (line_item["charge"]["applicable_date"] + datetime.timedelta(days=1)),
                (line_item["charge"]["applicable_date"]),
            )
            charges = [line_item["charge"]]
            occupancy = len(line_item["customer"]) if line_item["customer"] else None
            for i, charge in enumerate(charges):
                # Reseller Constant will need to deprecated when we get tax_code in each case
                charge_type = (
                    charge["tax_code"]
                    if charge["tax_code"]
                    else ResellerTax.get(charge["name"].strip())
                )
                is_sez = False
                has_buyer_provided_lut = True
                if charge_type:
                    attributes = [
                        {"key": "hotel_id", "value": str(hotel_id)},
                        {
                            "key": "seller_has_lut",
                            "value": str(self.has_hotel_provided_lut(hotel_id)),
                        },
                        {"key": "buyer_has_lut", "value": str(has_buyer_provided_lut)},
                        {"key": "is_buyer_in_sez", "value": str(is_sez)},
                        {"key": "buyer_gstin", "value": gstin},
                    ]
                    prices = []
                    for date_index, date in enumerate(dates):
                        price = {
                            "date": date.strftime("%Y-%m-%d"),
                            "pretax_price": abs(float(charge["pre_tax"])),
                            "index": str(date_index),
                        }
                        prices.append(price)
                        charge.update(
                            {"sku_index": str(i), "price_index": str(date_index)}
                        )

                    products.append(
                        {
                            "attributes": attributes,
                            "prices": prices,
                            "category_id": charge_type,
                            "index": str(i),
                        }
                    )

                else:
                    logger.warning(
                        f"Not getting tax for charge type {charge_type}, don't "
                        "have mapping between tax service and "
                        "reseller tax"
                    )

        return {"skus": products}

    @staticmethod
    def has_hotel_provided_lut(hotel_id):
        hotel_details = GetHotel(hotel_id=hotel_id).get_hotel()
        return hotel_details.has_lut
