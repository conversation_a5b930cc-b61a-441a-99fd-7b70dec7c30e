import os
from argparse import ArgumentParser

from reseller import app


def main():
    parser = ArgumentParser()

    parser.add_argument("-i", '--host',
                        help="Host url, e.g.: example.com, 127.0.0.1",
                        default='127.0.0.1',
                        nargs='?')

    parser.add_argument("-p", "--port",
                        help="port, e.g.:5000",
                        default='5000',
                        nargs='?')

    args = parser.parse_args()

    os.environ['FLASK_APP'] = app.name
    os.environ['FLASK_ENV'] = 'development'
    app.run(host=args.host, port=args.port)
    # app.run(host=args.host, port=args.port, ssl_context='adhoc')


if __name__ == '__main__':
    main()
