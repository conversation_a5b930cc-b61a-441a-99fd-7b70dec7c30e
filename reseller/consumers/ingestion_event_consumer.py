import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from core.notification import Slack, constants, notify
from reseller.infrastructure.consumers.base_rmq_consumer import BaseRMQConsumer
from reseller.infrastructure.consumers.consumer_config import (
    IngestionEventConsumerConfig,
)
from reseller.middlewares.common_middlewares import consumer_middleware
from reseller.services.ingestion import ingestion_audit
from reseller.services.ingestion.ingestion_service import IngestionService

logger = logging.getLogger(__name__)


class IngestionEventConsumer(BaseRMQConsumer):

    dead_letter_queue_name = "reseller_dead_letter_ingestion_queue"
    declare_exchange_if_not_exists = True
    enable_delayed_retries = True
    override_existing_queue = True
    delay_in_seconds = 600

    max_retries = 3

    def __init__(
        self,
        ingestion_service: IngestionService,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(IngestionEventConsumerConfig(tenant_id))
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.ingestion_service: IngestionService = ingestion_service

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.test_request_context():
            body = self.parse_body(body)

            resource_type = body.get("resource_type")
            resource_id = body.get("resource_id")
            message_id = body.get("message_id")

            logger.info(
                f"Ingestion event received message_id: {message_id},"
                f" resource_type: {resource_type}, resource_id: {resource_id}"
            )
            self.ingestion_service.ingest(resource_type, resource_id)
            logger.info(f"Message acknowledged: {message_id}")

    def notify_permanent_failure(self, body, error_message):
        try:
            body = self.parse_body(body)
            resource_type = body.get("resource_type")
            resource_id = body.get("resource_id")
            message_id = body.get("message_id")
            msg = f"Ingestion failed for {resource_type} : {resource_id} : {error_message}. Base event {message_id}"

            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
            ingestion_audit.record_ingestion_failure(resource_id, resource_type, msg)
        except Exception as e:
            logger.exception(e)
