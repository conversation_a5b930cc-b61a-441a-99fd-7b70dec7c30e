import json
import logging
from functools import partial

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from core.notification import Slack, constants, notify
from reseller.infrastructure.consumers.base_rmq_consumer import BaseRMQConsumer
from reseller.infrastructure.consumers.consumer_config import CRSConfig
from reseller.middlewares.common_middlewares import consumer_middleware
from reseller.services.ingestion import ingestion_audit
from reseller.services.ingestion.ingestion_event_publisher import (
    IngestionEventPublisher,
)

logger = logging.getLogger(__name__)


class CrsServiceConsumer(BaseRMQConsumer):

    declare_exchange_if_not_exists = True
    enable_delayed_retries = True
    override_existing_queue = True
    delay_in_seconds = 600

    max_retries = 3

    def __init__(
        self,
        ingestion_event_publisher: IngestionEventPublisher,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(CRSConfig(tenant_id))
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.ingestion_event_publisher: IngestionEventPublisher = (
            ingestion_event_publisher
        )

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.test_request_context():
            body = self.parse_body(body)

            consumer_event = body.get("events")
            message_id = body.get("message_id")

            logger.info(f"Event received with message_id: {message_id}")
            # single integration event can have multiple entities
            # queuing this and executing this one by one can reduce in-efficient retries
            # if not ON ingestion failure of any one of the entity, all the entities will be retried
            self.enqueue_ingestion_events(consumer_event, message_id)
            logger.info(f"Message acknowledged: {message_id}")

    def enqueue_ingestion_events(self, events, message_id):
        self.apply_action_on_events(
            events, message_id, self.ingestion_event_publisher.publish
        )
        logger.info(
            f"Event {message_id} is published to reseller exchange for ingestion"
        )

    @staticmethod
    def apply_action_on_events(events, message_id, action):
        published_event_id = 0
        for event in events:
            resource_type = event["entity_name"]
            resource_id = None

            if resource_type == "booking":
                resource_id = event["payload"]["booking_id"]
            elif (
                resource_type == "invoice"
                and event["payload"]["status"] != "preview"
                and event["payload"]["issued_to_type"] == "customer"
            ):
                resource_id = event["payload"]["invoice_id"]
            elif resource_type == "credit_note":
                resource_id = event["payload"]["credit_note_id"]

            if resource_id:
                published_event_id += 1
                action(
                    resource_type=resource_type,
                    resource_id=resource_id,
                    message_id=f"{message_id}-{published_event_id}",
                    routing_key="ingestion.#",
                )

    def publish_for_ingestion(self, **kwargs):
        self.ingestion_event_publisher.publish(
            resource_type=kwargs["resource_type"],
            resource_id=kwargs["resource_id"],
            message_id=kwargs["message_id"],
            routing_key="ingestion.#",
        )

    @staticmethod
    def record_failure(error_description=None, **kwargs):
        ingestion_audit.record_ingestion_failure(
            resource_type=kwargs["resource_type"],
            resource_id=kwargs["resource_id"],
            error_description=error_description,
        )

    def notify_permanent_failure(self, body, error_message):
        try:
            body = self.parse_body(body)
            consumer_event = body.get("events")
            message_id = body.get("message_id")
            msg = (
                f"Failed to enqueue integration events on reseller"
                f" exchange err {error_message} {repr(body)}"
            )
            notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
            self.apply_action_on_events(
                consumer_event,
                message_id,
                partial(self.record_failure, error_message=error_message),
            )
        except Exception as e:
            logger.exception(e)
