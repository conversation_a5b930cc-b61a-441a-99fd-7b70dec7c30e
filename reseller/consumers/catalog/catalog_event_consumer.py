import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from reseller.infrastructure.consumers.base_rmq_consumer import BaseRMQConsumer
from reseller.infrastructure.consumers.consumer_config import CatalogConfig
from reseller.middlewares.common_middlewares import consumer_middleware

logger = logging.getLogger(__name__)


class CatalogServiceConsumer(BaseRMQConsumer):
    async_timeout = 600
    RESELLER_RELATIVE_HOTEL_SYNC_URL = "cataloglistener/"
    declare_exchange_if_not_exists = True

    def __init__(
        self, catalog_application_service, tenant_id=TenantClient.get_default_tenant()
    ):
        super().__init__(CatalogConfig(tenant_id))
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.catalog_application_service = catalog_application_service

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.test_request_context():
            try:
                data = self.parse_body(body)
                logger.info("Syncing hotel details with data: {}".format(data))
                self.catalog_application_service.sync_hotel_details(data)
            except Exception as e:
                logger.exception(
                    f"Exception Occurred: {str(e)} while syncing catalog event with data: {data}"
                )
                raise
