import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from reseller.infrastructure.consumers.base_rmq_consumer import BaseRMQConsumer
from reseller.infrastructure.consumers.consumer_config import ResellerReportingConfig
from reseller.middlewares.common_middlewares import consumer_middleware

logger = logging.getLogger(__name__)


class ReportingConsumer(BaseRMQConsumer):
    declare_exchange_if_not_exists = True
    enable_delayed_retries = True
    delay_in_seconds = 300

    def __init__(self, reporting_service, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(ResellerReportingConfig(tenant_id))
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.reporting_service = reporting_service

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.test_request_context():
            try:
                data = self.parse_body(body)
                logger.info("Processing Reporting Event with data: {}".format(data))
                job_name = data.get("job_name")
                job_data = data.get("job_params")
                if job_name == "push_invoice_reports_to_finance_portal":
                    self.reporting_service.push_purchase_report_to_erp(job_data)
                elif job_name == "hotel_invoice_report":
                    self.reporting_service.generate_hotel_invoice_report(job_data)
            except Exception as e:
                logger.exception(
                    f"Exception Occurred: {str(e)} while processing reporting event with data: {data}"
                )
                raise
