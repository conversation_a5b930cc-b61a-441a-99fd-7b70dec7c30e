{% extends 'admin/model/list.html' %}

{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<h3>Bulk Retry</h3>
<div id="ManualIng Ingestion">
    Date: <input id="retryDate" type="date" class="form-control">
    <br>
    <button id="BulkIngestBtn" class="btn btn-primary">Retry</button>
</div>
<div class="loader"></div>
<script>
        $('#BulkIngestBtn').on('click',function(){
          var displayString = "Please fill ";
          var retryDate = $('#retryDate').val().trim()
          var details = {
            "date": retryDate,
          }
          if(details.date==""){
            displayString=displayString.concat("Date | ")
            alert(displayString);
            return;
          }
          $.ajax({
            type: "POST",
            url: "/bulk-generate-hotel-invoice_report/retry",
            data: JSON.stringify(details),
            contentType: 'application/json',
             success : function() {
                alert('Process Completed.');
            }
          });
          alert("Retry process started, will run in background")
          $('#retryDate').val()
        })
</script>
<script>
$( document ).ready(function() {
        $('.ingest-btn').on('click',function(){
         var invoiceNumber = $(this).attr('invoicenumber').trim();
         var entityType = $(this).attr('entitytype').trim();
         var details = {}
          if (entityType=='credit_note'){
                details = {"hotel_credit_note_numbers": [invoiceNumber]}
              }
              else{
                details = {"hotel_invoice_numbers": [invoiceNumber]}
              }
          $.ajax({
            type: "POST",
            beforeSend: function(){
                $('.loader').css("visibility", "visible");
            },
            url: "/hotelinvoice/generate-report",
            data: JSON.stringify(details),
            contentType: 'application/json',
            success : function() {
                alert('Process Completed.');
            },
            error : function() {
                alert('Error Ingesting Hotel Invoices');
            },
            complete: function(){
                 $('.loader').css("visibility", "hidden");
            }
          });
        })
        })
</script>
{{ super() }}

{% endblock %}