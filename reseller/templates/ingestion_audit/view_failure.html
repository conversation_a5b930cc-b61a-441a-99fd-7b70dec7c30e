{% extends 'admin/model/list.html' %}

{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<h3>Bulk Retry</h3>
<div id="ManualIng Ingestion">
    Date: <input id="retryDate" type="date" class="form-control">
    <br>
    <button id="BulkIngestBtn" class="btn btn-primary">Retry</button>
</div>
<div class="loader"></div>
<script>
        $('#BulkIngestBtn').on('click',function(){
          var displayString = "Please fill ";
          var retryDate = $('#retryDate').val().trim()
          var details = {
            "date": retryDate,
          }
          if(details.date==""){
            displayString=displayString.concat("Date | ")
            alert(displayString);
            return;
          }
          $.ajax({
            type: "POST",
            url: "/bulk-ingestion/retry",
            data: JSON.stringify(details),
            contentType: 'application/json',
          });
          alert("Retry process started, will run in background")
          $('#retryDate').val()
        })
</script>
<script>
$( document ).ready(function() {
        $('.ingest-btn').on('click',function(){
         var resourceId = $(this).attr('resourceid').trim();
         var resourceType = $(this).attr('resourcetype').trim();
          var details = {
            "resource_id": resourceId,
            "resource_type": resourceType
          }
          $.ajax({
            type: "POST",
            beforeSend: function(){
                $('.loader').css("visibility", "visible");
            },
            url: "/ingestion/",
            data: JSON.stringify(details),
            contentType: 'application/json',
            success : function() {
                alert('Data for '+details.resource_id+' ingested.');
            },
            error : function() {
                alert('Error Ingesting Data');
            },
            complete: function(){
                 $('.loader').css("visibility", "hidden");
            }
          });
        })
        })
</script>
{{ super() }}
{% endblock %}