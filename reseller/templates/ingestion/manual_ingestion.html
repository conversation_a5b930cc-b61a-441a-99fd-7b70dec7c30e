{% extends 'admin/master.html' %}
{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
</head>
<body>
<h2>Manual Ingestion</h2>
<div id="ManualIng">
    Pms Resource ID: <input id="resourceId" type="text" class="form-control">
    Resource-Type:
    <select id="resource-type">
        <option value="booking">Booking</option>
        <option value="invoice">Customer Invoice</option>
        <option value="credit_note">Customer Credit Note</option>
    </select>
    <br>
    <button id="IngestBtn" class="btn btn-primary">Ingest</button>
</div>
<div class="loader"></div>
    <script>
            var $resourceId = $('#resourceId');
            $('#IngestBtn').on('click',function(){
              var displayString = "Please fill ";
              var details = {
                "resource_id": $resourceId.val().trim(),
                "resource_type": $('#resource-type').val()
              }
              if(details.resource_id=="")displayString=displayString.concat("Pms Resource ID | ")
              if(details.resource_type=="--")displayString=displayString.concat("Resource-Type.")
              if(details.resource_id=="" || details.resource_type=="--")
              {
                alert(displayString);
                return;
              }
              $.ajax({
                type: "POST",
                beforeSend: function(){
                    $('.loader').css("visibility", "visible");
                },
                url: "/ingestion/",
                data: JSON.stringify(details),
                contentType: 'application/json',
                success : function() {
                    alert('Data for the resource '+details.resource_id+' ingested.');
                    $resourceId.val("")
                    $('#resource-type').val("--")
                },
                error : function() {
                    alert('Error Ingesting Data');
                },
                complete: function(){
                     $('.loader').css("visibility", "hidden");
                }
              });
            })
    </script>
</body>
{% endblock %}