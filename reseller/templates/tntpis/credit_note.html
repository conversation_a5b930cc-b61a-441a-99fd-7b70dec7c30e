<!DOCTYPE html>
<html>
  <head>
    <style type="text/css">
      @media print {
        @page {
          margin: 0;
          }
        body{
          -webkit-print-color-adjust: exact;
          }
      }
      .radisson {
        background-color: #ffffff;
        display: block;
        padding: 40px 24px;
        width: 528px;
        margin: 0 auto;
        font-family: Roboto, Helvetica, Arial, sans-serif;
        border: 2px solid #f3f3f3;
      }
      .header {
        font-size: 16px !important;
        font-weight: 700 !important;
      }
      .link {
        color: #0000ff;
        text-decoration: none;
      }
      .logo {
        height: 79px;
        width: 80px;
        margin-bottom: 16px;
      }
      .padding-top--8 {
        padding-top: 8px !important;
      }
      .signature-border {
        border-top: 1px solid #dedede;
      }
      .margin-top--24 {
        margin-top: 24px !important;
      }
      .margin-bottom--24 {
        margin-bottom: 24px !important;
      }
      .text-center {
        text-align: center;
      }
      .text-left {
        text-align: left;
      }
      .text-right {
        text-align: right;
      }
      .text-size__m {
        font-size: 10px;
      }
      .text-size__14 {
        font-size: 14px;
      }
      .margin--0 {
        margin: 0px;
      }
      .margin-bottom--0 {
        margin-bottom: 0px !important;
      }
      .margin-bottom--4 {
        margin-bottom: 4px !important;
      }
      .margin-bottom--8 {
        margin-bottom: 8px !important;
      }
      .margin-bottom--16 {
        margin-bottom: 16px !important;
      }
      .margin-bottom--20 {
        margin-bottom: 20px !important;
      }
      .margin-bottom--24 {
        margin-bottom: 24px !important;
      }
      .margin-bottom--36 {
        margin-bottom: 36px !important;
      }
      .margin-left--44 {
        margin-left: 44px;
      }
      .margin-top--8 {
        margin-top: 8px !important;
      }
      .margin-top--16 {
        margin-top: 16px !important;
      }
      .font-weight--700 {
        font-weight: 700;
      }
      p {
        font-size: 14px;
        font-weight: 400;
        margin: 0;
        color: black;
        line-height: 19px !important;
      }
      .vertical-align-middle {
        vertical-align: middle;
      }
      .key-text {
        color: #727171 !important;
        font-size: 10px !important;
        font-weight: 400 !important;
      }
      .value-text {
        font-size: 10px !important;
        font-weight: 400 !important;
        white-space: normal;
        overflow-wrap: anywhere;
      }
      .width--100 {
        width: 100%;
      }
      .divider {
        border: 1px solid #dedede;
        margin: 16px 0px;
      }
      td {
        vertical-align: top;
      }
    </style>
  </head>
  <body class="radisson">
    <table class="width--100">
      <tr class="width--100">
        <td style="width: 25%" class="vertical-align-middle">
          <img
            src="https://images.treebohotels.com/images/vortex/ParkInn_and_suites_Logo_White_and_Black.png"
            class="logo margin-bottom--24"
            alt="ParkInn_and_suites"
          />
        </td>
        <td style="width: 55%" class="vertical-align-middle">
          <p class="text-center">Tax Invoice</p>
        </td>
        <td style="width: 20%" class="vertical-align-middle">
          <p class="text-left key-text margin--0">Property Tax ID</p>
          <p class="text-left value-text margin--0">{{ hotel_credit_note.hotel.gst_details.gstin_number }}</p>
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 88px !important"></td>
        <td class="key-text">
          <p class="header text-center margin-bottom--8 margin-left--44">
            Copy of Credit Note
          </p>
          <p class="text-center text-size__m margin-bottom--8 margin-left--44">
            {{ hotel_credit_note.gst_details.legal_name|title }}
          </p>
        </td>
        <td style="width: 88px !important" class="value-text">
          {% if hotel_credit_note.qr_code %}
            <img
            class="margin-bottom--20"
            src="https://quickchart.treebo.com/chart?cht=qr&chs=500x500&chl={{ hotel_credit_note.qr_code }}"
            alt="QR Code"
            width="88px"
            height="88px"
            />
         {% endif %}

        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr>
        <td style="width: 10%" class="vertical-align-middle">
          <p class="text-left key-text margin-bottom--4">Bill No.</p>
        </td>
        <td style="width: 90%" class="vertical-align-middle">
          <p class="text-left value-text margin--0 margin-bottom--4">
            {{ hotel_credit_note.credit_note_number }}
          </p>
        </td>
      </tr>
    </table>
    <table class="width--100">
      <tr>
        <td style="width: 10%" class="vertical-align-middle">
          <p class="text-left key-text margin-bottom--4">Printed on</p>
        </td>
        <td style="width: 90%" class="vertical-align-middle">
          <p class="text-left value-text margin--0 margin-bottom--4">
            {{ printed_on.strftime('%b %d, %Y') }}
          </p>
        </td>
      </tr>
    </table>

    <div class="divider margin-top--8"></div>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">Guest Name</p>
        </td>
        <td style="width: 35%" class="value-text">
          <p class="value-text margin-bottom--4">
            {{ booking_owner }}
          </p>
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Room No.</p>
        </td>
        <td style="width: 33%" class="value-text">
          <p class="value-text margin-bottom--4">
            {% for line_item in hotel_credit_note.line_items %}
              {{ line_item.room.number }}
              {% if not loop.last %}, {% endif %}
            {% endfor %}
          </p>
        </td>
      </tr>
    </table>


    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">Membership</p>
        </td>
        <td style="width: 35%" class="value-text">
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Rate</p>
        </td>
        <td style="width: 33%" class="value-text">
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">Travel Agent</p>
        </td>
        <td style="width: 35%" class="value-text">
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Guests</p>
        </td>
        <td style="width: 33%" class="value-text">
          <p class="value-text margin-bottom--4">
          </p>
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">Company</p>
        </td>
        <td style="width: 35%" class="value-text">
          <p class="value-text margin-bottom--4">{{ hotel_credit_note.gst_details.legal_name|title }}</p>
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Arrival</p>
        </td>
        <td style="width: 33%" class="value-text">
          <p class="value-text margin-bottom--4">
            {% if hotel_invoice_reference.check_in %}
              {{ (hotel_invoice_reference.check_in|localize_to_ist).strftime("%b %d, %Y %I:%M %p") }}
            {% endif %}
          </p>
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">GSTIN</p>
        </td>
        <td style="width: 35%" class="value-text">
          <p class="value-text margin-bottom--4">
            {{ hotel_credit_note.gst_details.gstin_number }}
          </p>
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Departure</p>
        </td>
        <td style="width: 33%" class="value-text">
          <p class="value-text margin-bottom--4">
            {% if hotel_invoice_reference.check_out %}
              {{ (hotel_invoice_reference.check_out|localize_to_ist).strftime("%b %d, %Y %I:%M %p") }}
            {% endif %}
          </p>
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--4">Billing</p>
        </td>
        <td style="width: 35%" class="value-text">
        </td>
        <td style="width: 17%" class="key-text">
          <p class="key-text margin-bottom--4">Confirmation No.</p>
        </td>
        <td style="width: 33%">
          <p class="value-text margin-bottom--4">
            {{ hotel_credit_note.order_id }}
          </p>
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 15%" class="key-text">
          <p class="key-text margin-bottom--24">Address</p>
        </td>
        <td style="width: 35%" class="value-text">
          <p class="value-text margin-bottom--24">
            {{ hotel_credit_note.gst_details.address.field1 }}
            {% if hotel_credit_note.gst_details.address.field2 %}, {{ hotel_credit_note.gst_details.address.field2 }}{% endif %},
            {{ hotel_credit_note.gst_details.address.city }},
            {{ hotel_credit_note.gst_details.address.state }}
          </p>
        </td>
        <td style="width: 50%" class="key-text">
        </td>
      </tr>
    </table>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 20%" class="key-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            Date
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            Description
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            Reference
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            Debit
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            Credit
          </p>
        </td>
      </tr>
    </table>
    <div class="divider margin-top--8 margin-bottom--8"></div>


{% for customers, room, charges in line_items %}
  {% for charge in charges %}
    <table class="width--100">
      <tr class="width--100">
        <td style="width: 20%" class="key-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ charge.applicable_date.strftime('%Y-%m-%d') }}
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ charge.name }} - {{room.number}} ({{room.room_type_name}})
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ charge.reference }}
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ charge.pre_tax }}
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8"></p>
        </td>
      </tr>

      {% for tax in charge.tax_breakup %}
            <tr class="width--100">
        <td style="width: 20%" class="key-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ charge.applicable_date.strftime('%Y-%m-%d') }}
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ tax.code }} - {{ charge.name }} {{ tax.percent}}%
          </p>
        </td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{ tax.amount }}
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8"></p>
        </td>
      </tr>
      {% endfor %}

    </table>
  {% endfor %}
{% endfor %}

    <div class="divider margin-top--8 margin-bottom--8"></div>
    <table class="width--100">
      <tr class="width--100">
        <td style="width: 45%" class="key-text"></td>
        <td style="width: 25%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            Total INR
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--8">
            {{totals.post_tax}}
          </p>
        </td>
        <td style="width: 15%" class="value-text">
          <p
            class="text-left font-weight--400 text-size__m margin-bottom--8"
          ></p>
        </td>
      </tr>
    </table>

    <div class="divider margin-top--8 margin-bottom--8"></div>
    <table class="width--100">
      <tr class="width--100">
        <td style="width: 30%" class="key-text">
          <p class="text-left font-weight--700 text-size__m margin-bottom--0">
            HSN/SAC CODE
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p class="text-center font-weight--700 text-size__m margin-bottom--0">
            SALES
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p class="text-center font-weight--700 text-size__m margin-bottom--0">
            CGST
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p class="text-center font-weight--700 text-size__m margin-bottom--0">
            SGST
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p class="text-center font-weight--700 text-size__m margin-bottom--0">
            IGST
          </p>
        </td>
      </tr>
    </table>
{% for code, charge_details in tax_code_to_charge_details.items() %}
    <div class="divider margin-top--8 margin-bottom--8"></div>
    <table class="width--100">
      <tr class="width--100">
        <td style="width: 30%" class="key-text">
          <p class="text-left font-weight--400 text-size__m margin-bottom--24">
            {{ code }}
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p
            class="text-center font-weight--400 text-size__m margin-bottom--24"
          >
            {{ charge_details["sales_value"] }}
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p
            class="text-center font-weight--400 text-size__m margin-bottom--24"
          >
            {{ charge_details["CGST"] }}
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p
            class="text-center font-weight--400 text-size__m margin-bottom--24"
          >
            {{ charge_details["SGST"] }}
          </p>
        </td>
        <td style="width: 17.5%" class="value-text">
          <p
            class="text-center font-weight--400 text-size__m margin-bottom--24"
          >
            {{ charge_details["IGST"] }}
          </p>
        </td>
      </tr>
    </table>
  {% endfor %}

    <p class="margin-bottom--4 font-weight--400 text-size__m margin-top--16">
      Cheque/DD may be prepared in favor of
      "{{hotel_credit_note.hotel.gst_details.legal_name}}".
    </p>
    <p class="margin-bottom--36 font-weight--400 text-size__m">
      All bill payable on presentation. I agree that I am responsible for full
      payment of this bill in the event of it not being paid by the
      company/organisation or person indicated
    </p>

    <table class="width--100">
      <tr class="width--100">
        <td style="width: 50%">
          <p
            class="text-size__m text-left margin-bottom--16 font-weight--400 margin-top--8"
          >
            <span class="signature-border padding-top--8"
              >Cashier Signature</span
            >
          </p>
        </td>
        <td style="width: 50%">
          <p
            class="text-size__m text-right margin-bottom--16 font-weight--400 margin-top--8"
          >
            <span class="signature-border padding-top--8">Guest Signature</span>
          </p>
        </td>
      </tr>
    </table>

    <p class="key-text margin-bottom--2">Check-out by</p>
    <p class="value-text margin--0">{{printed_by}}</p>
    <div class="divider margin-top--24 margin-bottom--24"></div>

    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      {{ hotel_credit_note.hotel.name|title }}
    </p>
    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      {{ hotel_credit_note.hotel.gst_details.address.field1 }}
      {% if hotel_credit_note.hotel.gst_details.address.field2 %}
      , {{ hotel_credit_note.hotel.gst_details.address.field2 }}
      {% endif %}
      , {{ hotel_credit_note.hotel.gst_details.address.city }},
      {{ hotel_credit_note.hotel.gst_details.address.state }} - {{ hotel_credit_note.hotel.gst_details.address.pincode }}
    </p>
    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      Phone:
      {% if hotel_credit_note.hotel.phone_number %}
        <a href="tel:{{ hotel_credit_note.hotel.phone_number }}" class="link text-size__m">
          {{ hotel_credit_note.hotel.phone_number }}
        </a>
      {% else %}
        &nbsp;
      {% endif %}
    </p>

    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      Email:
      <a href="mailto:{{hotel_credit_note.hotel.email}}" class="link text-size__m">
        {{ hotel_credit_note.hotel.email }}
      </a>
    </p>
    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      Web:
      <a href="https://www.radissonhotels.com/india" class="link text-size__m"
        >www.radissonhotels.com/india</a
      >
    </p>
    <p class="text-center text-size__m font-weight--400 margin-bottom--0">
      CIN No.: , GST No: {{ hotel_credit_note.hotel.gst_details.gstin_number }}
    </p>
  </body>
</html>
