{% extends 'admin/master.html' %}
 {% block body %}
 <style>
     #HotelIds {
         width: 1063px;
     }
     #helptext {
         font-size: 70%;
         margin-left: 93px;
         margin-top: -11px;
     }
         .loader {
       visibility: hidden;
       border: 4px solid #f3f3f3;
       border-top: 4px solid #3498db;
       border-radius: 50%;
       width: 60px;
       height: 60px;
       margin: auto;
       animation: spin 2s linear infinite;
     }

     @keyframes spin {
       0% { transform: rotate(0deg); }
       100% { transform: rotate(360deg); }
     }
 </style>
 <head>
     <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
     <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
 </head>
 <body>
     <h2>Send Monthly Report</h2>
     <div id="EmailMonthlyReport">
         CS Hotel IDs: <input id="HotelIds" type="text" placeholder="Comma-Seperated 7-digit CS-Hotel-Ids (max 10)" class="form-control"><br>
         <span class="help-block" id="helptext">For Example : 0061719,567432,.....</span>
         Month/Year <input id="MonthYear" type="month" class="form-control">
         <br>
         <button id="SendBtn" class="btn btn-primary">Send</button>
     </div>
     <div class="loader"></div>
     <div class="modal fade" id="modalFade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
         <div class="modal-dialog modal-lg">
             <div class="modal-content">
                 <div class="modal-header">
                     <button type="button" class="close" data-dismiss="modal">Close</button>
                     <h4 class="modal-title" id="modalTitle"> Result </h4>
                 </div>
                 <div class="modal-body" id="modalBody" style="overflow-x: scroll;"></div>
             </div>
         </div>
     </div>
     <script>
          var hotelIds = $('#HotelIds');
          var monthYear = $('#MonthYear');
          $('#SendBtn').on('click',function(){
               var displayString = "Please fill ";
               if(hotelIds.val()=="")displayString = displayString.concat("Hotel-IDs | ")
               if(monthYear.val()=="")displayString = displayString.concat("Month/Year | ")
               if(hotelIds.val()=="" || monthYear.val()=="")
               {
                 alert(displayString);
                 return;
               }
               var value = monthYear.val().split('-')
               var month = value[1]
               var year = value[0]
               var details = {
                   "hotel_ids":hotelIds.val().split(',').map(function(hotelId) {
                                                return hotelId.trim();
                                }),
                   "month": month,
                   "year": year
               }
               $.ajax({
                 type: "POST",
                 beforeSend: function(){
                     $('.loader').css("visibility", "visible");
                 },
                 url: "/hotelinvoice/dispatch-monthly-invoice-reports",
                 data: JSON.stringify(details),
                 contentType: 'application/json',
                 success : function(data) {
                     if(data.failed_hotelIds)
                     {
                         $("#modalTitle").html("Failed");
                         $("#modalBody").html(data.msg+data.failed_hotelIds);
                         $("#modalFade").modal('show');
                     }
                     else
                     {
                         $("#modalTitle").html("Success");
                         $("#modalBody").html(data.msg);
                         $("#modalFade").modal('show');
                     }
                     hotelIds.val("")
                     monthYear.val("")
                 },
                 error : function(data) {
                     var msg = '';
                     if(data.status == 500)msg=data.responseJSON.message
                     else msg = "Error sending invoices"
                     alert(msg);
                 },
                 complete: function(){
                      $('.loader').css("visibility", "hidden");
                 }
               });
          })
     </script>
 </body>
{% endblock %}