<div class="table-wrapper">
    <table class="charge-table">
        <tr class="charge-table-header charge-table-row">
            <th class="font-11" style="width: 100px">GUEST DETAILS</th>
            <th colspan="11">
                <table class="inner-table">
                    <tr>
                        <th class="font-11 inner-cell">DATE</th>
                        <th class="font-11 inner-cell">SERVICES</th>

                        <th class="font-11 inner-cell">HSN/SAC</th>
                        <th class="font-11 inner-cell">PRICE</th>
                        {% for tax_code in totals.all_tax_codes %}
                        <th class="font-11 inner-cell">{{tax_code}}</th>
                        {% endfor %}
                        <th class="font-11 inner-cell">TOTAL</th>
                    </tr>
                </table>
            </th>
        </tr>

        {% for customers, room, charges in line_items %}
        <tr class="charge-table-row">
            <td class="table-row-2 guest-details font-11" colspan="2">
                <table>
                    <tbody>
                    <tr>
                        <td class="bold">{{ customers|join(", ", attribute='name') }}</td>
                    </tr>
                    <tr>
                        <td>{{ room.number }} - {{ room.room_type_name }}</td>
                    </tr>
                    <tr>
                        <td>Occupancy: {{ customers|length }}</td>
                    </tr>
                    </tbody>

                </table>
            </td>
            <td colspan="2">
                <table class="inner-table">
                    {% for charge in charges %}
                    <tr class="table-row-2 font-10">
                        <td class="inner-cell">{{ charge.applicable_date|dateformat_treebo_standard }}</td>
                        <td class="inner-cell">{{ charge.smart_description }}</td>
                        <td class="inner-cell">{{ charge.hsn_code }}</td>
                        <td class="inner-cell">₹{{ charge.pre_tax }}</td>
                        {% for tax_code in totals.all_tax_codes %}
                        {% set tax_detail = charge.get_tax_breakup_for_tax_code(tax_code) %}
                        <td class="inner-cell">₹{{tax_detail.amount}}@{{tax_detail.percent}}%</td>
                        {% endfor %}
                        <td class="inner-cell">₹{{ charge.pre_tax + charge.tax}}</td>
                    </tr>
                    {% endfor %}
                </table>
            </td>
        </tr>
        {% endfor %}

        <tr class="double-border">
            <td colspan="13">
                <div class="row bottom-margin-less">
                    <div class="col s5" style="margin-top: 4px;">
                        <div class="font-10 bold"> TOTAL</div>
                        <div class="font-10 amount-grey">Tax is not payable at reverse charge basis</div>
                    </div>
                    <div class="bold col s6">
                        <div class="col s2 font-10 amount-black align-right central-align">
                            ₹{{totals.pre_tax}}
                        </div>
                        {% for tax_code in totals.all_tax_codes %}
                        <div class="col s2 font-10 amount-black align-right central-align">
                            ₹{{totals.tax_breakup[tax_code]}}
                        </div>
                        {% endfor %}
                        <div class="col s2 font-10 amount-black align-right minor-right-padding central-align">
                            ₹{{totals.post_tax }}
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr class="double-border">
            <td colspan="13">
                <div style="float: right; margin-right: 1em;">
                    <div class="font-10 amount-black">
                        Total Invoice Amount: {{totals.post_tax|convert_payment_amount_to_words}}
                    </div>

                </div>
            </td>
        </tr>

        <tr class="">
            <td colspan="13">
                <div class="row total-payments-box font-8 auth-sign">
                    This is a computer-generated invoice and does not require a signature/stamp.
                </div>
            </td>
        </tr>
    </table>
</div>
