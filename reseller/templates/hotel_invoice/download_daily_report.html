{% extends 'admin/master.html' %}
{% block body %}
<style>
    #HotelIds {
        width: 1063px;
    }
    #helptext {
        font-size: 70%;
        margin-left: 93px;
        margin-top: -11px;
    }
        .loader {
      visibility: hidden;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      margin: auto;
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
</head>
<body>
    <h2>Download Daily Report</h2>
    <div id="DownloadInvoiceReports">
        CS Hotel IDs: <input id="HotelIds" type="text" placeholder="Comma-Seperated 7-digit Hotel-Ids(To download report for all Hotel-Ids type 'all')" class="form-control"><br>
        <span class="help-block" id="helptext">For Example : 0061719,567432,.....</span>
        For Date <input id="forDate" type="date" class="form-control">
        <br>
        <button id="DownloadBtn" class="btn btn-primary">Download</button>
    </div>
    <div class="loader"></div>
    <script>
         var hotelIds = $('#HotelIds');
         var forDate = $('#forDate');
         $('#DownloadBtn').on('click',function(){
              if(forDate.val()=="")
              {
                alert("Please fill for date");
                return;
              }
              if(hotelIds.val()=="")
              {
                alert("Please fill hotelIds");
                return;
              }
              var details = {
                  "hotel_ids":hotelIds.val().split(',').map(function(hotelId) {
                                                return hotelId.trim();
                                }),
                  "for_date":forDate.val()
              }
              $.ajax({
                type: "POST",
                beforeSend: function(){
                    $('.loader').css("visibility", "visible");
                },
                url: "/hotelinvoice/hotel-daily-invoice-csv",
                data: JSON.stringify(details),
                contentType: 'application/json',
                success : function(data) {
                  window.location = data;
                },
                error : function(data) {
                    alert(data);
                },
                complete: function(){
                     $('.loader').css("visibility", "hidden");
                }
              });
         })
    </script>
</body>
{% endblock %}