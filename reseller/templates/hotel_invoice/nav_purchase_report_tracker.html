{% extends 'admin/model/list.html' %}

{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<h3>Manual Push</h3>
<div id="ManualPush">
    Date: <input id="pushRetryDate" type="date" class="form-control">
    Status: <select id="pushStatus" class="form-control"><option value="pending">Pending</option><option value="failed">Failed</option></select>
    <br>
    <button id="BulkPushButton" class="btn btn-primary">Retry</button>
</div>
<div class="loader"></div>
<script>
$( document ).ready(function() {
    $('.filters').find('.remove-filter:last').css("visibility", "hidden"); // hack to remove default filter on this page
    $('#BulkPushButton').on('click',function(){
          var displayString = "Please fill ";
          var retryDate = $('#pushRetryDate').val().trim()
          var status = $('#pushStatus').val().trim()
          var details = {
            "date": retryDate,
            "status": status
          }
          if(details.date==""){
            displayString=displayString.concat("Date | ")
            alert(displayString);
            return;
          }
          $.ajax({
            type: "POST",
            url: "/hotelinvoice/push-invoice-reports-to-nav",
            data: JSON.stringify(details),
            contentType: 'application/json',
          });
          alert("Retry process started, will run in background")
          $('#retryDate').val()
        })

      $('.push-btn').on('click',function(){
         var unique_ref_id = $(this).attr('uniqueRefId').trim();
         var purchase_invoice_report_status = $(this).attr('purchaseReportStatus').trim();
          var details = {
            "unique_ref_id": unique_ref_id,
            "status": purchase_invoice_report_status
          }
          $.ajax({
            type: "POST",
            beforeSend: function(){
                $('.loader').css("visibility", "visible");
            },
            url: "/hotelinvoice/push-invoice-reports-to-nav",
            data: JSON.stringify(details),
            contentType: 'application/json',
            success : function() {
                alert('Purchase report generation/push for '+unique_ref_id+' completed.');
            },
            error : function() {
                alert('Purchase report generation/push for '+unique_ref_id+' failed.');
            },
            complete: function(){
                 $('.loader').css("visibility", "hidden");
            }
          });
        })
})
</script>
{{ super() }}

{% endblock %}
