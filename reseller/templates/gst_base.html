<!DOCTYPE html>
<html lang="en">

<head>
	<meta http-equiv=Content-Type content="text/html; charset=utf-8">

	{% block styles %}
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/0.98.2/css/materialize.min.css" />
	{% endblock %}

	<style type="text/css">
		@media print {
			@page {
				margin: 0;
			}

			body {
				-webkit-print-color-adjust: exact;
			}
		}

		.container {
			border: 1px solid #ccc;
			background-color: #f1f1f1;
			font-size: 12px;
			margin: 0 auto;
			width: 98%;
		}

		.row-1,
		.row-2,
		.row-3,
		.row-4,
		.row-5 {
			padding: 5px;
			margin-bottom: 2px;
		}

		.guest-not-used {
			width: 170px;
			height: 14px;
			font-family: Arial;
			font-size: 12px;
			font-weight: bold;
			font-style: normal;
			font-stretch: normal;
			line-height: normal;
			letter-spacing: normal;
			text-align: left;
			color: #4a4a4a;
		}

		.pos-header {
			font-family: Arial;
			font-size: 20px;
			font-weight: bold;
			font-style: normal;
			font-stretch: normal;
			line-height: normal;
			letter-spacing: normal;
			text-align: left;
			color: #4a4a4a;
		}

		.subscript_epos {
			width: 121px;
			height: 9px;
			opacity: 0.5;
			font-family: Arial;
			font-size: 8px;
			font-weight: normal;
			font-style: normal;
			font-stretch: normal;
			line-height: normal;
			letter-spacing: normal;
			text-align: left;
			color: #4a4a4a;
		}

		.treebo-logo {
			height: 33px;
			width: auto;
		}

		.hotel-details {
			text-align: right;
			margin-bottom: 1px;
			color: #4a4a4a;
			font-size: 10px;
		}

		.hotel-title {
			padding-right: 13px;
			font-weight: bold;
			margin-bottom: 5px;
			font-size: 12px;
			color: black;
		}

		.invoice-heading {
			font-weight: bold;
			font-size: 24px;
			padding-right: 6px;
		}

		.row .col.treebo-hotel-id {
			text-align: right;
			padding: 13px 0.75rem;
		}

		.color-gray {
			color: #686868;
			font-size: 8px;
		}

		.color-green {
			color: #0eb550;
			padding-right: 5px;
		}

		.color-other-gray {
			color: #9e9e9e;
			font-weight: bold;
		}

		.push-right {
			padding-left: 40px !important;
		}

		.push-booking-details-right {
			padding-left: 40px !important;
		}

		.line {
			width: 100%;
			opacity: 0.15;
			background: #979797;
		}

		.invoice-details {
			padding-top: 5px;
			padding-bottom: 20px;
		}

		.gray-invoice-details {
			padding-top: 5px;
			padding-bottom: 20px;
			background: #f7f7f7;
		}

		.align-right {
			text-align: right;
		}

		.align-more-right {
			text-align: right;
			padding-right: 0px !important;
		}

		.align-booking-detail-right {
			text-align: right;
			padding-right: 0px !important;
		}

		.row-3 {
			padding-top: 5px;
		}

		.checkin-row {
			margin-bottom: 5px;
			color: #4a4a4a;
		}

		.top-buffer {
			margin-top: 10px;
		}

		.clear {
			clear: both;
		}

		.checkin-details-right {
			padding-right: 0px !important;
		}

		.checkin-details-left .row {
			margin-bottom: 0px;
		}

		.checkin-details-right .row {
			margin-bottom: 0px;
		}

		.table-row-2 th {
			text-align: center;
		}

		.table-row-2 td {
			text-align: center;
			color: #1b1b1b;
			word-wrap: break-word;
		}

		.table-grey {
			color: #212121;
		}

		.guest-name {
			color: black;
			font-weight: bold;
			white-space: normal;
			word-wrap: break-word;
		}

		.guest-details {
			max-width: 140px;
		}

		.guest-details td {
			padding: 0;
			text-align: left;
		}

		.charges-table {
			background-color: #fff;
		}

		table.inner-table td {
			padding: 10px 2px;
		}

		.container .row.invoice-header-1 {
			padding: 0 30px;
			background-color: #fff;
			margin-left: 1px;
			margin-right: 1px;
			margin-bottom: 10px;
		}

		.container .row.invoice-charges-table {
			padding: 0 30px;
			background-color: #fff;
			margin-left: 1px;
			margin-right: 1px;
		}

		.treebo-hotelogix-id {
			font-size: 9px;
		}

		.row .col.invoice-label {
			padding-right: 0px;
		}

		.charges-heading {
			background-color: #fff;
		}

		.col.table-heading {
			padding: 0px;
			text-align: center;
		}

		.charge-table-header {}

		.inner-table {
			table-layout: fixed;
		}

		.inner-cell {
			width: 50px;
			padding: 0;
			text-align: center;
		}

		.table-wrapper {
			background: #fff;
			padding: 10px;
			margin: 0px 8px;
			margin-bottom: 10px;
		}

		.charge-table-row {
			border-bottom: 1px solid #ddd;
		}

		.double-border {
			border-top: 1px solid #ddd;
			border-bottom: 1px solid #ddd;
		}

		.container .row.invoice-payment-info {
			background: #fff;
			margin: 10px 8px;
		}

		.payment-row-1 {
			padding-left: 25px;
			margin-bottom: 10px;
		}

		.thankyou-row-1 {
			padding-left: 20px;
			margin-bottom: 10px;
		}

		.treebo-gstn-footer {
			margin-bottom: 5px;
		}

		.amount-in-words {
			padding-right: 10px;
			font-size: 8px;
		}

		.payment-details-right {
			padding-right: 10px !important;
		}

		.payment-details-left {
			padding-right: 10px !important;
			padding-left: 25px !important;
		}

		.payment-details-left .row {
			margin-bottom: 0px;
		}

		.payment-details-right .row {
			margin-bottom: 0px;
		}

		.payment-info-block {
			margin: 5px
		}

		.invoice-thanks {
			padding-top: 12px;
		}

		.container .row.invoice-thanks {
			background: #fff;
			margin: 10px 8px;
		}

		.thankyou-note {
			padding: 0px 10px;
			margin-bottom: 10px;
			margin-top: 0px;
		}

		.thankyou-heading {
			padding: 0px 10px;
			margin-bottom: 0px;
		}

		.minor-right-padding {
			padding-right: 15px !important;
		}

		.minor-spacing-right {
			padding-right: 5px;
		}

		.font-8 {
			font-size: 8px;
		}

		.font-9 {
			font-size: 9px;
		}

		.font-10 {
			font-size: 10px;
		}

		.font-11 {
			font-size: 11px;
		}

		.font-12 {
			font-size: 12px;
		}

		.font-14 {
			font-size: 14px;
		}

		.bold {
			font-weight: bold;
		}

		.amount-black {
			color: #222948
		}

		.amount-grey {
			color: #686868;
		}

		.random-black {
			color: #4a4a4a;
		}

		.dark-random-black {
			color: #5c5c5c;
		}

		.row .pdt-6 {
			padding-top: 6px;
		}

		.text-center {
			text-align: center;
		}

		.bottom-margin-less {
			margin-bottom: 5px;
		}

		.central-align {
			padding-top: 10px !important;
		}

		td {
			padding: 3px 2px !important;
		}

		.total-payments-box {
			margin-bottom: 0px;
			padding-top: 5px;
		}

		.stamp_box {
			height: 75px;
			width: 150px;
			object-fit: contain;
			display: block;
		}

		.auth-sign {
			padding-left: 10px;
			clear: left;
			margin-left: 3em;
		}

		.stamp_image {
			position: absolute;
		}
	</style>

</head>

<body>
	{% block content %}{% endblock %}
</body>

</html>