import logging

from core.hotel.constants import HotelCourse
from core.hotel.services.hotel import GetHotel, HotelSellerTypeService
from reseller.exceptions import ResellerCheckFailureException
from reseller.repository.invoice_sequence import get_invoice_sequence_repository

logger = logging.getLogger(__name__)


class ResellerService:
    def get_reseller_hotels(self, datetime):
        hotel_sequences = get_invoice_sequence_repository().find()
        reseller_hotel_ids = set()

        for hotel in hotel_sequences:
            if self.is_reseller(hotel.hotel_id, datetime):
                reseller_hotel_ids.add(hotel.hotel_id)
        return [GetHotel(hotel_id).get_hotel() for hotel_id in reseller_hotel_ids]

    @staticmethod
    def is_reseller(hotel_id, datetime):
        try:
            logger.info(
                f"Checking reseller for hotel {hotel_id} and date {datetime.date()}"
            )
            seller_type = HotelSellerTypeService(hotel_id).get_seller_type(
                datetime.date()
            )
            logger.info(
                f"Got seller type {seller_type} for hotel {hotel_id} and date {datetime.date()}"
            )
            if seller_type and seller_type.lower() == HotelCourse.RESELLER.lower():
                return True
            return False
        except Exception as e:
            msg = f"Failed to check seller type for hotel {hotel_id} and date{datetime.date()} due to {str(e)}"
            logger.error(msg)
            raise ResellerCheckFailureException(msg)
