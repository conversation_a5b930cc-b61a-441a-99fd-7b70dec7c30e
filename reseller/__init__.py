# pylint: disable=invalid-name
import logging
import os
import time
from inspect import getmembers, isfunction

import flask
from flask import Blueprint, Flask, request, send_file
from flask_log_request_id import RequestID, current_request_id
from flask_login import LoginManager
from flask_marshmallow import Marshmallow
from flask_pymongo import PyMongo
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.request_tracing.flask import after_request, before_request
from werkzeug.routing import BaseConverter

from object_registry import finalize_app_initialization
from reseller import monkey_patch_data_class
from reseller.config import log_conf
from reseller.config.base import Config
from reseller.config.log_conf import configure_logging
from reseller.middlewares.common_middlewares import exception_handler

logger = logging.getLogger(__name__)

app = Flask(__name__)


def register_jinja_filters():
    from reseller import jinja_filters

    for name, func in getmembers(jinja_filters):
        if isfunction(func):
            app.jinja_env.filters[name] = func  # pylint: disable=no-member


def register_blueprints():
    # pylint suppression is because of urls import * which should be removed
    # pylint: disable=redefined-outer-name
    from reseller import blueprints

    for _, func in getmembers(blueprints):
        if isinstance(func, Blueprint):
            app.register_blueprint(func)


def register_commands():
    """Register Click commands."""
    print("Registering commands")
    from reseller.cli.setup_and_create_hotel_detail import setup_and_create_hotel_detail

    app.cli.add_command(setup_and_create_hotel_detail)
    from reseller.cli.update_hotel_owner_from_prowl import update_hotel_owner_from_prowl

    app.cli.add_command(update_hotel_owner_from_prowl)
    from reseller.cli.ingestion_event_consumer import start_ingestion_event_consumer

    app.cli.add_command(start_ingestion_event_consumer)
    from reseller.cli.crs_consumer import start_crs_consumer

    app.cli.add_command(start_crs_consumer)
    from reseller.cli.catalog_consumer import start_catalog_consumer

    app.cli.add_command(start_catalog_consumer)
    from reseller.cli.reporting_consumer import start_reporting_consumer

    app.cli.add_command(start_reporting_consumer)


def setup_app_config():
    reseller_settings = os.getenv(
        "RESELLER_SETTINGS", "reseller.config.local.LocalConfig"
    )
    print(f"App starting up using settings: {reseller_settings}")
    app.config.from_object(reseller_settings)


def setup_logging():
    configure_logging(app)


def register_error_handlers():
    app.register_error_handler(Exception, exception_handler)


def setup_request_id():
    RequestID(app)

    def set_request_time():
        request_context.start_time = time.time()

    app.before_request(set_request_time)
    app.before_request(lambda: custom_before_request(flask.request))

    def append_request_id(response):
        response.headers.add("X-REQUEST-ID", current_request_id())
        return response

    app.after_request(append_request_id)
    app.after_request(lambda resp: after_request(resp, request))


def custom_before_request(req):
    before_request(req)
    if "ADMIN_TENANT_ID" in os.environ:
        logger.info(
            "ADMIN_TENANT_ID: %s, found in environment. Overriding that in context.",
            os.environ.get("ADMIN_TENANT_ID"),
        )
        request_context.tenant_id = os.environ.get(
            "ADMIN_TENANT_ID", TenantClient.get_default_tenant()
        )


def setup_pymongo():
    tenant_wise_connection = dict()
    # for tenant in TenantClient.get_active_tenants():
    #     tenant_id = tenant.tenant_id
    #     try:
    #         DB_CREDS = AwsSecretManager.get_db_credentials(tenant_id)
    #         mongo_uri = "mongodb://{0}:{1}@{2}:{3}/{4}?authSource=admin".format(
    #             DB_CREDS["username"],
    #             DB_CREDS["password"],
    #             DB_CREDS["host"],
    #             DB_CREDS["port"],
    #             DB_CREDS["dbname"],
    #         )
    #         tenant_wise_connection[tenant_id] = PyMongo(app, mongo_uri)
    #
    #     except Exception as e:
    #         if os.environ.get("APP_ENV", "local") == "local":
    #             tenant_wise_connection[tenant_id] = PyMongo(
    #                 app, app.config["MONGO_URI"]
    #             )
    #         else:
    #             logger.exception(
    #                 "Can't retrieve DB credentials for tenant_id: %s. Skipping",
    #                 tenant_id,
    #             )
    #         continue
    mongo_uri = os.environ.get("MONGO_URI")
    tenant_wise_connection["treebo"] = PyMongo(app, mongo_uri)
    return tenant_wise_connection


class RegexConverter(BaseConverter):
    def __init__(self, url_map, *items):
        super(RegexConverter, self).__init__(url_map)
        self.regex = items[0]


setup_app_config()
setup_logging()
setup_request_id()
register_error_handlers()
app.url_map.converters["regex"] = RegexConverter
marshmallow = Marshmallow(app)
tenant_wise_mongo_connection = setup_pymongo()
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = "login"
login_manager.session_protection = "strong"

from integrations.crs.treebo_crs.urls import *  # pylint: disable=wildcard-import,wrong-import-position

register_jinja_filters()
register_blueprints()
register_commands()
finalize_app_initialization()
from reseller.admin import setup_admin

setup_admin(app, os.environ.get("ADMIN_TENANT_ID", TenantClient.get_default_tenant()))


@app.route("/export-purchase-report", methods=["GET"])
def get_files():
    from object_registry import locate_instance
    from reseller.reporting.nav_purchase_report.nav_purchase_report_service import (
        NavisionReportingService,
    )

    generator: NavisionReportingService = locate_instance(NavisionReportingService)
    args = request.args
    file_name = generator.export_data_in_csv(args)
    if file_name:
        return send_file(file_name)
    return "Unable to find records"
