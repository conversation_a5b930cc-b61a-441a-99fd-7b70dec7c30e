import logging

from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from reseller import tenant_wise_mongo_connection

logger = logging.getLogger(__name__)


def get_tenant_id():
    return get_current_tenant_id() or TenantClient.get_default_tenant()


def multi_tenant_mongo():
    logger.info("Current tenant id is: {0}".format(get_current_tenant_id()))
    logger.info("Getting db connection for tenant: {0}".format(get_tenant_id()))
    logger.info(
        "Got the tenant connection: {0} for tenant: {1}".format(
            get_tenant_id(), tenant_wise_mongo_connection.get(get_tenant_id())
        )
    )
    return tenant_wise_mongo_connection.get(get_tenant_id())
