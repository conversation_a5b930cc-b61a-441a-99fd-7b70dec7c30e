import logging

from core.common.rmq.producer import Producer

logger = logging.getLogger(__name__)


class AsyncHandler:
    @classmethod
    def publish_work_item(cls, data, headers, routing_key=None, priority=0):
        try:
            if routing_key is None:
                routing_key = "work.{t}".format(t=headers["type"])
            producer = Producer(
                exchange_name="reseller-exchange",
                routing_key=routing_key,
                priority=priority,
            )
            producer.publish(body=data, headers=headers)
        except Exception as e:
            logger.exception(e)
            raise e
