from flask_admin.contrib.pymongo.filters import FilterEqual
from markupsafe import Markup
from wtforms import form

from core.common.constants import IngestionStatus, PurchaseInvoiceReportStatus
from reseller.admin.base.admin_view import BaseAdminView


class NavPurchaseReportTrackerForm(form.Form):
    pass


class NavPurchaseReportTrackerView(BaseAdminView):
    def _push(self, context, model, name):
        purchase_invoice_report_status = model.get("purchase_invoice_report_status", "")
        if purchase_invoice_report_status not in [
            PurchaseInvoiceReportStatus.PENDING,
            PurchaseInvoiceReportStatus.FAILED,
        ]:
            return "N/A"
        unique_ref_id = model["unique_ref_id"]
        _html = f"""
        <button class="push-btn" uniqueRefId={unique_ref_id} purchaseReportStatus={purchase_invoice_report_status} class="btn btn-primary">PUSH</button>
        """
        return Markup(_html)

    column_default_sort = ("invoice_date", True)
    can_create = False
    can_edit = False
    column_display_actions = True
    can_delete = False
    search_case_insensitive = True
    column_list = (
        "hotel_id",
        "invoice_number",
        "unique_ref_id",
        "booking_id",
        "invoice_date",
        "created_on",
        "purchase_invoice_report_status",
        "Push",
    )
    column_sortable_list = ("invoice_date", "created_on")
    column_searchable_list = ("invoice_number", "booking_id", "invoice_date")
    form = NavPurchaseReportTrackerForm
    column_filters = [
        ("invoice_date", str),
        ("created_on", str),
        ("purchase_invoice_report_status", str),
    ]
    column_formatters = {"Push": _push}
    list_template = "hotel_invoice/nav_purchase_report_tracker.html"

    def get_list(
        self,
        page,
        sort_column,
        sort_desc,
        search,
        filters,
        execute=True,
        page_size=None,
    ):
        self._filters.append(FilterEqual("issued_to_type", "issued_to_type"))
        filters.append((len(self._filters) - 1, "issued_to_type", "reseller"))

        return super(NavPurchaseReportTrackerView, self).get_list(
            page, sort_column, sort_desc, search, filters, execute=True, page_size=None
        )

    def purchase_invoice_report_status(self, obj):
        purchase_invoice_report_status = obj.get("purchase_invoice_report_status")
        if purchase_invoice_report_status:
            return (
                "generated/pushed"
                if purchase_invoice_report_status
                == PurchaseInvoiceReportStatus.GENERATED
                else purchase_invoice_report_status
            )
        return None
