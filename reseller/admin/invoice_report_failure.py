from markupsafe import Markup
from wtforms import form

from core.common.constants import InvoiceReportStatus
from reseller.admin.base.admin_view import BaseAdminView


class InvoiceReportFailureForm(form.Form):
    pass


class InvoiceReportFailureView(BaseAdminView):
    def _retry(self, context, model, name):
        if (
            model["status"] in InvoiceReportStatus.SUCCESS_ON_RETRY
            or model.get("invoice_number") is None
        ):
            return "N/A"
        invoice_number = model["invoice_number"]
        entity_type = model["entity_type"]
        _html = f"""
        <button class="ingest-btn" invoicenumber={invoice_number} entitytype={entity_type} class="btn btn-primary">Retry</button>
        """
        return Markup(_html)

    can_create = False
    can_edit = False
    column_display_actions = False
    can_delete = False
    list_template = "invoice_report_audit/invoice_report_failure.html"
    column_list = (
        "hotel_id",
        "process_name",
        "invoice_number",
        "failed_on",
        "status",
        "failure_description",
        "failure_trace",
        "created_at",
        "Retry",
    )
    column_sortable_list = ("hotel_id", "status", "failed_on", "process_name")
    column_searchable_list = (
        "status",
        "invoice_number",
        "hotel_id",
        "failed_on",
        "process_name",
    )
    form = InvoiceReportFailureForm

    column_default_sort = ("failed_on", True)
    search_case_insensitive = True
    column_formatters = {"Retry": _retry}
    column_filters = [
        ("failed_on", str),
        ("process_name", str),
        ("status", str),
        ("hotel_id", str),
    ]
