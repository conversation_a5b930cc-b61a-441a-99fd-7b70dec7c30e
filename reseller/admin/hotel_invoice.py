import logging
from datetime import datetime

from bson import ObjectId
from flask import redirect, request, url_for
from flask_admin import expose
from wtforms import form

from core.common.utils.date import utc_to_ist
from reseller.admin.base.admin_view import BaseAdminView
from reseller.admin.base.row_buttons import RowActionLinkButton
from reseller.constants import IssuedTo
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice

logger = logging.getLogger(__name__)


class HotelInvoiceForm(form.Form):
    pass


class HotelInvoiceView(BaseAdminView):
    can_create = False
    can_edit = False
    column_display_actions = True
    can_delete = False
    search_case_insensitive = True
    column_list = (
        "status",
        "invoice_number",
        "booking_id",
        "order_id",
        "created_at",
        "check_in",
        "check_out",
    )
    column_sortable_list = ("status", "invoice_number", "booking_id")
    column_searchable_list = (
        "status",
        "invoice_number",
        "booking_id",
        "order_id",
        "invoice_id",
    )
    form = HotelInvoiceForm
    column_filters = [
        ("booking_id", str),
        ("invoice_number", str),
        ("created_at", datetime),
        ("check_in", datetime),
        ("check_out", datetime),
    ]
    column_extra_row_actions = [
        RowActionLinkButton(
            "View invoice",
            "hotel_invoicesview.view_html",
            open_in_new_tab=True,
            render_condition=lambda row: row.get("status") in ["locked", "cancelled"],
        )
    ]

    def created_at(self, obj):
        return utc_to_ist(obj["created_at"]).date()

    def check_in(self, obj):
        if obj.get("check_in"):
            return obj["check_in"].date()
        return None

    def check_out(self, obj):
        if obj.get("check_out"):
            return obj["check_out"].date()
        return None

    @expose("/action/view_html")
    def view_html(self, *args, **kwargs):  # pylint: disable=unused-argument
        hotel_invoice = get_hotel_invoice_repository().get(
            _id=ObjectId(request.args["id"])
        )
        if hotel_invoice.issued_to_type == IssuedTo.CUSTOMER.value:
            url = GetHotelInvoice(
                invoice_id=hotel_invoice.invoice_id
            ).get_pms_invoice_signed_url(hotel_invoice)
        else:
            url = url_for(
                "hotelinvoice.html_view", invoice_number=hotel_invoice.invoice_number
            )
        return redirect(url)
