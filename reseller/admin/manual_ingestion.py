from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect

from reseller import app


class ManualIngestionView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development", "staging"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        return self.render("ingestion/manual_ingestion.html")
