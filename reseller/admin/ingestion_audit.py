from datetime import date

from markupsafe import Markup
from wtforms import form

from core.common.constants import IngestionStatus
from reseller.admin.base.admin_view import BaseAdminView


class InvoiceIngestionFailureForm(form.Form):
    pass


class IngestionFailureView(BaseAdminView):
    def _retry(self, context, model, name):
        if model["status"] == IngestionStatus.SUCCESS_ON_RETRY:
            return "N/A"
        hotel_id = model.get("hotel_id", None)
        resource_id = model.get("resource_id", None)
        resource_type = model.get("resource_type", None)
        _html = f"""
        <button class="ingest-btn" resourceid={resource_id} hotelid={hotel_id} resourcetype={resource_type} class="btn btn-primary">Retry</button>
        """
        return Markup(_html)

    column_default_sort = ("failed_on", True)
    can_create = False
    can_edit = False
    column_display_actions = True
    can_delete = False
    search_case_insensitive = True
    column_list = (
        "hotel_id",
        "resource_type",
        "resource_id",
        "failed_on",
        "status",
        "failure_description",
        "created_at",
        "last_retry_at",
        "Retry",
    )
    column_sortable_list = ("status", "failed_on")
    column_searchable_list = (
        "status",
        "resource_type",
        "resource_id",
        "hotel_id",
        "failed_on",
    )
    form = InvoiceIngestionFailureForm
    column_filters = [
        ("failed_on", str),
        ("status", str),
        ("hotel_id", str),
        ("resource_id", str),
        ("resource_type", str),
    ]
    column_formatters = {"Retry": _retry}
    list_template = "ingestion_audit/view_failure.html"
