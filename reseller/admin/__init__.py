from flask import redirect
from flask_admin import Admin, AdminIndexView, expose
from flask_login import current_user

from reseller import app, tenant_wise_mongo_connection
from reseller.admin.customer_invoice import CustomerInvoiceView
from reseller.admin.download_daily_report import DownloadDailyReportView
from reseller.admin.hotel_credit_note import HotelCreditNoteView
from reseller.admin.hotel_customer_invoice import InvoiceMappingView
from reseller.admin.hotel_invoice import HotelInvoiceView
from reseller.admin.ingestion_audit import IngestionFailureView
from reseller.admin.invoice_report_failure import InvoiceReportFailureView
from reseller.admin.manual_ingestion import ManualIngestionView
from reseller.admin.nav_purchase_report_tracker import NavPurchaseReportTrackerView
from reseller.admin.send_daily_report import SendDailyReportView
from reseller.admin.send_hotel_invoice_to_hotel import SendHotelInvoiceView
from reseller.admin.send_monthly_report import SendMonthlyReportView


class ResellerAdminIndexView(AdminIndexView):
    @expose("/")
    def index(self):
        if not current_user.is_authenticated and app.config["ENV"] not in [
            "local",
            "development",
            "staging",
        ]:
            return redirect("/login")

        return super(ResellerAdminIndexView, self).index()


def setup_admin(app, tenant_id):
    admin = Admin(
        app,
        name="Reseller Admin",
        index_view=ResellerAdminIndexView(),
    )  # pylint:  disable=invalid-name
    setup_admin_views(admin, tenant_id)


def setup_admin_views(admin, tenant_id):
    mongo = tenant_wise_mongo_connection[tenant_id]
    admin.add_view(CustomerInvoiceView(mongo.db.customer_invoices, "Customer Invoices"))
    admin.add_view(HotelInvoiceView(mongo.db.hotel_invoices, "Hotel Invoices"))
    admin.add_view(InvoiceMappingView(mongo.db.invoice_mapping, "Mapping"))
    admin.add_view(HotelCreditNoteView(mongo.db.hotel_credit_note, "Hotel Credit Note"))
    admin.add_view(
        IngestionFailureView(mongo.db.ingestion_failure, "Invoice Ingestion Failure")
    )
    admin.add_view(ManualIngestionView(name="Manual Ingestion"))
    admin.add_view(DownloadDailyReportView(name="Download Invoice Report"))
    admin.add_view(SendHotelInvoiceView(name="Send Hotel Invoices"))
    admin.add_view(SendDailyReportView(name="Send Daily Report"))
    admin.add_view(SendMonthlyReportView(name="Send Monthly Report"))
    admin.add_view(
        InvoiceReportFailureView(
            mongo.db.invoice_report_failure, name="Invoice Report Failures"
        )
    )
    admin.add_view(
        NavPurchaseReportTrackerView(
            mongo.db.hotel_invoice_reports, name="Nav Purchase Report Tracker"
        )
    )
