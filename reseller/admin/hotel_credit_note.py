from datetime import datetime

from bson import ObjectId
from flask import redirect, request, url_for
from flask_admin import expose
from wtforms import form

from core.common.utils.date import utc_to_ist
from reseller.admin.base.admin_view import BaseAdminView
from reseller.admin.base.row_buttons import RowActionLinkButton
from reseller.constants import IssuedTo
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote


class HotelCreditNoteForm(form.Form):
    pass


class HotelCreditNoteView(BaseAdminView):
    can_create = False
    can_edit = False
    column_display_actions = True
    can_delete = False
    search_case_insensitive = True
    column_list = (
        "status",
        "reference_invoice_numbers",
        "credit_note_number",
        "booking_id",
        "order_id",
        "created_at",
    )
    column_sortable_list = ("status", "booking_id")
    column_searchable_list = ("status", "booking_id", "order_id")
    form = HotelCreditNoteForm
    column_filters = [
        ("created_at", datetime),
    ]

    column_extra_row_actions = [
        RowActionLinkButton(
            "View Credit Note",
            "hotel_credit_noteview.view_html",
            open_in_new_tab=True,
            render_condition=lambda row: row["status"] == "locked",
        ),
    ]

    def created_at(self, obj):
        return utc_to_ist(obj["created_at"]).date()

    @expose("/action/view_html")
    def view_html(self, *args, **kwargs):  # pylint: disable=unused-argument
        hotel_credit_note = get_hotel_credit_note_repository().get(
            _id=ObjectId(request.args["id"])
        )
        if hotel_credit_note.issued_to_type == IssuedTo.CUSTOMER.value:
            url = GetHotelCreditNote(
                credit_note_id=hotel_credit_note.credit_note_id
            ).get_pms_credit_note_signed_url(hotel_credit_note)
        else:
            url = url_for(
                "hotelinvoice.credit_note_html_view",
                credit_note_number=hotel_credit_note.credit_note_number,
                hotel_id=hotel_credit_note.hotel_id,
            )
        return redirect(url)
