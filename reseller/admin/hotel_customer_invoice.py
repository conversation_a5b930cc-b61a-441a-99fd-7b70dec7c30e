from flask import request
from werkzeug.utils import redirect
from wtforms import form

from reseller import app
from reseller.admin.base.admin_view import BaseAdminView


class InvoiceForm(form.Form):
    pass


class InvoiceMappingView(BaseAdminView):
    can_create = False
    can_edit = False
    column_display_actions = True
    can_delete = False
    list_template = "custom_hotel_customer_invoice_mapping.html"
    column_list = ("customer_invoice_number", "hotel_invoice_id")
    column_sortable_list = ("customer_invoice_number", "hotel_invoice_id")
    column_searchable_list = ("customer_invoice_number", "hotel_invoice_id")
    form = InvoiceForm


@app.route("/show_hotel_invoice")
def show_hotel_invoice():
    try:
        hotel_invoice_id = request.args.get("hotel_invoice_id")
        url = f"/admin/hotel_invoicesview/?search=={hotel_invoice_id}"
        return redirect(url, code=302)
    except Exception as e:
        raise e
