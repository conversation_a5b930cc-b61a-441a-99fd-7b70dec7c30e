from wtforms import form

from reseller.admin.base.admin_view import BaseAdminView


class CustomerInvoiceForm(form.Form):
    pass


class CustomerInvoiceView(BaseAdminView):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = ("invoice_number", "invoice_id", "status", "booking_id")
    column_filters = [("booking_id", str), ("invoice_number", str)]
    column_sortable_list = ("invoice_id", "status", "booking_id")
    column_searchable_list = ("invoice_id", "status", "booking_id")
    form = CustomerInvoiceForm
