from flask import redirect
from flask_admin.contrib.pymongo import ModelView
from flask_login import current_user

from reseller import app
from reseller.admin.base.filter_converter import BaseFilterConverter


class BaseAdminView(ModelView):
    filter_converter = BaseFilterConverter()
    search_case_insensitive = False

    def is_accessible(self):
        if app.config["ENV"] in ["local", "development", "staging"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/login")

    def _get_field_value(self, model, name):
        attr = getattr(self, name, None)
        if attr is not None:
            # take precedence if field is defined on model view itself. simulating django admin.
            if callable(attr):
                return attr(model)
            return attr

        name = name.split(
            "."
        )  # be able to work with names sperated by .(dot notation) for example field1.field2
        val = model
        for field in name:
            if isinstance(val, dict):
                val = super()._get_field_value(val, field)
            elif isinstance(val, list):
                val = val[int(field)]
        return val

    def scaffold_filters(self, attr):
        """Hook to define our custom filters"""
        name, attr = attr[0], attr[1]
        visible_name = self.get_column_name(name)
        flt = self.filter_converter.convert(attr.__name__, name, visible_name)
        return flt

    def _search(self, query, search_term):
        query = super()._search(query, search_term)
        if self.search_case_insensitive:
            query = self._set_case_insenstive_for_regex(query)
        return query

    def _set_case_insenstive_for_regex(self, data):
        """recursively loops over query to figure out where we are using regex and append {"$options": "i"}"""
        if isinstance(data, list):
            for item in data:
                self._set_case_insenstive_for_regex(item)
        if isinstance(data, dict):
            if "$regex" in data:
                data["$options"] = "i"
            else:
                for _, value in data.items():
                    self._set_case_insenstive_for_regex(value)
        return data

    def _create_ajax_loader(self, name, options):
        pass

    def scaffold_form(self):
        pass

    def scaffold_list_form(self, widget=None, validators=None):
        pass

    def scaffold_list_columns(self):
        pass
