from flask_admin.helpers import get_url
from flask_admin.model.template import EndpointLinkRowAction
from markupsafe import Markup


class RowActionLinkButton(EndpointLinkRowAction):
    def __init__(
        self,
        title,
        endpoint,
        id_arg="id",
        url_args=None,
        open_in_new_tab=None,
        render_condition=None,
    ):
        self.target = "_blank" if open_in_new_tab else ""
        if render_condition is not None and not callable(render_condition):
            raise RuntimeError(
                f"render_condition must be a callable..ie. a function "
                f"and not of type {render_condition}"
            )
        self.render_condition = render_condition

        super().__init__(
            icon_class="",
            endpoint=endpoint,
            title=title,
            id_arg=id_arg,
            url_args=url_args,
        )

    def render(self, context, row_id, row):
        if self.render_condition:
            should_render = self.render_condition(row)
            if not should_render:
                return ""

        kwargs = dict(self.url_args) if self.url_args else {}
        kwargs[self.id_arg] = row_id
        url = get_url(self.endpoint, **kwargs)

        html = f"""
        <button>
        <a href={url} target="{self.target}">{self.title}</a>
        </button>
        """
        return Markup(html)
