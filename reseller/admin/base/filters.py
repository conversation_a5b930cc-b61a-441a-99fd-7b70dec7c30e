import datetime
import decimal

from bson import Decimal128
from flask_admin.babel import lazy_gettext
from flask_admin.contrib.pymongo.filters import (
    BasePyMongoFilter,
    FilterEqual,
    FilterGreater,
    FilterNotEqual,
    FilterSmaller,
)
from flask_admin.model import filters

# pylint: disable=abstract-method
from core.common.utils.date import ist_to_utc


class BaseIntFilter(BasePyMongoFilter):
    def apply(self, query, value):
        try:
            value = int(value)
        except ValueError:
            value = 0
        return super().apply(query, value)


# pylint: disable=abstract-method
class BaseFloatFilter(BasePyMongoFilter):
    def apply(self, query, value):
        try:
            value = float(value)
        except ValueError:
            value = 0
        return super().apply(query, value)


# pylint: disable=abstract-method
class BaseDecimalFilter(BasePyMongoFilter):
    def apply(self, query, value):
        try:
            value = Decimal128(value)
        except (ValueError, decimal.InvalidOperation):
            value = 0
        return super().apply(query, value)


class BaseGreater(FilterGreater):
    """Do not use filter greater as it converts everything to float"""

    def apply(self, query, value):
        query.append({self.column: {"$gt": value}})
        return query


class BaseSmaller(FilterSmaller):
    """Do not use filter smaller as it converts everything to float"""

    def apply(self, query, value):
        query.append({self.column: {"$lt": value}})
        return query


class DateEqualsFilter(BasePyMongoFilter, filters.BaseDateFilter):
    def apply(self, query, value):
        min_date_time = ist_to_utc(datetime.datetime.combine(value, datetime.time.min))
        max_date_time = ist_to_utc(datetime.datetime.combine(value, datetime.time.max))
        query.append({self.column: {"$gte": min_date_time, "$lte": max_date_time}})
        return query

    def operation(self):
        return lazy_gettext("equals")


class DateTimeBetweenFilter(BasePyMongoFilter, filters.BaseDateTimeBetweenFilter):
    def __init__(self, column, name, options=None, data_type="datetimerangepicker"):
        super(DateTimeBetweenFilter, self).__init__(
            column, name, options, data_type=data_type
        )

    def apply(self, query, value):
        query.append({self.column: {"$gte": value[0], "$lte": value[1]}})
        return query

    def operation(self):
        return lazy_gettext("between")


class IntEqual(BaseIntFilter, FilterEqual):
    pass


class IntNotEqual(BaseIntFilter, FilterNotEqual):
    pass


class IntGreater(BaseIntFilter, BaseGreater):
    pass


class IntSmaller(BaseIntFilter, BaseSmaller):
    pass


class FloatEqual(BaseFloatFilter, FilterEqual):
    pass


class FloatNotEqual(BaseFloatFilter, FilterNotEqual):
    pass


class FloatGreater(BaseFloatFilter, BaseGreater):
    pass


class FloatSmaller(BaseFloatFilter, BaseSmaller):
    pass


class DecimalEqual(BaseDecimalFilter, FilterEqual):
    pass


class DecimalNotEqual(BaseDecimalFilter, FilterNotEqual):
    pass


class DecimalGreater(BaseDecimalFilter, BaseGreater):
    pass


class DecimalSmaller(BaseDecimalFilter, BaseSmaller):
    pass
