from flask_admin.contrib.pymongo.filters import (
    BooleanEqualFilter,
    BooleanNotEqualFilter,
    FilterEqual,
    FilterGreater,
    FilterLike,
    FilterNotEqual,
    FilterNotLike,
    FilterSmaller,
)
from flask_admin.model import filters

from reseller.admin.base.filters import (
    DateEqualsFilter,
    DateTimeBetweenFilter,
    DecimalEqual,
    DecimalGreater,
    DecimalNotEqual,
    DecimalSmaller,
    FloatEqual,
    FloatGreater,
    FloatNotEqual,
    FloatSmaller,
    IntEqual,
    IntNotEqual,
)


class BaseFilterConverter(filters.BaseFilterConverter):
    int_filters = (IntEqual, IntNotEqual, FilterGreater, FilterSmaller)
    float_filters = (FloatEqual, FloatNotEqual, FloatGreater, FloatSmaller)
    decimal_filters = (DecimalEqual, DecimalNotEqual, DecimalGreater, DecimalSmaller)
    bool_filters = (BooleanEqualFilter, BooleanNotEqualFilter)
    string_filters = (FilterEqual, FilterNotEqual, FilterLike, FilterNotLike)
    date_time_filters = (
        DateEqualsFilter,
        DateTimeBetweenFilter,
    )

    def convert(self, type_name, column, name):
        filter_name = type_name.lower()

        if filter_name in self.converters:
            return self.converters[filter_name](column, name)

        return None

    @filters.convert("int")
    def convert_ints(self, column, name):
        return [f(column, name) for f in self.int_filters]

    @filters.convert("float")
    def convert_floats(self, column, name):
        return [f(column, name) for f in self.float_filters]

    @filters.convert("decimal")
    def convert_decimals(self, column, name):
        return [f(column, name) for f in self.decimal_filters]

    @filters.convert("bool")
    def convert_bools(self, column, name):
        return [f(column, name) for f in self.bool_filters]

    @filters.convert("str")
    def convert_strings(self, column, name):
        return [f(column, name) for f in self.string_filters]

    @filters.convert("datetime")
    def convert_date_times(self, column, name):
        return [f(column, name) for f in self.date_time_filters]
