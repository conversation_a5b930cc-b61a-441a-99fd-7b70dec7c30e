from core.hotel_invoice.data_classes.invoices_to_be_sent import InvoicesToBeSent
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class InvoicesToBeSentRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        invoices_to_be_sent = InvoicesToBeSent(
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            dispatch_date=document["dispatch_date"],
            invoice_id=document["invoice_id"],
            created_at=document["created_at"],
            storage_path=document.get("storage_path", ""),
            entity_type=document.get("entity_type", "hotel_invoice"),
        )
        return invoices_to_be_sent

    def find_invoices_by_ids(self, invoice_ids):
        query = {"invoice_id": {"$in": invoice_ids}}
        invoices = self.find(**query)
        return invoices

    def get_hotel_ids_for_date_range(self, from_date, to_date):
        query = {"dispatch_date": {"$gte": str(from_date), "$lte": str(to_date)}}
        return self.distinct(query, "hotel_id")


def get_invoices_to_be_sent_repository():
    return tenant_wise_repository(InvoicesToBeSentRepository, "invoices_to_be_sent")
