import logging

from core.hotel.data_classes.hotel_detail import HotelDetail
from reseller.repository.base_repository import (
    BaseRepository,
    DocumentDoesNotExist,
    tenant_wise_repository,
)

logger = logging.getLogger(__name__)


class HotelDetailRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        hotel_detail = HotelDetail.from_db_json(document)
        return hotel_detail

    def get_hotel_detail_by_hotel_id(self, hotel_id):
        query = {"hotel_id": hotel_id}
        try:
            return self.get(**query)
        except DocumentDoesNotExist:
            logger.error(f"Hotel Detail not found for hotel_id {hotel_id}")

    def get_multiple_hotel_detail_by_hotel_id(self, hotel_ids):
        query = {"hotel_id": {"$in": hotel_ids}}
        return self.find(**query)

    def create_or_update_hotel_detail(self, hotel_detail):
        query = {"hotel_id": hotel_detail.hotel_id}
        self.update(query=query, dict_or_data_class=hotel_detail, upsert=True)

    def create_or_update_hotel_details(self, hotel_details):
        queries = []
        for hotel_detail in hotel_details:
            query = {"hotel_id": hotel_detail.hotel_id}
            queries.append(query)
        self.bulk_update(queries=queries, data_list=hotel_details)


def get_hotel_detail_repository():
    return tenant_wise_repository(HotelDetailRepository, "hotel_detail")
