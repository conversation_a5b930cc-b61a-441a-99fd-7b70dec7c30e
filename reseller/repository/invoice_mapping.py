from core.invoice_mapping.data_class.invoice_mapping import InvoiceMapping
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class InvoiceMappingRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        return InvoiceMapping(
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            booking_id=document["booking_id"],
            customer_invoice_id=document["customer_invoice_id"],
            customer_invoice_pi_id=document["customer_invoice_pi_id"],
            hotel_invoice_id=document["hotel_invoice_id"],
            hotel_invoice_number=document["hotel_invoice_number"],
            customer_invoice_pi_number=document["customer_invoice_pi_number"],
            customer_invoice_number=document["customer_invoice_number"],
        )

    def get_by_customer_invoice_numbers(self, customer_invoice_numbers):
        query = {"customer_invoice_number": {"$in": customer_invoice_numbers}}
        invoice_mappings = self.find(**query)
        return invoice_mappings

    def get_by_customer_invoice_ids(self, customer_invoice_ids):
        query = {"customer_invoice_id": {"$in": customer_invoice_ids}}
        invoice_mappings = self.find(**query)
        return invoice_mappings

    def get_by_hotel_invoice_numbers(self, hotel_invoice_numbers):
        query = {"hotel_invoice_number": {"$in": hotel_invoice_numbers}}
        invoice_mappings = self.find(**query)
        return invoice_mappings


def get_invoice_mapping_repository():
    return tenant_wise_repository(InvoiceMappingRepository, "invoice_mapping")
