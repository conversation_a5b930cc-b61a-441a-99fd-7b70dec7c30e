import logging

from pymongo.errors import DuplicateKeyError

from core.ingestion.data_classes.ingestion_lock import IngestionLock
from reseller.repository.base_repository import (
    BaseRepository,
    DocumentDoesNotExist,
    tenant_wise_repository,
)

logger = logging.getLogger(__name__)


class IngestionProcessLocksRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        ingestion_lock = IngestionLock.from_json(document)
        return ingestion_lock

    def is_lock_present(self, resource_type, resource_id):
        query = {"resource_type": resource_type, "resource_id": resource_id}
        document = self.find(**query)
        return len(document) > 0

    def remove_lock_if_present(self, resource_type, resource_id):
        query = {"resource_type": resource_type, "resource_id": resource_id}
        document = self.delete(**query)
        return document

    def acquire_lock(self, resource_type, resource_id):
        lock = IngestionLock(resource_type=resource_type, resource_id=resource_id)
        try:
            self.create(lock)
            return True
        except DuplicateKeyError:
            return False


def get_ingestion_process_locks_repository():
    return tenant_wise_repository(IngestionProcessLocksRepository, "ingestion_lock")
