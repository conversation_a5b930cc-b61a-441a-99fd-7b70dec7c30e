from core.common.constants import HsnCodes, IssuedToTypes
from core.common.data_classes.address import Address
from core.common.data_classes.charge import Charge
from core.common.data_classes.customer import Customer
from core.common.data_classes.gst_details import GstDetails
from core.common.data_classes.tax_breakup import TaxBreakup
from core.hotel.data_classes.account_details import AccountDetails
from core.hotel.data_classes.room import Room
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.data_classes.invoice_hotel import InvoiceHotel
from core.hotel_invoice.data_classes.line_item import LineItem
from reseller.repository.base_repository import (
    BaseRepository,
    DocumentNotFoundException,
    tenant_wise_repository,
)
from reseller.services.hotel.get_hotel import GetHotel


class HotelInvoiceRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        hotel_invoice = HotelInvoice(
            invoice_id=document["invoice_id"],
            booking_id=document["booking_id"],
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            invoice_number=document["invoice_number"],
            hotel=self._hotel(document["hotel"], document["created_at"]),
            gst_details=self._gst_details(document["gst_details"]),
            line_items=self._line_items(document),
            status=document["status"],
            source=document["source"],
            order_id=document["order_id"],
            check_in=document["check_in"],
            check_out=document["check_out"],
            issued_to_type=document.get("issued_to_type", IssuedToTypes.RESELLER),
            bill_id=document.get("bill_id", ""),
            invoice_url=document.get("invoice_url", ""),
            invoice_date=document.get("invoice_date", ""),
            is_locked_in_crs=document.get("is_locked_in_crs", False),
            irn=document.get("irn"),
            qr_code=document.get("qr_code"),
            is_einvoice=document.get("is_einvoice"),
            ack_no=document.get("ack_no"),
            acked_on=document.get("acked_on"),
            signed_invoice=document.get("signed_invoice"),
        )
        hotel_invoice.created_at = document["created_at"]
        return hotel_invoice

    def _gst_details(self, gst_details):
        gst_details = GstDetails(
            legal_name=gst_details["legal_name"],
            gstin_number=gst_details["gstin_number"],
            address=Address(**gst_details["address"]),
            has_lut=gst_details.get("has_lut", False),
        )
        return gst_details

    def _line_items(self, document):
        line_items = []
        for item in document.get("line_items", []):
            # blocking booking commission charges to go in hotel invoice
            if item["charge"]["hsn_code"] == HsnCodes.OtherAccommodationServices:
                continue
            charge = self._charge(item["charge"])
            room = self._room(item["room"])
            customers = self._customers(item["customers"])
            line_item = LineItem(
                charge,
                room,
                customers,
            )
            line_items.append(line_item)
        return line_items

    def _charge(self, charge_details):
        charge = Charge(
            uid=charge_details["uid"],
            name=charge_details["name"],
            quantity=charge_details["quantity"],
            applicable_date=charge_details["applicable_date"],
            pre_tax=charge_details["pre_tax"],
            tax=charge_details["tax"],
            hsn_code=charge_details["hsn_code"],
            tax_breakup=[
                TaxBreakup(item["code"], item["amount"], percent=item["percent"])
                for item in charge_details["tax_breakup"]
            ],
            smart_description=charge_details["smart_description"],
            tax_code=charge_details.get("tax_code", ""),
        )
        return charge

    def _room(self, room_details):
        return Room(
            number=room_details["number"],
            room_type_name=room_details["room_type_name"],
        )

    def _customers(self, customers_details):
        def get_name(cus):
            try:
                name = cus["name"]
            except KeyError:
                # to cater for legacy structure
                name = (cus["first_name"], cus["last_name"])
            return name

        customers = [
            Customer(
                uid=cus["uid"],
                name=get_name(cus),
                email=cus.get("email", ""),
                phone_number=cus.get("phone_number", ""),
            )
            for cus in customers_details
        ]
        return customers

    def _hotel(self, hotel_data, invoice_created_date):
        gst_details = self._pick_date_range_specific_gstin(
            hotel_data, invoice_created_date
        )
        account_details = self._account_details(hotel_data["account_details"])
        hotel = InvoiceHotel(
            uid=hotel_data["uid"],
            name=hotel_data["name"],
            email=hotel_data["email"],
            phone_number=hotel_data["phone_number"],
            account_details=account_details,
            gst_details=gst_details,
            legal_state_code=hotel_data["legal_state_code"],
            legal_signature_url=hotel_data["legal_signature_url"],
            navision_code=hotel_data.get("navision_code"),
            pms_id=hotel_data.get("pms_id"),
            msme_number=hotel_data.get("msme_number"),
        )
        return hotel

    def _pick_date_range_specific_gstin(self, hotel_data, invoice_created_date):
        date_range_specific_gstin = GetHotel().extract_gstin_from_invoice_sequence(
            from_date=invoice_created_date,
            to_date=invoice_created_date,
            hotel_id=hotel_data["uid"],
        )
        hotel_data["gst_details"]["gstin_number"] = date_range_specific_gstin
        gst_details = self._gst_details(hotel_data["gst_details"])
        return gst_details

    def _account_details(self, account_details):
        if account_details:
            account_detail = AccountDetails(
                account_name=account_details["account_name"],
                account_number=account_details["account_number"],
                ifsc_code=account_details["ifsc_code"],
                account_type=account_details["account_type"],
                bank_name=account_details["bank_name"],
                pan_number=account_details["pan_number"],
            )
            return account_detail
        return account_details

    def get_hotel_invoice(self, **query):
        documents = self.find(**query)
        if not documents:
            raise DocumentNotFoundException("Invoice Not Found")
        return documents[0]

    def get_invoices_by_ids(self, invoice_ids):
        query = {"invoice_id": {"$in": invoice_ids}}
        return self.find(**query)


def get_hotel_invoice_repository():
    return tenant_wise_repository(HotelInvoiceRepository, "hotel_invoices")
