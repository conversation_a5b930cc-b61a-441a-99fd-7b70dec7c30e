from core.hotel.data_classes.hotel_night_audit import HotelNightAudit
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class HotelNightAuditRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        night_audit = HotelNightAudit(
            hotel_id=document["hotel_id"],
            processed=document["processed"],
            last_night_audit_date=document.get("last_night_audit_date"),
        )
        return night_audit


def get_hotel_night_audit():
    return tenant_wise_repository(HotelNightAuditRepository, "hotel_night_audit")
