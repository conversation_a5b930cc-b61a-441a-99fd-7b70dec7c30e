from core.common.data_classes.address import Address
from core.common.data_classes.credit_note_charge import Charge
from core.common.data_classes.gst_details import GstDetails
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_credit_line_item import LineItem
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.customer_invoice.data_classes.payment import Payment
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class CustomerCreditNoteRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        return CustomerCreditNote(
            booking_id=document["booking_id"],
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            credit_note_id=document["credit_note_id"],
            credit_note_number=document["credit_note_number"],
            gst_details=self._gst_details(document["gst_details"]),
            line_items=self._line_items(document["line_items"]),
            status=document["status"],
            source_created_at=document["source_created_at"],
            reference_invoice_numbers=document["reference_invoice_numbers"],
            issued_by_type=document["issued_by_type"],
            bill_id=document.get("bill_id", ""),
            credit_note_date=document.get("credit_note_date", ""),
        )

    def _gst_details(self, gst_details):
        gst_details = GstDetails(
            legal_name=gst_details["legal_name"],
            gstin_number=gst_details["gstin_number"],
            address=Address(**gst_details["address"]),
            has_lut=gst_details.get("has_lut"),
            is_sez=gst_details.get("is_sez"),
        )
        return gst_details

    def _line_items(self, line_items_data):
        line_items = []
        for item in line_items_data:
            charge = self._charge(item["charge"])
            line_item = LineItem(
                charge,
                room_id=item["room_id"],
                guest_stay_id=item["guest_stay_id"],
            )
            line_items.append(line_item)
        return line_items

    def _payments(self, payments_data):
        return [
            Payment(
                amount=payment_data["amount"],
                payment_date=payment_data["payment_date"],
                description=payment_data["description"],
                payment_mode=payment_data["payment_mode"],
            )
            for payment_data in payments_data
        ]

    def _charge(self, charge_details):
        charge = Charge(
            uid=charge_details["uid"],
            name=charge_details["name"],
            quantity=charge_details["quantity"],
            applicable_date=charge_details["applicable_date"],
            pre_tax=charge_details["pre_tax"],
            tax=charge_details["tax"],
            hsn_code=charge_details["hsn_code"],
            tax_breakup=[
                TaxBreakup(item["code"], item["amount"], percent=item["percent"])
                for item in charge_details["tax_breakup"]
            ],
            smart_description=charge_details["smart_description"],
            tax_code=charge_details.get("tax_code", ""),
        )
        return charge


def get_customer_credit_note_repository():
    return tenant_wise_repository(CustomerCreditNoteRepository, "customer_credit_note")
