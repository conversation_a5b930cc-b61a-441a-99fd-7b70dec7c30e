import logging

from core.common.constants import IngestionStatus
from core.failure_audit.data_classes.ingestion_failure import IngestionFailure
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository

logger = logging.getLogger(__name__)


class IngestionFailureRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        ingestion_failure = IngestionFailure.from_json(document)
        return ingestion_failure

    def get_failure(self, resource_id):
        query = {"resource_id": resource_id}
        document = self.get(**query)
        return document

    def get_failures_for_date(self, date):
        query = {
            "status": {
                "$in": [IngestionStatus.FAILED, IngestionStatus.FAILED_ON_RETRY]
            },
            "failed_on": date,
        }
        return self.find(**query)


def get_ingestion_failure_repository():
    return tenant_wise_repository(IngestionFailureRepository, "ingestion_failure")
