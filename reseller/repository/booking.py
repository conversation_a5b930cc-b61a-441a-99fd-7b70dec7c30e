from core.booking.data_classes.booking import Booking
from core.booking.data_classes.guest_stay import GuestStay
from core.booking.data_classes.room_stay import RoomStay
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class BookingRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        booking = Booking(
            booking_id=document["booking_id"],
            source_created_date=document["source_created_date"],
            owner=document["owner"],
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            pms_id=document["pms_id"],
            rooms=self._rooms(document["rooms"]),
            order_id=document["order_id"],
            status=document["status"],
            source=document.get("source"),
            sub_source=document.get("sub_source"),
        )
        return booking

    def _rooms(self, rooms_data):
        rooms = []

        for room_data in rooms_data:
            guests = self._guests(room_data["guests"])
            room = RoomStay(
                guests=guests,
                room_booking_id=room_data["room_booking_id"],
                type=room_data["type"],
                number=room_data["number"],
                status=room_data["status"],
                pms_id=room_data.get("pms_id"),
            )
            rooms.append(room)
        return rooms

    def _guests(self, guests_data):
        return [
            GuestStay(
                stay_id=guest_data["stay_id"],
                title=guest_data["title"],
                first_name=guest_data["first_name"],
                last_name=guest_data["last_name"],
                stay_start=guest_data["stay_start"],
                stay_end=guest_data["stay_end"],
                actual_stay_start=guest_data.get("actual_stay_start"),
                actual_stay_end=guest_data.get("actual_stay_end"),
            )
            for guest_data in guests_data
        ]


def get_booking_repository():
    return tenant_wise_repository(BookingRepository, "bookings")
