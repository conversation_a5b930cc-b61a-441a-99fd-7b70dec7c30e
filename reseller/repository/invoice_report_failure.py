import logging

from core.common.constants import InvoiceReportStatus
from core.failure_audit.data_classes.invoice_report_failure import InvoiceReportFailure
from reseller.repository.base_repository import (
    BaseRepository,
    DocumentDoesNotExist,
    tenant_wise_repository,
)

logger = logging.getLogger(__name__)


class InvoiceReportFailureRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        invoice_report_failure = InvoiceReportFailure.from_json(document)
        return invoice_report_failure

    def get_failure(self, failed_on, hotel_id=None, invoice_id=None):
        if hotel_id or invoice_id:
            query = {
                "failed_on": failed_on,
                "hotel_id": hotel_id,
                "invoice_id": invoice_id,
            }
            document = self.get(**query)
            return document
        else:
            raise DocumentDoesNotExist

    def get_failures_for_date(self, date):
        query = {
            "status": {
                "$in": [InvoiceReportStatus.FAILED, InvoiceReportStatus.FAILED_ON_RETRY]
            },
            "failed_on": date,
        }
        return self.find(**query)

    def get_failure_from_invoice_number(self, invoice_number):
        if invoice_number:
            query = {
                "invoice_number": invoice_number,
            }
            document = self.get(**query)
            return document
        else:
            raise DocumentDoesNotExist


def get_invoice_report_failure_repository():
    return tenant_wise_repository(
        InvoiceReportFailureRepository, "invoice_report_failure"
    )
