from core.common.constants import InvoiceStatus, IssuedToTypes
from core.hotel_invoice.data_classes.invoice_report import HotelInvoiceReport
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class HotelInvoiceReportRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        hotel_invoice_report = HotelInvoiceReport(
            cs_hotel_id=document.get("cs_hotel_id"),
            hotel_id=document["hotel_id"],
            hotel_trade_name=document["hotel_trade_name"],
            hotel_nav_code=document.get("hotel_nav_code"),
            billed_by_legal_name=document["billed_by_legal_name"],
            billed_by_gstin=document["billed_by_gstin"],
            billed_by_address=document["billed_by_address"],
            billed_by_city=document["billed_by_city"],
            billed_by_state=document["billed_by_state"],
            billed_by_pin_code=document["billed_by_pin_code"],
            group_code=document["group_code"],
            booking_id=document["booking_id"],
            reference_number=document["reference_number"],
            room_booking_code=document["room_booking_code"],
            booking_date=document["booking_date"],
            checkin=document["checkin"],
            checkout=document["checkout"],
            days=document["days"],
            room_number=document["room_number"],
            room_type=document["room_type"],
            occupancy=document["occupancy"],
            guest_names=document["guest_names"],
            invoice_number=document["invoice_number"],
            invoice_date=document["invoice_date"],
            charge_type=document["charge_type"],
            sac_code=document["sac_code"],
            pre_tax_price=document["pre_tax_price"],
            total_amount=document["total_amount"],
            cgst_value=document["cgst_value"],
            cgst_percent=document["cgst_percent"],
            sgst_value=document["sgst_value"],
            sgst_percent=document["sgst_percent"],
            igst_value=document["igst_value"],
            igst_percent=document["igst_percent"],
            flood_cess_value=document.get("flood_cess_value", 0.00),
            flood_cess_percent=document.get("flood_cess_percent", 0.00),
            billed_to_legal_name=document["billed_to_legal_name"],
            billed_to_gstin_number=document["billed_to_gstin_number"],
            billed_to_address=document["billed_to_address"],
            billed_to_city=document["billed_to_city"],
            billed_to_state=document["billed_to_state"],
            billed_to_pin_code=document["billed_to_pin_code"],
            booking_owner_name=document["booking_owner_name"],
            customer_invoice_number=document["customer_invoice_number"],
            source=document["source"],
            sub_source=document.get("sub_source"),
            report_date=document["report_date"],
            pms_type=document["pms_type"],
            status=document["status"],
            issued_to_type=document.get("issued_to_type", IssuedToTypes.RESELLER),
            total_invoice_amount=document.get("total_invoice_amount"),
            original_invoice_number=document.get("original_invoice_number"),
            entity_type=document.get("entity_type"),
            unique_ref_id=document.get("unique_ref_id"),
            purchase_invoice_report_status=document.get(
                "purchase_invoice_report_status"
            ),
            created_on=document.get("created_on"),
        )
        hotel_invoice_report.created_at = document["created_at"]
        return hotel_invoice_report

    def get_reseller_reports_by_invoice_numbers(self, invoice_numbers):
        query = {"invoice_number": {"$in": invoice_numbers}}
        reports = self.find(**query)
        return reports

    def get_reseller_reports_by_booking_id(self, booking_id):
        query = {"booking_id": booking_id}
        reports = self.find(**query)
        return reports

    def get_issued_to_reseller_reports(self, for_date):
        query = {
            "$and": [
                {"$or": [{"report_date": for_date}, {"invoice_date": for_date}]},
                {"issued_to_type": IssuedToTypes.RESELLER},
            ]
        }
        reports = self.find(**query)
        return reports

    def get_issued_to_reseller_reports_by_status(self, for_date, status=None):
        filters = [{"created_on": for_date}, {"issued_to_type": IssuedToTypes.RESELLER}]
        # if status:
        #     filters.append({"purchase_invoice_report_status": status})
        # else:
        #     filters.append(
        #         {"purchase_invoice_report_status": {"$ne": InvoiceStatus.CANCELLED}}
        #     )
        query = {"$and": filters}
        reports = self.find(**query)
        return reports

    def get_reports_for_date(self, for_date, hotel_id, pms_type):
        query = {
            "$and": [
                {
                    "$or": [
                        {"report_date": str(for_date)},
                        {"invoice_date": str(for_date)},
                    ]
                },
                {"hotel_id": hotel_id},
                {"pms_type": pms_type},
            ]
        }
        reports = self.find(**query)
        return reports

    def get_reports_for_date_and_hotels(self, for_date, hotel_ids):
        query = {
            "$and": [
                {
                    "$or": [
                        {"report_date": str(for_date)},
                        {"invoice_date": str(for_date)},
                    ]
                },
                {"hotel_id": {"$in": hotel_ids}},
            ]
        }
        reports = self.find(**query)
        return reports

    def get_reports_for_date_range(self, from_date, to_date, hotel_id, pms_type):
        query = {
            "$and": [
                {
                    "$or": [
                        {"report_date": {"$gte": str(from_date), "$lte": str(to_date)}},
                        {
                            "invoice_date": {
                                "$gte": str(from_date),
                                "$lte": str(to_date),
                            }
                        },
                    ]
                },
                {"hotel_id": hotel_id},
                {"pms_type": pms_type},
            ]
        }
        reports = self.find(**query)
        return reports

    def get_hotel_ids_for_date_range(self, from_date, to_date):
        query = {
            "$or": [
                {"report_date": {"$gte": str(from_date), "$lte": str(to_date)}},
                {"invoice_date": {"$gte": str(from_date), "$lte": str(to_date)}},
            ]
        }
        return self.distinct(query, "cs_hotel_id")

    def get_invoice_data(self, invoice_numbers):
        pipeline = [
            {"$match": {"invoice_number": {"$in": invoice_numbers}}},
            {
                "$group": {
                    "_id": {
                        "invoice_number": "$invoice_number",
                        "total_invoice_amount": "$total_invoice_amount",
                        "checkin": "$checkin",
                        "checkout": "$checkout",
                        "invoice_date": "$invoice_date",
                    }
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "invoice_number": "$_id.invoice_number",
                    "total_invoice_amount": "$_id.total_invoice_amount",
                    "checkin": "$_id.checkin",
                    "checkout": "$_id.checkout",
                    "invoice_date": "$_id.invoice_date",
                }
            },
        ]
        return list(self.aggregate(pipeline))


def get_hotel_invoice_reports_repository():
    return tenant_wise_repository(HotelInvoiceReportRepository, "hotel_invoice_reports")
