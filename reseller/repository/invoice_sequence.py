import logging
from datetime import datetime, timedelta

from core.common.utils.date import maybe_convert_string_to_datetime, utc_to_ist
from core.hotel_invoice.data_classes.invoice_counter import InvoiceSequence
from reseller.repository.base_repository import (
    BaseRepository,
    DocumentDoesNotExist,
    tenant_wise_repository,
)

logger = logging.getLogger(__name__)


class InvoiceSequenceRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        invoice_sequence = InvoiceSequence(
            hotel_id=document["hotel_id"],
            gstin=document["gstin"],
            hotel_seq_id=document["hotel_seq_id"],
            invoice_number=document["invoice_number"],
            credit_note_number=document["credit_note_number"],
            active=document["active"],
            course_start=document["course_start"],
            course_end=document["course_end"],
        )

        return invoice_sequence

    def get_date_specific_gstin(self, from_date, to_date, hotel_id):
        """
        Return invoice sequence for a given start and end date
        """
        from_date = maybe_convert_string_to_datetime(utc_to_ist(from_date))
        to_date = maybe_convert_string_to_datetime(utc_to_ist(to_date))
        from_date = datetime.combine(from_date, from_date.time().min) + timedelta(
            days=1
        )
        to_date = datetime.combine(to_date, to_date.time().max)
        try:
            query = {
                "hotel_id": hotel_id,
                "course_start": {"$lte": to_date},
                "$or": [{"active": True}, {"course_end": {"$gte": from_date}}],
            }
            return self.get(**query)
        except DocumentDoesNotExist:
            logger.error(
                f"Invoice sequence doesnot exist for hotel_id {hotel_id}"
                f"for date {from_date}"
            )


def get_invoice_sequence_repository():
    return tenant_wise_repository(InvoiceSequenceRepository, "hotel_invoice_sequence")
