import logging

from pymongo.errors import DuplicateKeyError
from treebo_commons.utils import dateutils

from core.ingestion.data_classes.retry_ingestion_event import RetryIngestion<PERSON>vent
from reseller.constants import BookingQueueStatus
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository

logger = logging.getLogger(__name__)


class RetryIngestionEventsRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        retry_ingestion_events = RetryIngestionEvent.from_json(document)
        return retry_ingestion_events

    def get_retry_event(self, resource_id):
        query = {"resource_id": resource_id}
        event = self.find(**query)
        return event

    def update_retry_event(self, resource_id=None, status=None):
        query = {"resource_id": resource_id}
        retry_event = self.get_retry_event(resource_id)
        if retry_event:
            data_to_update = dict(status=status)
            self.update(query=query, dict_or_data_class=data_to_update, upsert=True)

    def create_retry_event(
        self, hotel_id, resource_id, resource_type, module_name, status, eta
    ):
        retry_event = RetryIngestionEvent(
            hotel_id=hotel_id,
            resource_id=resource_id,
            resource_type=resource_type,
            module_name=module_name,
            status=status,
            eta=eta,
        )
        try:
            self.create(retry_event)
            return True
        except DuplicateKeyError:
            return False

    def get_events_to_retry(self):
        query = {
            "status": BookingQueueStatus.PENDING,
            "eta": {"$lte": dateutils.current_datetime()},
        }

        events = self.find(**query)
        return events


def get_retry_ingestion_events_repository():
    return tenant_wise_repository(
        RetryIngestionEventsRepository, "retry_ingestion_events"
    )
