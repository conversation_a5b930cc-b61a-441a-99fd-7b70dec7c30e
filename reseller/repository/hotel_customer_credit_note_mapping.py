from core.invoice_mapping.data_class.hotel_customer_credit_note_mapping import (
    HotelCustomerCreditNoteMapping,
)
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class HotelCustomerCreditNoteMappingRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        return HotelCustomerCreditNoteMapping(
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            booking_id=document["booking_id"],
            hotel_credit_note_number=document["hotel_credit_note_number"],
            hotel_credit_note_id=document["hotel_credit_note_id"],
            customer_credit_note_id=document["customer_credit_note_id"],
            customer_credit_note_number=document["customer_credit_note_number"],
        )

    def get_by_customer_credit_note_numbers(self, customer_credit_note_numbers):
        query = {"customer_credit_note_number": {"$in": customer_credit_note_numbers}}
        credit_note_mappings = self.find(**query)
        return credit_note_mappings

    def get_by_hotel_credit_note_numbers(self, hotel_credit_note_numbers):
        query = {"hotel_credit_note_number": {"$in": hotel_credit_note_numbers}}
        credit_note_mappings = self.find(**query)
        return credit_note_mappings


def get_hotel_customer_credit_note_mapping_repository():
    return tenant_wise_repository(
        HotelCustomerCreditNoteMappingRepository, "hotel_customer_credit_note_mapping"
    )
