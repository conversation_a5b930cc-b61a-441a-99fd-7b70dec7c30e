from core.common.constants import TREEBO_LEGAL_NAME
from core.common.data_classes.address import Address
from core.common.data_classes.gst_details import GstDetails
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class TreeboGstDetailsRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        address_details = document["address"]
        address = Address(
            field1=address_details["field1"],
            field2=address_details["field2"],
            city=address_details["city"],
            state=address_details["state"],
            country=address_details["country"],
            pincode=address_details["pincode"],
        )

        gst_details = GstDetails(
            legal_name=TREEBO_LEGAL_NAME,
            gstin_number=document["gstin"],
            address=address,
            has_lut=True,
        )

        return gst_details


def get_treebo_gst_details_repository():
    return tenant_wise_repository(TreeboGstDetailsRepository, "treebo_gst")
