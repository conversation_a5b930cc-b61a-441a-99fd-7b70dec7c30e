from core.user.data_classes.user import User
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class UserRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        user = User(email=document["email"], authenticated=document["authenticated"])

        return user


def get_user_repository():
    return tenant_wise_repository(UserRepository, "users")
