from core.common.constants import IssuedByTypes
from core.common.data_classes.address import Address
from core.common.data_classes.charge import Charge
from core.common.data_classes.customer import Customer
from core.common.data_classes.gst_details import GstDetails
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice
from core.customer_invoice.data_classes.line_item import LineItem
from core.customer_invoice.data_classes.payment import Payment
from core.hotel.data_classes.room import Room
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class CustomerInvoiceRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        return CustomerInvoice(
            booking_id=document["booking_id"],
            hotel_id=document["hotel_id"],
            pms_type=document["pms_type"],
            invoice_id=document["invoice_id"],
            invoice_number=document["invoice_number"],
            gst_details=self._gst_details(document["gst_details"]),
            line_items=self._line_items(document["line_items"]),
            status=document["status"],
            source_created_at=document["source_created_at"],
            payments=self._payments(document.get("payments", [])),
            issued_by_type=document.get("issued_by_type", IssuedByTypes.RESELLER),
            bill_id=document.get("bill_id", ""),
            invoice_date=document.get("invoice_date", ""),
            irn=document.get("irn"),
            qr_code=document.get("qr_code"),
            is_einvoice=document.get("is_einvoice"),
            is_locked_in_crs=document.get("is_locked_in_crs"),
        )

    def _gst_details(self, gst_details):
        gst_details = GstDetails(
            legal_name=gst_details.get("legal_name"),
            gstin_number=gst_details.get("gstin_number"),
            address=Address(**gst_details["address"]),
            has_lut=gst_details.get("has_lut"),
            is_sez=gst_details.get("is_sez"),
        )
        return gst_details

    def _line_items(self, line_items_data):
        line_items = []
        for item in line_items_data:
            charge = self._charge(item["charge"])
            room = self._room(item.get("room"))
            customers = self._customers(item.get("customers"))
            line_item = LineItem(
                charge,
                room_id=item["room_id"],
                guest_stay_id=item["guest_stay_id"],
                room=room,
                customers=customers,
            )
            line_items.append(line_item)
        return line_items

    def _room(self, room_details):
        if not room_details:
            return None
        return Room(
            number=room_details["number"],
            room_type_name=room_details["room_type_name"],
        )

    def _customers(self, customers_details):
        if not customers_details:
            return frozenset()

        def get_name(cus):
            try:
                name = cus["name"]
            except KeyError:
                # to cater for legacy structure
                name = (cus["first_name"], cus["last_name"])
            return name

        customers = [
            Customer(
                uid=cus["uid"],
                name=get_name(cus),
                email=cus.get("email", ""),
                phone_number=cus.get("phone_number", ""),
            )
            for cus in customers_details
        ]
        return customers

    def _payments(self, payments_data):
        return [
            Payment(
                amount=payment_data["amount"],
                payment_date=payment_data["payment_date"],
                description=payment_data["description"],
                payment_mode=payment_data["payment_mode"],
            )
            for payment_data in payments_data
        ]

    def _charge(self, charge_details):
        charge = Charge(
            uid=charge_details["uid"],
            name=charge_details["name"],
            quantity=charge_details["quantity"],
            applicable_date=charge_details["applicable_date"],
            pre_tax=charge_details["pre_tax"],
            tax=charge_details["tax"],
            hsn_code=charge_details["hsn_code"],
            tax_breakup=[
                TaxBreakup(item["code"], item["amount"], percent=item["percent"])
                for item in charge_details["tax_breakup"]
            ],
            smart_description=charge_details["smart_description"],
            tax_code=charge_details.get("tax_code", ""),
        )
        return charge


def get_customer_invoice_repository():
    return tenant_wise_repository(CustomerInvoiceRepository, "customer_invoices")
