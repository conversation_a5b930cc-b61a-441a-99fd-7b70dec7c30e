import abc
import dataclasses  # pylint: disable=wrong-import-order
import datetime
import decimal
import logging
from collections import defaultdict
from functools import partial

from bson import Decimal128
from pymongo import UpdateOne
from pymongo.collection import Collection, ReturnDocument
from pymongo.errors import BulkWriteError

from reseller.helpers import get_tenant_id, multi_tenant_mongo

logger = logging.getLogger(__name__)


class DocumentDoesNotExist(RuntimeError):
    def __init__(self, collection: Collection, query: dict):
        self.message = f"{self.__class__.__name__} in collection: {collection.name} for query: {query}"
        super().__init__(self.message)


class MultipleDocumentsExist(RuntimeError):
    def __init__(self, collection: Collection, query: dict):
        self.message = f"{self.__class__.__name__} in collection: {collection.name} for query: {query}"
        super().__init__(self.message)


class DocumentNotFoundException(Exception):
    def __init__(self, message):
        self.message = f"{message}"
        super().__init__(self.message)


class BaseRepository(metaclass=abc.ABCMeta):
    def __init__(self, collection: Collection):
        # assert collection.name in mongo.db.list_collection_names()
        self.collection = collection

    @abc.abstractmethod
    def convert_document_to_data_class(self, document):
        pass

    def get(self, **query):
        documents = self.find(**query)
        if not documents:
            raise DocumentDoesNotExist(self.collection, query)
        if len(documents) > 1:
            raise MultipleDocumentsExist(self.collection, query)
        return documents[0]

    def create(self, dict_or_data_class, created_at=None, modified_at=None, **kwargs):
        data = self._get_data(dict_or_data_class)
        self._set_created_at(data, created_at)
        self._set_modified_at(data, modified_at)
        self.collection.insert_one(data, **kwargs)

    def update(self, query, dict_or_data_class, suppress_error=False, **kwargs):
        data = self._get_data(dict_or_data_class)
        if kwargs.get("upsert"):
            self._set_created_at(data)
        self._set_modified_at(data)
        document = self.collection.find_one_and_update(
            filter=query,
            update={"$set": data},
            return_document=ReturnDocument.AFTER,
            **kwargs,
        )
        if not document:
            if suppress_error:
                return None
            raise DocumentDoesNotExist(self.collection, query)

        return self.convert_document_to_data_class(document)

    def bulk_update(self, queries, data_list):
        update_requests = []
        for query_index, new_data in enumerate(data_list):
            new_data = self._get_data(new_data)
            update_requests.append(
                UpdateOne(queries[query_index], {"$set": new_data}, upsert=True)
            )
        try:
            self.collection.bulk_write(update_requests, ordered=True)
        except BulkWriteError as e:
            logger.error(f"Bulk operation failed with exception {e}".format(e=e))
            raise BulkWriteError

    def update_many(self, query, dict_or_data_class, **kwargs):
        data = self._get_data(dict_or_data_class)
        self._set_modified_at(data)
        update_response = self.collection.update_many(
            filter=query,
            update={"$set": data},
            **kwargs,
        )

        return update_response.modified_count

    def raw_update(self, **kwargs):
        return self.collection.find_one_and_update(**kwargs)

    def find(self, **query):
        documents = self.collection.find(query)
        return [self.convert_document_to_data_class(document) for document in documents]

    def delete(self, **query):
        self.collection.delete_many(query)

    def count(self, **query):
        return self.collection.count_documents(query)

    def distinct(self, query, distinct_key):
        return self.collection.find(query).distinct(distinct_key)

    def exists(self, **query):
        if self.collection.count_documents(query, limit=1) == 1:
            return True
        return False

    upsert = partial(update, upsert=True)

    def all(self):
        return self.find(**{})

    def __str__(self):
        return f"Repository: {self.collection.name}"

    # pylint: disable=unused-argument
    def _get_data(self, data, fields_to_be_excluded: list = None):
        """Does some validations and checks"""
        if not isinstance(data, dict):
            # if it is not a dict treat it as a data class
            data = dataclasses.asdict(data)

        data = self._convert_decimal_to_decimal_128(data)

        return data

    def _convert_decimal_to_decimal_128(self, data: dict):
        if isinstance(data, dict):
            data = {k: self._convert_decimal_to_decimal_128(v) for k, v in data.items()}
        elif isinstance(data, (tuple, list)):
            data = [self._convert_decimal_to_decimal_128(item) for item in data]
        elif isinstance(data, decimal.Decimal):
            data = Decimal128(data)
        return data

    def _set_created_at(self, data, created_at=None):
        data["created_at"] = created_at or datetime.datetime.utcnow()

    def _set_modified_at(self, data, modified_at=None):
        data["modified_at"] = modified_at or datetime.datetime.utcnow()

    def aggregate(self, pipeline):
        return self.collection.aggregate(pipeline)

    __repr__ = __str__


tenant_wise_repository_cache = defaultdict(dict)


def tenant_wise_repository(repository_cls, collection_name):
    tenant_id = get_tenant_id()
    if tenant_id not in tenant_wise_repository_cache:
        tenant_wise_repository_cache[tenant_id] = {}
    if collection_name not in tenant_wise_repository_cache[tenant_id]:
        tenant_wise_repository_cache[tenant_id][collection_name] = repository_cls(
            multi_tenant_mongo().db[collection_name]
        )
    return tenant_wise_repository_cache[tenant_id][collection_name]
