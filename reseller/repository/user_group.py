from core.user.data_classes.user_group import UserGroup
from reseller.repository.base_repository import BaseRepository, tenant_wise_repository


class UserGroupRepository(BaseRepository):
    def convert_document_to_data_class(self, document):
        user_group = UserGroup(email=document["email"], role=document["role"])

        return user_group


def get_user_group_repository():
    return tenant_wise_repository(UserGroupRepository, "user_group")
