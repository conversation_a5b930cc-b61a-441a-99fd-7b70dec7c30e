import json
import logging

from flask import Blueprint, redirect, render_template, request, session, url_for
from flask_login import current_user, login_user
from requests import HTTPError
from requests_oauthlib import OAuth2Session
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.request_tracing.context import get_current_tenant_id

from core.user.constants import UserRole
from core.user.data_classes.user import User
from reseller import app, login_manager
from reseller.repository.user import get_user_repository
from reseller.repository.user_group import get_user_group_repository

flask_login_blue_print = Blueprint(
    "flask-login", __name__, url_prefix="/"
)  # pylint: disable=invalid-name
auth = app.config["AUTH"]  # pylint: disable=invalid-name
logger = logging.getLogger(__name__)


def get_google_auth(state=None, token=None):
    oauth2_cred = AwsSecretManager.get_secret(
        get_current_tenant_id(), "reseller-oauth2-cred"
    )
    if oauth2_cred:
        auth.update(oauth2_cred)
    if token:
        return OAuth2Session(auth["CLIENT_ID"], token=token)
    if state:
        return OAuth2Session(
            auth["CLIENT_ID"], state=state, redirect_uri=auth["REDIRECT_URI"]
        )
    oauth = OAuth2Session(
        auth["CLIENT_ID"], redirect_uri=auth["REDIRECT_URI"], scope=auth["SCOPE"]
    )
    return oauth


@login_manager.user_loader
def load_user(email):
    user = get_user_repository().find(email=email)
    if not user:
        return None
    return user[0]


@flask_login_blue_print.route("/login")
def login():
    if current_user.is_authenticated:
        return redirect(url_for("admin.index"))
    google = get_google_auth()
    auth_url, state = google.authorization_url(auth["AUTH_URI"], access_type="offline")
    session["oauth_state"] = state
    return render_template("login.html", auth_url=auth_url)


# pylint: disable=too-many-return-statements
@flask_login_blue_print.route("/callback")
def callback():
    if current_user is not None and current_user.is_authenticated:
        return redirect(url_for("admin.index"))
    if "error" in request.args:
        if request.args.get("error") == "access_denied":
            return "You denied access"
        return "Error encountered while logging in"
    if "code" not in request.args and "state" not in request.args:
        return redirect("/login")

    google = get_google_auth(state=session["oauth_state"])
    try:
        request_url = request.url.replace("http://", "https://")
        logger.info(f"Request URL: {request.url}")
        token = google.fetch_token(
            auth["TOKEN_URI"],
            client_secret=auth["CLIENT_SECRET"],
            authorization_response=request_url,
        )
    except HTTPError:
        return "HTTPError occurred."
    google = get_google_auth(token=token)
    resp = google.get(auth["USER_INFO"])
    if resp.status_code == 200:
        user_data = resp.json()
        email = user_data["email"]
        if not get_user_group_repository().find(email=email, role=UserRole.ADMIN):
            return "You are not authorized to access"
        user = get_user_repository().update(
            query={"email": email}, dict_or_data_class=User(email=email), upsert=True
        )
        user.tokens = json.dumps(token)
        login_user(user)
        return redirect(url_for("admin.index"))
    return "Could not fetch your information"
