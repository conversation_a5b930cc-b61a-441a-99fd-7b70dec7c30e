# coding=utf-8
"""
Middlewares
"""
import logging
import re
from functools import wraps

import flask
from marshmallow import ValidationError
from ths_common.exceptions import (
    AggregateNotFound,
    ApiValidationException,
    AuthorizationError,
    CRSException,
    DatabaseError,
    DatabaseLockError,
    DownstreamSystemFailure,
    InternalServerException,
    InvalidOperationError,
    JobNotRegistered,
    MissingFactsException,
    OutdatedVersion,
    PolicyAuthException,
    ResourceNotFound,
    UserTypeHeaderInformationMissing,
    ValidationException,
)
from treebo_commons.exceptions import NaiveDatetimeUnsupportedException
from treebo_commons.request_tracing.context import (
    get_current_hotel_id,
    get_current_request_id,
    get_current_tenant_id,
)
from treebo_commons.request_tracing.flask import after_request, before_request
from werkzeug.exceptions import NotFound

from reseller.core.threadlocal import consumer_context

logger = logging.getLogger(__name__)


def get_http_status_code_from_exception(exception: CRSException) -> int:
    if isinstance(exception, AuthorizationError) or isinstance(
        exception, PolicyAuthException
    ):
        return 403

    resource_missing_exceptions = [AggregateNotFound, ResourceNotFound]
    for error_cls in resource_missing_exceptions:
        if isinstance(exception, error_cls):
            return 404

    bad_request_exceptions = [
        ApiValidationException,
        ValidationException,
        InvalidOperationError,
        NaiveDatetimeUnsupportedException,
    ]

    for error_cls in bad_request_exceptions:
        if isinstance(exception, error_cls):
            return 400

    resource_conflict_exceptions = [
        OutdatedVersion,
        DatabaseLockError,
    ]

    for error_cls in resource_conflict_exceptions:
        if isinstance(exception, error_cls):
            return 409

    internal_server_errors = [
        InternalServerException,
        DatabaseError,
        JobNotRegistered,
        MissingFactsException,
        DownstreamSystemFailure,
    ]

    for error_cls in internal_server_errors:
        if isinstance(exception, error_cls):
            return 500

    if isinstance(exception, UserTypeHeaderInformationMissing):
        return 401

    if isinstance(exception, CRSException):
        return 400

    return 500


def need_error_logging(error):
    if isinstance(error, NotFound):
        return False
    return True


def exception_handler(error, from_consumer=False):
    """
    Exception handler
    :param error:
    :param from_consumer:
    :return:
    """

    if isinstance(error, ApiValidationException):
        status_code = 400
        error_messages = error.error_messages
        error_code = error.code(app_name="crs")
        errors = []
        for error_message in error_messages:
            error = dict(
                code=error_code,
                message=error_message["error"],
                extra_payload=dict(field=error_message["field"]),
                developer_message=None,
                request_id=get_current_request_id() if not from_consumer else None,
            )
            errors.append(error)
        from core.common.api.api_response import APIResponse

        response = APIResponse.build(errors=errors, status_code=status_code)

    elif isinstance(error, CRSException):
        status_code = get_http_status_code_from_exception(error)
        error_code = error.code(app_name="crs")
        error = dict(
            code=error_code,
            message=error.message,
            developer_message=error.description,
            extra_payload=error.extra_payload,
            request_id=get_current_request_id() if not from_consumer else None,
        )
        from core.common.api.api_response import APIResponse

        response = APIResponse.build(errors=[error], status_code=status_code)
    else:

        # populate status code
        status_code = 500
        if getattr(error, "status_code", None):
            status_code = error.status_code
        if getattr(error, "code", None):
            status_code = error.code

        if isinstance(error, ValidationError):
            status_code = 400

        if not re.search(r"^[1-5]\d{2}$", str(status_code)):
            status_code = 500

        error_code = None
        # populate error dict
        error_dict = dict(code=status_code)
        # TODO:: causing JSON serializer error for unknown types. Need to find a cleaner solution for this.
        # error_dict['extra_payload'] = error.args if hasattr(error, 'args') else None
        error_dict["extra_payload"] = dict()
        error_dict["message"] = (
            error.message if hasattr(error, "message") else "Exception occurred."
        )
        error_dict["developer_message"] = (
            error.description if hasattr(error, "description") else str(error)
        )
        error_dict["request_id"] = (
            get_current_request_id() if not from_consumer else None
        )
        error_dict["tenant_id"] = get_current_tenant_id()
        from core.common.api.api_response import APIResponse

        response = APIResponse.build(errors=[error_dict], status_code=status_code)

    if need_error_logging(error):

        if not from_consumer:
            request = flask.request
            request_url = request.url
            request_headers = dict(request.headers)

            if request.is_json:
                request_data = (
                    request.json
                    if request.get_json(silent=True)
                    else request.get_data(as_text=True)
                )
            else:
                request_data = request.get_data(as_text=True)

            hotel_id = get_current_hotel_id()
            tenant_id = get_current_tenant_id()

            logger.exception(
                "Exception in api: %s. Request Payload: %s",
                error,
                request_data,
                extra=dict(
                    error_code=error_code,
                    status_code=status_code,
                    request_url=request_url,
                    request_headers=request_headers,
                    request_data=request_data,
                    request_method=request.method,
                    hotel_id=hotel_id,
                    tenant_id=tenant_id,
                ),
            )
        else:
            tenant_id = get_current_tenant_id()
            logger.exception(
                "Exception in consumer: %s",
                error,
                extra=dict(error_code=error_code, tenant_id=tenant_id),
            )

    return response


def consumer_middleware(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        from flask import current_app

        with current_app.test_request_context():
            from flask import request

            # TODO: Any better way to set tenant_id. May be better to move this out of treebo_commons?
            # TODO: In treebo_commons, figure out a better way to set context for background jobs, instead of from
            #  request headers
            request_headers = dict(request.headers)
            request_headers["X-Tenant-Id"] = consumer_context.tenant_id
            before_request(request_headers)
            r_val = func(*args, **kwargs)
            after_request(response=None, request=None)
            return r_val

    return wrapper
