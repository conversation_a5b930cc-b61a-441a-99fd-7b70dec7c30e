class HotelCustomerInvoiceMappingDoesNotExist(Exception):
    """
    Raise when buy sell invoice mapping doesn't exist in mapping collection
    """

    def __init__(self, booking_id, hotel_id):
        msg = "Buy sell invoice mapping does't exist for hx_booking_id: {bid} and hx_hotel_id: {hid}.".format(
            bid=booking_id, hid=hotel_id
        )
        super(HotelCustomerInvoiceMappingDoesNotExist, self).__init__(msg)


class BookingDoesNotExist(Exception):
    """
    Raise when booking does not exist in booking collection
    """

    def __init__(self, booking_id, hotel_id):
        msg = f"Booking does not exist for hx_booking_id: {booking_id} and hx_hotel_id: {hotel_id}"
        super(BookingDoesNotExist, self).__init__(msg)


class GstDoesNotExist(Exception):
    def __init__(self, state_code):
        msg = f"Treebo GST does not exist for state code: {state_code}"
        super(GstDoesNotExist, self).__init__(msg)


class ResellerCheckFailureException(Exception):
    pass


class HotelIsInMarketplace(Exception):
    pass


class InvalidInvoiceStateForManualIngestion(Exception):
    def __init__(self, booking_id, hotel_id):
        msg = f"Invalid Invoice state for manual ingestion for booking_id: {booking_id} and hotel_id: {hotel_id}"
        super(InvalidInvoiceStateForManualIngestion, self).__init__(msg)


class GstDoesNotExistInCatalog(Exception):
    def __init__(self, hotel_id):
        msg = f"GSTIN does not exist for hotel {hotel_id} in catalog."
        super(GstDoesNotExistInCatalog, self).__init__(msg)


class HotelInvoiceNumberNotReceived(Exception):
    pass


class HotelCreditNoteNumberNotReceived(Exception):
    pass


class ConcurrentIngestionException(Exception):
    def __init__(self, resource_type, resource_id):
        msg = f"Concurrent ingestion requested for {resource_type} {resource_id}"
        super(ConcurrentIngestionException, self).__init__(msg)
