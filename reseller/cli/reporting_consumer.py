import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from reseller.consumers.reporting.reporting_consumer import ReportingConsumer
from reseller.core.threadlocal import consumer_context
from reseller.services.reporting_service import ReportingService


@click.command("start_reporting_consumer")
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
@inject(reporting_service=ReportingService)
def start_reporting_consumer(
    reporting_service, tenant_id=TenantClient.get_default_tenant()
):

    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = ReportingConsumer(reporting_service, tenant_id)
    consumer.start_consumer()
