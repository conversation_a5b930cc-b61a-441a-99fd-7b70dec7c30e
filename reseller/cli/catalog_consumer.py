import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from reseller.consumers.catalog.catalog_event_consumer import CatalogServiceConsumer
from reseller.core.threadlocal import consumer_context
from reseller.services.catalog_application_service import CatalogApplicationService


@click.command("start_catalog_consumer")
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
@inject(catalog_application_service=CatalogApplicationService)
def start_catalog_consumer(
    catalog_application_service, tenant_id=TenantClient.get_default_tenant()
):

    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = CatalogServiceConsumer(catalog_application_service, tenant_id)
    consumer.start_consumer()
