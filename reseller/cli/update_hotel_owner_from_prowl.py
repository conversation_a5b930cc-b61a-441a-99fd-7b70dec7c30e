import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import (
    get_current_tenant_id,
    request_context,
)

from core.hotel.data_classes.hotel_detail import HotelOwner
from core.hotel.services.hotel_owner import GetHotelOwner
from reseller import tenant_wise_mongo_connection
from reseller.repository.hotel_detail import HotelDetailRepository

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--hotel_ids",
    help="comma separated hotel_ids for which the owners needs to be updated from prowl.",
    default=None,
)
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
def update_hotel_owner_from_prowl(
    hotel_ids,
    tenant_id,
):
    logger.info("Started update_hotel_owner_from_prowl.. ")
    request_context.tenant_id = tenant_id
    if hotel_ids:
        logger.info(f"Setup will be run for hotel_ids: {hotel_ids}")
    else:
        logger.info(f"Setup will be run for all hotel_ids")

    mongo = tenant_wise_mongo_connection.get(get_current_tenant_id())
    hotel_detail_repository = HotelDetailRepository(mongo.db.hotel_detail)
    if hotel_ids:
        query = {"hotel_id": {"$in": hotel_ids}}
        hotel_details = hotel_detail_repository.find(query=query)
    else:
        hotel_details = hotel_detail_repository.find()
    try:
        for hd in hotel_details:
            try:
                hotel_owner_details = GetHotelOwner.get_hotel_owner(hd.hotel_id)
            except Exception as e:
                logger.info(
                    f"No HotelOwner Found for hotel_id: {hd.hotel_id} in prowl. Exception: {e}"
                )
                continue
            if hotel_owner_details:
                logger.info(f"HotelOwner Found for hotel_id: {hd.hotel_id} in prowl.")
                hd.hotel_owners = []
                for hod in hotel_owner_details:
                    hd.hotel_owners.append(
                        HotelOwner(
                            first_name=hod.first_name,
                            last_name=hod.last_name,
                            email=hod.email_id,
                            is_primary_owner=True,
                            phone_number=hod.contact_number,
                        )
                    )
            else:
                logger.info(
                    f"No HotelOwner Found for hotel_id: {hd.hotel_id} in prowl."
                )

        logger.info(f"Details to be updated: {hotel_details}")
        hotel_detail_repository.create_or_update_hotel_details(hotel_details)
    except Exception as e:
        logger.error(f"Exception occurred while updating database: {e}")

    logger.info("Completed update_hotel_owner_from_prowl")
