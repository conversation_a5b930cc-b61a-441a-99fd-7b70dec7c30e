import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from reseller.consumers.crs.crs_event_consumer import CrsServiceConsumer
from reseller.core.threadlocal import consumer_context
from reseller.services.ingestion.ingestion_event_publisher import (
    IngestionEventPublisher,
)


@click.command("start_crs_consumer")
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
@inject(ingestion_event_publisher=IngestionEventPublisher)
def start_crs_consumer(
    ingestion_event_publisher, tenant_id=TenantClient.get_default_tenant()
):

    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = CrsServiceConsumer(ingestion_event_publisher, tenant_id)
    consumer.start_consumer()
