import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from reseller.consumers.ingestion_event_consumer import IngestionEventConsumer
from reseller.core.threadlocal import consumer_context
from reseller.services.ingestion.ingestion_service import IngestionService


@click.command("start_ingestion_event_consumer")
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
@inject(ingestion_service=IngestionService)
def start_ingestion_event_consumer(
    ingestion_service, tenant_id=TenantClient.get_default_tenant()
):

    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = IngestionEventConsumer(ingestion_service, tenant_id)
    consumer.start_consumer()
