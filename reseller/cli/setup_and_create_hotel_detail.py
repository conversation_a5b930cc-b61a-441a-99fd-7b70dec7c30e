import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import (
    get_current_tenant_id,
    request_context,
)

from core.hotel.providers.get_hotels_v2.catalogue import CatalogueGetHotelDetails
from object_registry import inject
from reseller import tenant_wise_mongo_connection
from reseller.repository.hotel_detail import HotelDetailRepository

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--hotel_ids",
    help="comma separated hotel_ids for which the hotel_details needs to be synced",
    default=None,
)
@click.option(
    "--tenant_id", default=TenantClient.get_default_tenant(), help="tenant_id"
)
@with_appcontext
@inject(
    catalog_hotel_details_service=CatalogueGetHotelDetails,
)
@with_appcontext
def setup_and_create_hotel_detail(
    catalog_hotel_details_service,
    hotel_ids,
    tenant_id,
):
    logger.info("Started setup_and_create_hotel_detail.. ")
    request_context.tenant_id = tenant_id
    if hotel_ids:
        logger.info(f"Setup will be run for hotel_ids: {hotel_ids}")
    else:
        logger.info(f"Setup will be run for all hotel_ids")

    hotel_details_by_hotel_id_map = get_hotel_details(
        catalog_hotel_details_service, hotel_ids=hotel_ids
    )
    try:
        mongo = tenant_wise_mongo_connection.get(
            get_current_tenant_id(), TenantClient.get_default_tenant()
        )
        hotel_detail_repository = HotelDetailRepository(mongo.db.hotel_detail)
        logger.info(f"Details to be updated: {hotel_details_by_hotel_id_map}")
        hotel_detail_repository.create_or_update_hotel_details(
            list(hotel_details_by_hotel_id_map.values())
        )
    except Exception as e:
        logger.error(f"Exception occurred while updating database: {e}")

    logger.info("Completed setup_and_create_hotel_detail")


def get_hotel_details(catalog_hotel_details_service, hotel_ids=None):
    hotel_details = catalog_hotel_details_service.get_hotels(hotel_ids=hotel_ids)
    hotel_details_by_hotel_id_map = dict()
    if hotel_details:
        for hotel_detail in hotel_details:
            hotel_details_by_hotel_id_map[hotel_detail.hotel_id] = hotel_detail
    return hotel_details_by_hotel_id_map
