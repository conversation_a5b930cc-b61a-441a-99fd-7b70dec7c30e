import logging

from flask import url_for

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from reseller.api.dtos.push_job_to_queue import PushJobToQueueSchema
from reseller.api.request_parser import schema_wrapper_parser
from reseller.services.ingestion.reporting_event_publisher import (
    ReportingEventPublisher,
)

logger = logging.getLogger(__name__)


class PushJobToQueue(TreeboBaseAPI):
    """
    Pushes job to the reporting queue.
    """

    DOMAIN = "hotelinvoice"

    @schema_wrapper_parser(PushJobToQueueSchema)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        job_name = request_data.get("job_name")
        job_params = request_data.get("job_params")
        api_endpoint = url_for(self.DOMAIN + "." + job_name)
        logger.info(
            f"Processing Job {job_name} with endpoint {api_endpoint} & job_params: {job_params}."
        )
        ReportingEventPublisher().publish(
            report_data=dict(job_name=job_name, job_params=job_params),
            routing_key="reseller-reporting.#",
        )
        logger.info(f"Job {job_name} has been enqueued.")
        return f"Request has been enqueued."
