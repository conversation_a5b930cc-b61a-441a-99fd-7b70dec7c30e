# pylint: disable=invalid-name
import logging

from flask import Blueprint, jsonify, request

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from reseller.exceptions import GstDoesNotExist
from reseller.services.hotel.get_hotel import GetHotel
from reseller.services.invoice_counter import get_latest_invoice_sequence

logger = logging.getLogger(__name__)

hotel_details_blue_print = Blueprint(
    "hotel_details", __name__, url_prefix="/hotel-details"
)


class HotelGstAddressAPI(TreeboBaseAPI):
    def get(self):
        request_data = jsonify(request.args).json
        try:
            hotel_gstin_detail = GetHotel().hotel_gst_address(
                hotel_id=request_data["hx_hotel_id"],
                date=request_data["date"],
                pms_type=request_data["pms_type"],
            )
            return hotel_gstin_detail, 200
        except GstDoesNotExist as e:
            return f"Failed due to: {str(e)}", 400


class HotelInvoiceSequenceAPI(TreeboBaseAPI):
    def get(self):
        request_data = jsonify(request.args).json

        hotel_id = request_data["hotel_id"]
        hotel_invoice_sequence = get_latest_invoice_sequence(hotel_id=hotel_id)
        response = {
            "hotel_id": hotel_id,
            "invoice_sequence": hotel_invoice_sequence.invoice_number,
        }
        return response, 200


hotel_details_blue_print.add_url_rule(
    "/", view_func=HotelGstAddressAPI.as_view("hotel_details")
)
hotel_details_blue_print.add_url_rule(
    "/invoice-sequence/", view_func=HotelInvoiceSequenceAPI.as_view("invoice-sequence")
)
