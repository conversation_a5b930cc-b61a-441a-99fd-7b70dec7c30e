import logging

from flask import Blueprint

from core.common.api.treebo_api import TreeboBaseAPI
from reseller.api.dtos.hotel_ingestion_schema import HotelIngestionRequestSchema
from reseller.api.request_parser import schema_wrapper_parser
from reseller.services.ingestion.hotel_ingestion import HotelIngestionService

logger = logging.getLogger(__name__)

hotel_ingestion_blueprint = Blueprint(
    "hotel_ingestion", __name__, url_prefix="/hotel/ingest"
)  # pylint: disable=invalid-name


class HotelIngestionAPI(TreeboBaseAPI):
    @schema_wrapper_parser(HotelIngestionRequestSchema)
    def post(self, **kwargs):
        try:
            parsed_request = kwargs.get("parsed_request")
            for request_data in parsed_request["hotels"]:
                HotelIngestionService(
                    hotel_id=request_data["hotel_id"],
                    date=request_data["date"],
                    sold_as=request_data["sold_as"],
                ).ingest_hotels()

        except Exception as e:
            message = f"Error Ingesting data for hotels due to {str(e)}"
            logger.exception(message)

            return message, 400

        return f"Hotel data ingested successfully for given hotels", 200


hotel_ingestion_blueprint.add_url_rule(
    "/", view_func=HotelIngestionAPI.as_view("hotel_ingestion")
)
