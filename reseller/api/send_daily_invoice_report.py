import datetime
from datetime import timedelta

from flask import request

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.date import (
    get_dates_between_dates,
    maybe_convert_string_to_datetime,
    utc_to_ist,
)
from reseller.api.dtos.invoice_reports import GetDailyInvoiceReportCsvRequest
from reseller.api.dtos.send_invoices_and_reports_schema import (
    SendDailyReportsForDateRangeSchema,
    SendDailyReportsSchema,
)
from reseller.api.generate_hotel_invoice_report import logger
from reseller.api.request_parser import RequestTypes, schema_wrapper_parser
from reseller.services.hotel_invoice.send_hotel_invoice_report import (
    get_csv_report,
    get_csv_report_for_all_hotels,
    send_daily_invoice_report,
    send_daily_invoice_report_for_all_hotels,
)


class DispatchInvoiceReportToPartners(TreeboBaseAPI):
    @schema_wrapper_parser(SendDailyReportsSchema)
    def post(self, **kwargs):
        """
        if for_date and hotel_ids are not passed, then we send hotel invoice reports
        for yesterday for all active hotels
        """
        request_data = kwargs.get("parsed_request")
        for_date = request_data.get("date")
        if for_date:
            for_date = utc_to_ist(
                datetime.datetime.strptime(for_date, "%Y-%m-%d")
            ).date()
        else:
            for_date = utc_to_ist(datetime.datetime.utcnow() - timedelta(days=1)).date()
        hotel_ids = request_data.get("hotel_ids", [])
        if not hotel_ids:
            status, message = send_daily_invoice_report_for_all_hotels(
                for_date=for_date
            )
        else:
            status, message = send_daily_invoice_report(
                for_date=for_date, hotel_ids=hotel_ids
            )
        invoice_report_dispatcher_response = (
            "Invoice report dispatcher response: {status}, {message}".format(
                status=status, message=message
            )
        )
        logger.info(invoice_report_dispatcher_response)
        return f"{invoice_report_dispatcher_response}"


class GetDailyInvoiceCsv(TreeboBaseAPI):
    @schema_wrapper_parser(GetDailyInvoiceReportCsvRequest)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        for_date = request_data["for_date"]
        hotel_ids = request_data["hotel_ids"]
        if "all" in hotel_ids:
            csv_download_url = get_csv_report_for_all_hotels(
                for_date=maybe_convert_string_to_datetime(for_date)
            )
        else:
            csv_download_url = get_csv_report(
                for_date=maybe_convert_string_to_datetime(for_date), hotel_ids=hotel_ids
            )
        logger.info(f"Got csv download url - {csv_download_url}")
        return csv_download_url


class DispatchInvoiceReportsForDateRangeAPI(TreeboBaseAPI):
    @schema_wrapper_parser(SendDailyReportsForDateRangeSchema)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        start_date = request_data["from_date"]
        end_date = request_data["to_date"]
        hotel_ids = request_data["hotel_ids"]
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        dates_between_start_and_end = get_dates_between_dates(end_date, start_date)
        response = dict()
        failed_hotel_ids = dict()
        for date in dates_between_start_and_end:
            status, response_from_service = send_daily_invoice_report(
                for_date=date.date(), hotel_ids=hotel_ids
            )
            invoice_report_dispatcher_response = (
                "Invoice report dispatcher response: {status}".format(status=status)
            )
            logger.info(invoice_report_dispatcher_response)
            if response_from_service["failed_hotel_ids"]:
                failed_hotel_ids[date.strftime("%Y-%m-%d")] = response_from_service[
                    "failed_hotel_ids"
                ]
        if failed_hotel_ids:
            response["msg"] = "Invoice reports are not sent for some hotel_ids"
            response["failed_hotelIds"] = failed_hotel_ids
            return response
        response[
            "msg"
        ] = "Invoice Reports are successfully sent for all hotel_ids for given date range"
        return response
