import logging
import time

from flask import request

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.constants import InvoiceStatus, PmsType
from core.hotel.services.hotel import GetHotel
from reseller.repository.hotel_invoice import get_hotel_invoice_repository

logger = logging.getLogger(__name__)


class UpdateInvoicesForReport(TreeboBaseAPI):
    def post(self):
        request_data = request.json
        hotel_list = request_data.get("hotel_id")

        for hotel_id in hotel_list:
            hotel = GetHotel(hotel_id).get_hotel()
            buy_invoices = get_hotel_invoice_repository().find(
                **{"hotel_id": hotel_id, "status": {"$in": [InvoiceStatus.CANCELLED]}}
            )
            if not buy_invoices:
                logger.info(
                    f"no buy invoices found for hotel with hotel_id: {hotel_id}"
                )
            else:
                for hotel_invoice in buy_invoices:
                    hotel_invoice_id = hotel_invoice.invoice_id
                    if not getattr(hotel_invoice.hotel, "pms_id", None):
                        get_hotel_invoice_repository().update(
                            {"invoice_id": hotel_invoice_id},
                            {
                                "hotel.pms_id": hotel.pms_id(PmsType.HX),
                                "hotel.navision_code": hotel.navision_code,
                            },
                        )
                        logger.info(
                            f"updated buyinvoices with invoice_id: {hotel_invoice_id}"
                        )
                    else:
                        logger.info(
                            f"data already populated for invoice: {hotel_invoice_id}"
                        )
                logger.info(f"updated all buy_invoices for hotel_id: {hotel_id}")
            time.sleep(1)
        logger.info(f"completed updating buy invoices")
        return f"Updated all bookings and invoices"
