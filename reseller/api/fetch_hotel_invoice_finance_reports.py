import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from object_registry import locate_instance
from reseller.api.dtos.fetch_hotel_invoices import FetchHotelInvoicesSchema
from reseller.api.request_parser import RequestTypes, schema_wrapper_parser
from reseller.reporting.nav_purchase_report.nav_purchase_report_service import (
    NavisionReportingService,
)
from reseller.reporting.nav_purchase_report.serialisers.all_e_tech import (
    PurchaseInvoicePushSchema,
)

logger = logging.getLogger(__name__)


class FetchHotelInvoiceFinanceReports(TreeboBaseAPI):

    DOMAIN = "hotelinvoice"

    @schema_wrapper_parser(FetchHotelInvoicesSchema, param_type=RequestTypes.ARGS)
    def get(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        invoice_numbers = request_data.get("invoice_numbers")
        unique_ref_ids = request_data.get("unique_ref_ids")
        nav_reporting_service: NavisionReportingService = locate_instance(
            NavisionReportingService
        )
        hotel_invoices_data = nav_reporting_service.fetch_hotel_invoice_data(
            invoice_numbers=invoice_numbers, unique_ref_ids=unique_ref_ids
        )
        response = PurchaseInvoicePushSchema().dump(hotel_invoices_data, many=True)
        return {"data": response.data}
