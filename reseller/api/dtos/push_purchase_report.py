from marshmallow import Schema, fields
from marshmallow.validate import OneOf

from core.common.constants import PurchaseInvoiceReportStatus


class PushPurchaseReportSchema(Schema):
    date = fields.String(required=False)
    status = fields.String(
        validate=OneOf(
            [PurchaseInvoiceReportStatus.PENDING, PurchaseInvoiceReportStatus.FAILED],
            error="'{input}' is not a valid choice for status",
        ),
        required=False,
    )
    invoice_numbers = fields.List(fields.String(), required=False)
    unique_ref_id = fields.String(required=False)
