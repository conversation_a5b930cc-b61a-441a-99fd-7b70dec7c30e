from marshmallow import Schema, ValidationError, fields, validates_schema


class GetResellerInvoiceReportsRequest(Schema):
    date = fields.String()
    invoice_numbers = fields.String()
    booking_id = fields.String()

    @validates_schema
    def validate(self, data):
        date = data.get("date")
        invoice_numbers = data.get("invoice_numbers")
        booking_id = data.get("booking_id")
        if not (date or invoice_numbers or booking_id):
            raise ValidationError("Either input date, invoice_numbers or booking_id")


class GetResellerInvoiceReportsResponse(Schema):
    cs_hotel_id = fields.String(required=True)
    hotel_id = fields.String(required=True)
    hotel_trade_name = fields.String(required=True)
    hotel_nav_code = fields.String(required=True)
    billed_by_legal_name = fields.String(required=True)
    billed_by_gstin = fields.String(required=True)
    billed_by_address = fields.String(required=True)
    billed_by_city = fields.String(required=True)
    billed_by_state = fields.String(required=True)
    billed_by_pin_code = fields.String(required=True)
    group_code = fields.String(required=True)
    booking_id = fields.String(required=True)
    reference_number = fields.String(required=True)
    room_booking_code = fields.String(required=True)
    booking_date = fields.String(required=True)
    checkin = fields.String(required=True)
    checkout = fields.String(required=True)
    days = fields.String(required=True)
    room_number = fields.String(required=True)
    room_type = fields.String(required=True)
    occupancy = fields.String(required=True)
    guest_names = fields.String(required=True)
    invoice_number = fields.String(required=True)
    invoice_date = fields.String(required=True)
    charge_type = fields.String(required=True)
    sac_code = fields.String(required=True)
    pre_tax_price = fields.String(required=True)
    total_amount = fields.String(required=True)
    cgst_value = fields.String(required=True)
    cgst_percent = fields.String(required=True)
    sgst_value = fields.String(required=True)
    sgst_percent = fields.String(required=True)
    igst_value = fields.String(required=True)
    igst_percent = fields.String(required=True)
    flood_cess_value = fields.String(required=True)
    flood_cess_percent = fields.String(required=True)
    billed_to_legal_name = fields.String(required=True)
    billed_to_gstin_number = fields.String(required=True)
    billed_to_address = fields.String(required=True)
    billed_to_city = fields.String(required=True)
    billed_to_state = fields.String(required=True)
    billed_to_pin_code = fields.String(required=True)
    booking_owner_name = fields.String(required=True)
    customer_invoice_number = fields.String(required=True)
    source = fields.String(required=True)
    report_date = fields.String(required=True)
    pms_type = fields.String(required=True)
    status = fields.String(required=True)
    issued_to_type = fields.String(required=True)
    total_invoice_amount = fields.String(required=True)
    original_invoice_number = fields.String(required=True)
    entity_type = fields.String(required=True)
    unique_ref_id = fields.String(required=True)


class GetDailyInvoiceReportCsvRequest(Schema):
    for_date = fields.String(required=True)
    hotel_ids = fields.List(fields.String(), required=True)
