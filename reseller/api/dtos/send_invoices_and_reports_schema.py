from datetime import datetime

from marshmallow import Schema, ValidationError, fields, validates_schema

from reseller.constants import InvoiceType


def validate_hotel_ids(hotel_ids):
    if not hotel_ids:
        raise ValidationError("Empty list error")
    elif len(hotel_ids) > 10:
        raise ValidationError("Too many hotel_ids")


class SendDailyInvoicesSchema(Schema):
    date = fields.String(required=False)
    hotel_ids = fields.List(
        fields.String(), required=False, validate=validate_hotel_ids
    )


class SendDailyReportsSchema(Schema):
    date = fields.String(required=False)
    hotel_ids = fields.List(
        fields.String(), required=False, validate=validate_hotel_ids
    )


class GenerateReportsSchema(Schema):
    date = fields.String(required=False)
    hotel_ids = fields.List(fields.String(), required=False)
    invoice_numbers = fields.List(fields.String(), required=False)
    credit_note_numbers = fields.List(fields.String(), required=False)
    hotel_invoice_numbers = fields.List(fields.String(), required=False)
    hotel_credit_note_numbers = fields.List(fields.String(), required=False)


class SendMonthlyReportsSchema(Schema):
    month = fields.String(required=False)
    year = fields.String(required=False)
    hotel_ids = fields.List(
        fields.String(), required=False, validate=validate_hotel_ids
    )

    @validates_schema
    def validate_month_year(self, data):
        month = data.get("month", None)
        year = data.get("year", None)
        if (not month and year) or (month and not year):
            raise ValidationError("Missing data for required Month or Year field.")


def _validate_date_range(data, threshold=10):
    from_date = data.get("from_date", None)
    to_date = data.get("to_date", None)
    if (
        datetime.strptime(to_date, "%Y-%m-%d")
        - datetime.strptime(from_date, "%Y-%m-%d")
    ).days > threshold:
        raise ValidationError(f"Date range exceeding {threshold} days")


class SendDailyInvoicesForDateRangeSchema(Schema):
    from_date = fields.String(required=True)
    to_date = fields.String(required=True)
    hotel_ids = fields.List(fields.String(), required=True, validate=validate_hotel_ids)

    @validates_schema
    def validate_date_range(self, data):
        _validate_date_range(data)


class SendDailyReportsForDateRangeSchema(Schema):
    from_date = fields.String(required=True)
    to_date = fields.String(required=True)
    hotel_ids = fields.List(fields.String(), required=True, validate=validate_hotel_ids)


class GetInvoicesAndCreditNotesSchema(Schema):
    from_date = fields.String()
    to_date = fields.String()
    booking_id = fields.String()
    invoice_number = fields.String()
    entity_type = fields.String()
    issued_to_type = fields.String()
    booking_reference_number = fields.String()


class InvoiceAndCreditNoteData(Schema):
    booking_id = fields.String()
    booking_reference_number = fields.String()
    entity_type = fields.String()
    issued_to_type = fields.String()
    checkin_date = fields.String()
    checkout_date = fields.String()
    status = fields.String()


class InvoiceData(InvoiceAndCreditNoteData):
    invoice_number = fields.String()
    invoice_date = fields.String()
    invoice_amount = fields.String()
    invoice_url = fields.String()


class CreditNoteData(InvoiceAndCreditNoteData):
    credit_note_number = fields.String()
    credit_note_date = fields.String()
    credit_note_amount = fields.String()
    credit_note_url = fields.String()


class GetInvoicesAndCreditNotesResponseSchema(Schema):
    invoices = fields.Nested(InvoiceData, many=True)
    credit_notes = fields.Nested(CreditNoteData, many=True)


class GetInvoicesAndCreditNotesPDFResponseSchema(Schema):
    download_url = fields.String()


class GetInvoicesAndCreditNotesPDFArchiveSchema(Schema):
    from_date = fields.String()
    to_date = fields.String()

    @validates_schema
    def validate_date_range(self, data):
        _validate_date_range(data, threshold=31)
