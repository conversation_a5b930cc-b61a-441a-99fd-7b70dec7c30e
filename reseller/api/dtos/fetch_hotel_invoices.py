from marshmallow import Schema, ValidationError, fields, post_load, validates_schema


class FetchHotelInvoicesSchema(Schema):
    invoice_numbers = fields.String()
    unique_ref_ids = fields.String()

    @validates_schema
    def validate(self, data):
        invoice_numbers = data.get("invoice_numbers")
        unique_reference_ids = data.get("unique_ref_ids")
        if not (invoice_numbers or unique_reference_ids):
            raise ValidationError(
                "Either invoice_numbers or unique_ref_ids should be present"
            )

    @post_load
    def split_fields(self, data, **kwargs):
        if "invoice_numbers" in data:
            data["invoice_numbers"] = [
                num.strip() for num in data["invoice_numbers"].split(",") if num.strip()
            ]
        if "unique_ref_ids" in data:
            data["unique_ref_ids"] = [
                ref.strip() for ref in data["unique_ref_ids"].split(",") if ref.strip()
            ]
        return data
