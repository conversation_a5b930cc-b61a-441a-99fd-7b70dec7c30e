from marshmallow import Schema, ValidationError, fields

from core.hotel.constants import HotelCourse


def validate_sold_as(sold_as):
    if sold_as.lower() not in [
        HotelCourse.RESELLER.lower(),
        HotelCourse.MARKETPLACE.lower(),
    ]:
        raise ValidationError("sold as should be either 'reseller' or 'marketplace'")


class HotelCourseSchema(Schema):
    hotel_id = fields.String(required=True)
    date = fields.String(required=True)
    sold_as = fields.String(required=True, validate=validate_sold_as)


class HotelIngestionRequestSchema(Schema):
    hotels = fields.Nested(HotelCourseSchema(many=True))
