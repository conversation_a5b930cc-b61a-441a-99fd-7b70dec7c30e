import logging

from flask import request

from core.common.api.treebo_api import TreeboBaseAPI
from reseller.api.dtos.invoice_reports import (
    GetResellerInvoiceReportsRequest,
    GetResellerInvoiceReportsResponse,
)
from reseller.api.request_parser import RequestTypes, schema_wrapper_parser
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)
from reseller.services.hotel_invoice.send_hotel_invoice_report import (
    get_reseller_invoice_reports,
)

logger = logging.getLogger(__name__)


class GetResellerInvoiceReports(TreeboBaseAPI):
    @schema_wrapper_parser(
        GetResellerInvoiceReportsRequest, param_type=RequestTypes.ARGS
    )
    def get(self, **kwargs):
        """
        We send hotel invoice reports for date passed
        """
        logger.info(
            "Invoice Partner Report request with params, {res}".format(res=request.args)
        )
        request_data = kwargs.get("parsed_request")
        invoice_no = request_data.get("invoice_numbers")
        booking_id = request_data.get("booking_id")
        if invoice_no:
            invoice_numbers = invoice_no.split(",")
            reseller_invoice_reports = get_hotel_invoice_reports_repository().get_reseller_reports_by_invoice_numbers(
                invoice_numbers=invoice_numbers
            )
        elif booking_id:
            reseller_invoice_reports = get_hotel_invoice_reports_repository().get_reseller_reports_by_booking_id(
                booking_id=booking_id
            )
        else:
            reseller_invoice_reports = get_reseller_invoice_reports(
                request_data["date"]
            )
        response = GetResellerInvoiceReportsResponse().dump(
            reseller_invoice_reports, many=True
        )
        return {"data": response.data}
