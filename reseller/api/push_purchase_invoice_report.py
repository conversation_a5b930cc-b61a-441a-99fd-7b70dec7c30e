import datetime
import logging

from _datetime import timedelta

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.constants import PurchaseInvoiceReportStatus
from core.common.utils.date import utc_to_ist
from core.notification.handlers.navision import (
    send_email_notification,
    send_slack_notification,
)
from object_registry import locate_instance
from reseller.api.dtos.push_purchase_report import PushPurchaseReportSchema
from reseller.api.request_parser import schema_wrapper_parser
from reseller.reporting.nav_purchase_report.nav_purchase_report_service import (
    NavisionReportingService,
)

logger = logging.getLogger(__name__)


class PushInvoiceReportToNavision(TreeboBaseAPI):
    @schema_wrapper_parser(PushPurchaseReportSchema)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        for_date = request_data.get("date")
        status = request_data.get("status", PurchaseInvoiceReportStatus.PENDING)
        invoice_numbers = request_data.get("invoice_numbers")
        unique_ref_id = request_data.get("unique_ref_id")
        try:
            logger.info(
                f"Started invoice pushing for args date: {for_date}, inv_nums: {invoice_numbers or unique_ref_id}, status: {status}"
            )
            nav_reporting_service: NavisionReportingService = locate_instance(
                NavisionReportingService
            )
            if invoice_numbers or unique_ref_id:
                nav_reporting_service.push_hotel_invoices(
                    invoice_numbers=invoice_numbers,
                    unique_ref_id=unique_ref_id,
                    status=status,
                )
                return "Data push requested successfully"
            if not for_date:
                for_date = (
                    utc_to_ist(datetime.datetime.utcnow() - timedelta(days=1))
                    .date()
                    .strftime("%Y-%m-%d")
                )
            nav_reporting_service.push_hotel_invoices_for_date(
                date=for_date, status=status
            )
            return "Data push requested successfully"
        except Exception as e:
            msg = f"Navision Purchase report push has failed with error: {e.args[0]} ARGS: date: {for_date}, inv_nums: {invoice_numbers}, status: {status}"
            logger.exception(msg)
            send_slack_notification(msg)
            send_email_notification(msg, for_date)
            raise e
