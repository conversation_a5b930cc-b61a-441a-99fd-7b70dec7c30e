# pylint: disable=invalid-name
import logging
from datetime import datetime, timedelta

from flask import Blueprint, request

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.constants import EntityType
from reseller.repository.invoice_report_failure import (
    get_invoice_report_failure_repository,
)
from reseller.services.hotel_invoice.create_invoice_report import (
    CreateHotelInvoiceReport,
)

logger = logging.getLogger(__name__)

bulk_generate_hotel_invoice_report_blue_print = Blueprint(
    "bulk_generate_hotel_invoice_report",
    __name__,
    url_prefix="/bulk-generate-hotel-invoice_report",
)


class BulkGenerateHotelInvoiceReport(TreeboBaseAPI):
    def post(self):
        request_data = request.get_json(silent=True) or {}
        if "date" in request_data:
            date = request_data["date"]
        else:
            date = (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        repository = get_invoice_report_failure_repository()
        failures = repository.get_failures_for_date(date)
        hotel_credit_note_numbers, hotel_invoice_numbers = [], []
        for item in failures:
            if item.entity_type == EntityType.CreditNote:
                hotel_credit_note_numbers.append(item.invoice_number)
            else:
                hotel_invoice_numbers.append(item.invoice_number)
        generate_invoice_report = CreateHotelInvoiceReport(
            hotel_credit_note_numbers=hotel_credit_note_numbers,
            hotel_invoice_numbers=hotel_invoice_numbers,
        )
        generate_invoice_report.create_hotel_invoice_report()
        return f"Request is processed"


bulk_generate_hotel_invoice_report_blue_print.add_url_rule(
    "/retry",
    view_func=BulkGenerateHotelInvoiceReport.as_view(
        "bulk_hotel_report_generation_retry"
    ),
)
