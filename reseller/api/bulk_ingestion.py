# pylint: disable=invalid-name
import logging
from datetime import datetime, timedelta

from flask import Blueprint, request

from core.common.api.treebo_api import TreeboBaseAPI
from object_registry import inject
from reseller.repository.ingestion_audit import get_ingestion_failure_repository
from reseller.services.ingestion.ingestion_service import IngestionService

logger = logging.getLogger(__name__)

bulk_ingestion_blue_print = Blueprint(
    "bulk_ingestion", __name__, url_prefix="/bulk-ingestion"
)


class BulkRetryIngestion(TreeboBaseAPI):
    @inject(ingestion_service=IngestionService)
    def post(self, ingestion_service):
        request_data = request.get_json(silent=True) or {}
        if "date" in request_data:
            date = request_data["date"]
        else:
            date = (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        failures = get_ingestion_failure_repository().get_failures_for_date(date)

        for item in failures:
            ingestion_service.ingest(item.resource_type, item.resource_id)
        return f"Request is processed"


bulk_ingestion_blue_print.add_url_rule(
    "/retry", view_func=BulkRetryIngestion.as_view("bulk_ingestion_retry")
)
