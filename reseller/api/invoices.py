import datetime
import logging

from flask import Blueprint, request

from core.common.api.treebo_api import TreeboBaseAPI
from reseller.api.dtos.send_invoices_and_reports_schema import (
    GetInvoicesAndCreditNotesPDFArchiveSchema,
    GetInvoicesAndCreditNotesPDFResponseSchema,
    GetInvoicesAndCreditNotesResponseSchema,
    GetInvoicesAndCreditNotesSchema,
)
from reseller.api.request_parser import RequestTypes, schema_wrapper_parser
from reseller.services.invoices import InvoiceAndCreditNoteService

logger = logging.getLogger(__name__)

invoices_blue_print = Blueprint(
    "invoicesandcreditnotes",
    __name__,  # pylint: disable=invalid-name
    url_prefix="/invoicesandcreditnotes",
)


class GetInvoicesAndCreditNotes(TreeboBaseAPI):
    """
    Fetches the invoices & credit notes basic details based on the criteria
    """

    @schema_wrapper_parser(
        GetInvoicesAndCreditNotesSchema, param_type=RequestTypes.ARGS
    )
    def get(self, hotel_id, **kwargs):
        request_data = kwargs.get("parsed_request")
        data = InvoiceAndCreditNoteService().get_invoices_and_credit_notes(
            hotel_id=hotel_id, parsed_data=request_data
        )
        response = GetInvoicesAndCreditNotesResponseSchema().dump(data)
        return {"data": response.data}


class GetInvoicesAndCreditNotesPDFArchive(TreeboBaseAPI):
    """
    Fetches the invoices & credit notes PDFs in a zip file for a given date range.
    """

    @schema_wrapper_parser(
        GetInvoicesAndCreditNotesPDFArchiveSchema, param_type=RequestTypes.ARGS
    )
    def get(self, hotel_id, **kwargs):
        request_data = kwargs.get("parsed_request")
        data = InvoiceAndCreditNoteService().get_invoices_and_credit_notes_pdfs(
            hotel_id, request_data
        )
        response = GetInvoicesAndCreditNotesPDFResponseSchema().dump(
            {"download_url": data}
        )
        return {"data": response.data}


class GetInvoicesOrCreditNotesPDF(TreeboBaseAPI):
    """
    Fetches the invoices & credit notes PDFs in a zip file for a given date range.
    """

    def get(self, hotel_id, document_number):
        data = InvoiceAndCreditNoteService().get_pdf_url(hotel_id, document_number)
        response = GetInvoicesAndCreditNotesPDFResponseSchema().dump(
            {"download_url": data}
        )
        return {"data": response.data}


invoices_blue_print.add_url_rule(
    '/<regex("[\w-]+"):hotel_id>/get-invoices-and-credit-notes',
    view_func=GetInvoicesAndCreditNotes.as_view("get_invoices_and_credit_notes"),
)

invoices_blue_print.add_url_rule(
    '/<regex("[\w-]+"):hotel_id>/get-pdf-archive',
    view_func=GetInvoicesAndCreditNotesPDFArchive.as_view("get_pdf_archive"),
)

invoices_blue_print.add_url_rule(
    '/<regex("[\w-]+"):hotel_id>/get-pdf/<regex("[\w\s.-]+"):document_number>',
    view_func=GetInvoicesOrCreditNotesPDF.as_view("get_pdf"),
)
