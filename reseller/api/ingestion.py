import logging

from flask import Blueprint, request

from core.common.api.api_response import APIResponse
from core.common.api.treebo_api import TreeboBaseAPI
from object_registry import inject
from reseller.services.ingestion.ingestion_service import IngestionService

logger = logging.getLogger(__name__)

ingestion_blue_print = Blueprint(
    "ingestion", __name__, url_prefix="/ingestion"
)  # pylint: disable=invalid-name


class IngestionAPI(TreeboBaseAPI):
    @staticmethod
    @inject(ingestion_service=IngestionService)
    def post(ingestion_service):
        request_data = request.json
        resource_id = request_data["resource_id"]
        resource_type = request_data["resource_type"]
        message = ingestion_service.ingest(resource_type, resource_id)
        if not message:
            message = f"Ingestion completed successfully for {resource_type} with id {resource_id}"
        return APIResponse.build(status_code=200, data=message)


ingestion_blue_print.add_url_rule("/", view_func=IngestionAPI.as_view("ingest"))
