import datetime
import logging

from flask import Blueprint, send_file

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.date import get_dates_between_dates, utc_to_ist
from reseller.api.dtos.send_invoices_and_reports_schema import (
    GetInvoicesAndCreditNotesPDFResponseSchema,
    SendDailyInvoicesForDateRangeSchema,
    SendDailyInvoicesSchema,
)
from reseller.api.fetch_hotel_invoice_finance_reports import (
    FetchHotelInvoiceFinanceReports,
)
from reseller.api.generate_hotel_invoice_report import GenerateHotelInvoiceReport
from reseller.api.hotel_invoice_reports import GetResellerInvoiceReports
from reseller.api.push_job_queue import PushJobToQueue
from reseller.api.push_purchase_invoice_report import PushInvoiceReportToNavision
from reseller.api.push_purchase_report import PurchaseReportDataPushAPI
from reseller.api.request_parser import schema_wrapper_parser
from reseller.api.send_daily_invoice_report import (
    DispatchInvoiceReportsForDateRangeAPI,
    DispatchInvoiceReportToPartners,
    GetDailyInvoiceCsv,
)
from reseller.api.send_monthly_invoice_report import DispatchMonthlyReportToPartners
from reseller.api.update_invoices_for_report import UpdateInvoicesForReport
from reseller.services.hotel_invoice import (
    get_hotel_credit_note_html,
    get_hotel_invoice_html,
)
from reseller.services.hotel_invoice.get_hotel_credit_note_pdf import (
    get_hotel_credit_note_pdf_url,
)
from reseller.services.hotel_invoice.get_hotel_invoice_pdf import (
    get_hotel_invoice_pdf_url,
)
from reseller.services.invoices_to_be_sent import InvoiceDispatchService
from reseller.services.scripts.find_missing_reports import FindMissingReports

logger = logging.getLogger(__name__)

hotel_invoice_blue_print = Blueprint(
    "hotelinvoice", __name__, url_prefix="/hotelinvoice"  # pylint: disable=invalid-name
)


class HotelInvoiceViewAPI(TreeboBaseAPI):
    def get(self, invoice_number):
        html = get_hotel_invoice_html.as_html(invoice_number)
        return html


class HotelInvoiceDownloadPdfAPI(TreeboBaseAPI):
    def get(self, invoice_number):
        pdf_file = get_hotel_invoice_html.as_pdf(invoice_number)
        return send_file(
            pdf_file.name,
            attachment_filename=f"{invoice_number}.pdf",
            mimetype="application/pdf",
        )


class DispatchInvoicesAPI(TreeboBaseAPI):
    @schema_wrapper_parser(SendDailyInvoicesSchema)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        date = request_data.get("date")
        if date:
            date = utc_to_ist(datetime.datetime.strptime(date, "%Y-%m-%d")).strftime(
                "%Y-%m-%d"
            )
        else:
            date = (
                utc_to_ist(datetime.datetime.utcnow() - datetime.timedelta(days=1))
                .date()
                .strftime("%Y-%m-%d")
            )
        hotel_ids = request_data.get("hotel_ids", [])
        data = InvoiceDispatchService().send_invoices(date=date, hotel_ids=hotel_ids)
        response = {
            "msg": "Successfully sent invoices for specified hotel_ids except the failed_hotel_ids.",
            "failed_hotelIds": data["failed_hotel_ids"],
            "passed_hotelIds": data["passed_hotel_ids"],
        }
        return response


class HotelCreditNoteAPI(TreeboBaseAPI):
    def get(self, credit_note_number, hotel_id):
        html = get_hotel_credit_note_html.as_html(credit_note_number, hotel_id)
        return html


class DispatchInvoicesForDateRangeAPI(TreeboBaseAPI):
    @schema_wrapper_parser(SendDailyInvoicesForDateRangeSchema)
    def post(self, **kwargs):

        request_data = kwargs.get("parsed_request")
        start_date = request_data["from_date"]
        end_date = request_data["to_date"]
        hotel_ids = request_data["hotel_ids"]
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        dates_between_start_and_end = get_dates_between_dates(end_date, start_date)
        response = dict()
        failed_hotel_ids = dict()
        for date in dates_between_start_and_end:
            response_from_service = InvoiceDispatchService().send_invoices(
                date.strftime("%Y-%m-%d"), hotel_ids
            )
            logger.info(
                f"For date {date} the passed_hotel_ids are {response_from_service['passed_hotel_ids']} "
                f"and failed_hotel_ids are {response_from_service['failed_hotel_ids']}"
            )
            if response_from_service["failed_hotel_ids"]:
                failed_hotel_ids[date.strftime("%Y-%m-%d")] = response_from_service[
                    "failed_hotel_ids"
                ]
        if failed_hotel_ids:
            response["msg"] = "Some or all invoices are not sent for some hotel_ids "
            response["failed_hotelIds"] = failed_hotel_ids
            return response
        response[
            "msg"
        ] = "Invoices are successfully sent for all hotel_ids for given date range"
        return response


class GetHotelInvoicePDF(TreeboBaseAPI):
    """
    Fetches the invoices pdf.
    """

    def get(self, hotel_id, invoice_number):
        data = get_hotel_invoice_pdf_url(hotel_id, invoice_number)
        response = GetInvoicesAndCreditNotesPDFResponseSchema().dump(
            {"download_url": data}
        )
        return {"data": response.data}


class GetHotelCreditNotePDF(TreeboBaseAPI):
    """
    Fetches the credit note pdf.
    """

    def get(self, hotel_id, credit_note_number):
        data = get_hotel_credit_note_pdf_url(hotel_id, credit_note_number)
        response = GetInvoicesAndCreditNotesPDFResponseSchema().dump(
            {"download_url": data}
        )
        return {"data": response.data}


hotel_invoice_blue_print.add_url_rule(
    "generate-report",
    view_func=GenerateHotelInvoiceReport.as_view("hotel_invoice_report"),
)

hotel_invoice_blue_print.add_url_rule(
    "dispatch-invoices", view_func=DispatchInvoicesAPI.as_view("dispatch_invoices")
)

# pylint: disable=anomalous-backslash-in-string
hotel_invoice_blue_print.add_url_rule(
    '/<regex("[\w\s.-]+"):invoice_number>',
    view_func=HotelInvoiceViewAPI.as_view("html_view"),
)

# pylint: disable=anomalous-backslash-in-string
hotel_invoice_blue_print.add_url_rule(
    'download-pdf/<regex("[\w\s.-]+"):invoice_number>',
    view_func=HotelInvoiceDownloadPdfAPI.as_view("download_pdf"),
)

# pylint: disable=anomalous-backslash-in-string
hotel_invoice_blue_print.add_url_rule(
    '/credit_note/<regex("[\w\s.-]+"):credit_note_number>/<regex("[\w-]+"):hotel_id>',
    view_func=HotelCreditNoteAPI.as_view("credit_note_html_view"),
)

hotel_invoice_blue_print.add_url_rule(
    "update-invoices-for-report",
    view_func=UpdateInvoicesForReport.as_view("update_invoices_for_report"),
)

hotel_invoice_blue_print.add_url_rule(
    "get-reseller-invoice-reports",
    view_func=GetResellerInvoiceReports.as_view("get_reseller_invoice_reports"),
)

hotel_invoice_blue_print.add_url_rule(
    "dispatch-invoice-reports",
    view_func=DispatchInvoiceReportToPartners.as_view("dispatch_invoice_reports"),
)

hotel_invoice_blue_print.add_url_rule(
    "dispatch-monthly-invoice-reports",
    view_func=DispatchMonthlyReportToPartners.as_view(
        "dispatch_monthly_invoice_" "reports"
    ),
)

hotel_invoice_blue_print.add_url_rule(
    "hotel-daily-invoice-csv",
    view_func=GetDailyInvoiceCsv.as_view("hotel_daily_invoice_csv"),
)

hotel_invoice_blue_print.add_url_rule(
    "dispatch-invoices-for-date-range",
    view_func=DispatchInvoicesForDateRangeAPI.as_view(
        "dispatch_invoices_" "for_date_range"
    ),
)

hotel_invoice_blue_print.add_url_rule(
    "dispatch-invoice-reports-for-date-range",
    view_func=DispatchInvoiceReportsForDateRangeAPI.as_view(
        "dispatch_invoice_reports" " for_date_range"
    ),
)
hotel_invoice_blue_print.add_url_rule(
    "find-missing-reports", view_func=FindMissingReports.as_view("find_missing_reports")
)

hotel_invoice_blue_print.add_url_rule(
    "push-invoice-reports-to-nav",
    view_func=PushInvoiceReportToNavision.as_view("push_invoice_reports_to_nav"),
)

hotel_invoice_blue_print.add_url_rule(
    "push-job-to-queue", view_func=PushJobToQueue.as_view("push_job_to_queue")
)

hotel_invoice_blue_print.add_url_rule(
    "push-invoice-reports-to-finance-portal",
    view_func=PurchaseReportDataPushAPI.as_view(
        "push_invoice_reports_to_finance_portal"
    ),
)

hotel_invoice_blue_print.add_url_rule(
    '/<regex("[\w-]+"):hotel_id>/get-hotel-invoice-pdf/<regex("[\w\s.-]+"):invoice_number>',
    view_func=GetHotelInvoicePDF.as_view("get_hotel_invoice_pdf"),
)


hotel_invoice_blue_print.add_url_rule(
    '/<regex("[\w-]+"):hotel_id>/get-hotel-credit_note-pdf/<regex("[\w\s.-]+"):credit_note_number>',
    view_func=GetHotelCreditNotePDF.as_view("get_hotel_credit_note_pdf"),
)

hotel_invoice_blue_print.add_url_rule(
    "finance-reports",
    view_func=FetchHotelInvoiceFinanceReports.as_view(
        "get_hotel_invoice_finance_reports"
    ),
)
