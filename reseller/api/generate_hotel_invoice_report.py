import datetime
import logging

from flask import request

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.date import utc_to_ist
from reseller.api.dtos.send_invoices_and_reports_schema import GenerateReportsSchema
from reseller.api.request_parser import schema_wrapper_parser
from reseller.services.hotel_invoice.create_invoice_report import (
    CreateHotelInvoiceReport,
)

logger = logging.getLogger(__name__)


class GenerateHotelInvoiceReport(TreeboBaseAPI):
    """
    If report date and hotel_ids are not passed then
    generates hotel invoice reports for previous date for all hotels
    """

    @schema_wrapper_parser(GenerateReportsSchema)
    def post(self, **kwargs):
        request_data = kwargs.get("parsed_request")
        report_date = request_data.get("date")
        customer_invoice_numbers = request_data.get("invoice_numbers")
        customer_credit_note_numbers = request_data.get("credit_note_numbers")
        hotel_invoice_numbers = request_data.get("hotel_invoice_numbers")
        hotel_credit_note_numbers = request_data.get("hotel_credit_note_numbers")
        if (
            customer_invoice_numbers
            or customer_credit_note_numbers
            or hotel_invoice_numbers
            or hotel_credit_note_numbers
        ):
            logger.info(
                f"Running report for invoice ids {customer_invoice_numbers} {hotel_invoice_numbers} "
                f"and credit_note_numbers {customer_credit_note_numbers} {hotel_credit_note_numbers}"
            )
            CreateHotelInvoiceReport(
                customer_invoice_numbers=customer_invoice_numbers,
                customer_credit_note_numbers=customer_credit_note_numbers,
                hotel_invoice_numbers=hotel_invoice_numbers,
                hotel_credit_note_numbers=hotel_credit_note_numbers,
            ).create_hotel_invoice_report()
            return f"Invoice reports created successfully"
        if report_date:
            report_date = utc_to_ist(
                datetime.datetime.strptime(report_date, "%Y-%m-%d")
            ).date()
        else:
            report_date = utc_to_ist(
                datetime.datetime.utcnow()
            ).date() - datetime.timedelta(1)
        hotel_ids = request_data.get("hotel_ids", [])
        logger.info(f"Running report for date {report_date} and hotel_ids {hotel_ids}")
        CreateHotelInvoiceReport(
            report_date=report_date, hotel_ids=hotel_ids
        ).create_hotel_invoice_report()
        return f"Invoice reports created successfully"
