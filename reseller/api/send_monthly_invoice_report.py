import datetime

from flask import request

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.date import utc_to_ist
from reseller.api.dtos.send_invoices_and_reports_schema import SendMonthlyReportsSchema
from reseller.api.generate_hotel_invoice_report import logger
from reseller.api.request_parser import schema_wrapper_parser
from reseller.services.hotel_invoice.send_hotel_invoice_report import (
    send_monthly_invoice_report,
    send_monthly_invoice_report_for_all_hotels,
)


class DispatchMonthlyReportToPartners(TreeboBaseAPI):
    @schema_wrapper_parser(SendMonthlyReportsSchema)
    def post(self, **kwargs):
        """
        if month or year information is not present, then we send the monthly buy invoice for the previous month
        :return:
        """
        response = dict()
        request_data = kwargs.get("parsed_request")
        month = request_data.get("month", None)
        year = request_data.get("year", None)
        hotel_ids = request_data.get("hotel_ids", [])
        if not month and not year:
            today = utc_to_ist(datetime.datetime.utcnow()).date()
            month = today.month
            year = today.year
            if month == 1:
                month = 12
                year -= 1
            else:
                month -= 1
        if not hotel_ids:
            status, response_from_service = send_monthly_invoice_report_for_all_hotels(
                month=month, year=year
            )
        else:
            status, response_from_service = send_monthly_invoice_report(
                month=month, year=year, hotel_ids=hotel_ids
            )
        invoice_report_dispatcher_response = (
            "Invoice report dispatcher response: {status}".format(status=status)
        )
        logger.info(invoice_report_dispatcher_response)
        if response_from_service["failed_hotel_ids"]:
            response["msg"] = "Monthly Invoice Report not sent for "
            response["failed_hotelIds"] = response_from_service["failed_hotel_ids"]
            return response
        response["msg"] = "Monthly Invoice Report successfully sent for given hotel_ids"
        return response
