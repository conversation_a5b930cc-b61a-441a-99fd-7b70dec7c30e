from reseller.repository.customer_credit_note import get_customer_credit_note_repository


class CreateOrUpdateCustomerCreditNote:
    def __init__(self, customer_credit_note):
        self.customer_credit_note = customer_credit_note

    def create_or_update_customer_credit_note(self):
        query = {
            "booking_id": self.customer_credit_note.booking_id,
            "hotel_id": self.customer_credit_note.hotel_id,
            "invoice_id": self.customer_credit_note.credit_note_id,
        }
        get_customer_credit_note_repository().update(
            query=query, dict_or_data_class=self.customer_credit_note, upsert=True
        )
        return self.customer_credit_note


class CreateOrUpdateCustomerCreditNotes:
    def __init__(self, customer_credit_notes):
        self.customer_credit_notes = customer_credit_notes

    def create_or_update_customer_credit_notes(self):
        queries = []
        for customer_credit_note in self.customer_credit_notes:
            query = {
                "booking_id": customer_credit_note.booking_id,
                "hotel_id": customer_credit_note.hotel_id,
                "invoice_id": customer_credit_note.credit_note_id,
            }
            queries.append(query)
        get_customer_credit_note_repository().bulk_update(
            queries=queries, data_list=self.customer_credit_notes
        )
