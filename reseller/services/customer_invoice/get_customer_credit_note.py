from reseller.repository.customer_credit_note import get_customer_credit_note_repository


class GetCustomerCreditNote:
    def __init__(self, booking, hotel, pms_type):
        self.booking = booking
        self.hotel = hotel
        self.pms_type = pms_type

    def get_invoices(self):
        customer_credit_notes = get_customer_credit_note_repository().find(
            booking_id=self.booking.booking_id,
            hotel_id=self.hotel.uid,
        )
        return customer_credit_notes
