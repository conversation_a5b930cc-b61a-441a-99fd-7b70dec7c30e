from reseller.repository.customer_invoice import get_customer_invoice_repository


class GetCustomerInvoice:
    def __init__(self, invoice_id, hotel_id, **kwargs):
        self.invoice_id = invoice_id
        self.hotel_id = hotel_id
        self.kwargs = kwargs

    @classmethod
    def from_invoice_number(cls, invoice_number):
        customer_invoice = get_customer_invoice_repository().get(
            invoice_number=invoice_number
        )
        return customer_invoice

    def get_invoice(self):
        customer_invoice = get_customer_invoice_repository().get(
            invoice_id=self.invoice_id, hotel_id=self.hotel_id
        )
        return customer_invoice
