from core.customer_invoice.integrations.crs.invoice import Invoice
from reseller.repository.customer_invoice import get_customer_invoice_repository


class GetCustomerInvoices:
    def __init__(self, booking, hotel, pms_type, invoice_number=None):
        self.booking = booking
        self.hotel = hotel
        self.pms_type = pms_type
        self.invoice_number = invoice_number

    def from_db(self):
        query = dict(
            booking_id=self.booking.booking_id,
            hotel_id=self.hotel.uid,
            pms_type=self.pms_type,
        )
        if self.invoice_number:
            query["invoice_number"] = self.invoice_number
        customer_invoices = get_customer_invoice_repository().find(**query)
        return customer_invoices

    def from_pms(self):
        customer_invoices = Invoice.get_invoices(
            booking=self.booking,
            hotel=self.hotel,
        )
        return customer_invoices
