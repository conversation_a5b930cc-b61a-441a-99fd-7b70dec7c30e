from reseller.repository.customer_invoice import get_customer_invoice_repository


class CreateOrUpdateCustomerInvoice:
    def __init__(self, customer_invoice):
        self.customer_invoice = customer_invoice

    def create_or_update_invoice(self):
        query = {
            "hotel_id": self.customer_invoice.hotel_id,
            "invoice_id": self.customer_invoice.invoice_id,
            "pms_type": self.customer_invoice.pms_type,
        }
        get_customer_invoice_repository().update(
            query=query, dict_or_data_class=self.customer_invoice, upsert=True
        )
        return self.customer_invoice


class CreateOrUpdateCustomerInvoices:
    def __init__(self, customer_invoices=None):
        self.customer_invoices = customer_invoices

    def create_or_update_invoices(self):
        queries = []
        for customer_invoice in self.customer_invoices:
            query = {
                "hotel_id": customer_invoice.hotel_id,
                "invoice_id": customer_invoice.invoice_id,
                "pms_type": customer_invoice.pms_type,
            }
            queries.append(query)
        get_customer_invoice_repository().bulk_update(
            queries=queries, data_list=self.customer_invoices
        )
