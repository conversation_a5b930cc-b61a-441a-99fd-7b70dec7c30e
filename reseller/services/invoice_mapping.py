from core.invoice_mapping.data_class.invoice_mapping import InvoiceMapping
from reseller.repository.invoice_mapping import get_invoice_mapping_repository


def update_invoice_mapping(mapping: InvoiceMapping, fields_to_be_updated: dict):
    query = {
        "hotel_id": mapping.hotel_id,
        "pms_type": mapping.pms_type,
        "customer_invoice_id": mapping.customer_invoice_id,
        "hotel_invoice_id": mapping.hotel_invoice_id,
    }
    data_to_be_updated = fields_to_be_updated or mapping
    get_invoice_mapping_repository().update(query, data_to_be_updated)


class MappingService:
    def __init__(self, hotel, pms_type):
        self.hotel = hotel
        self.pms_type = pms_type

    def get(self, customer_invoice_id, proforma: bool) -> InvoiceMapping:
        query = self._make_query(customer_invoice_id, proforma=proforma)
        return get_invoice_mapping_repository().get(**query)

    def create(
        self,
        customer_invoice_id,
        customer_invoice_number,
        hotel_invoice_id,
        hotel_invoice_number,
        booking_id,
        proforma: bool,
    ):

        if proforma:
            customer_invoice_pi_id = customer_invoice_id
            customer_invoice_pi_number = customer_invoice_number
            customer_invoice_id = ""
            customer_invoice_number = ""
        else:
            customer_invoice_pi_id = ""
            customer_invoice_pi_number = ""
            customer_invoice_id = customer_invoice_id
            customer_invoice_number = customer_invoice_number

        mapping = InvoiceMapping(
            hotel_id=self.hotel.uid,
            pms_type=self.pms_type,
            booking_id=booking_id,
            customer_invoice_id=customer_invoice_id,
            customer_invoice_pi_id=customer_invoice_pi_id,
            hotel_invoice_id=hotel_invoice_id,
            hotel_invoice_number=hotel_invoice_number,
            customer_invoice_pi_number=customer_invoice_pi_number,
            customer_invoice_number=customer_invoice_number,
        )
        get_invoice_mapping_repository().create(mapping)

    def update(self, customer_invoice_id, data_to_be_updated: dict, proforma: bool):
        query = self._make_query(customer_invoice_id, proforma=proforma)
        mapping = get_invoice_mapping_repository().update(query, data_to_be_updated)
        return mapping

    def _make_query(self, customer_invoice_id, proforma: bool):
        query = {
            "hotel_id": self.hotel.uid,
            "pms_type": self.pms_type,
        }
        if proforma:
            query["customer_invoice_pi_id"] = customer_invoice_id
        else:
            query["customer_invoice_id"] = customer_invoice_id

        return query
