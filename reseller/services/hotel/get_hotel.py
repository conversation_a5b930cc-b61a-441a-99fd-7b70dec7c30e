from datetime import datetime

from core.common.data_classes.gst_details import GstDetails
from core.hotel.providers.get_hotel.catalogue import CatalogueGetHotel
from core.hotel.services.hotel import GetHotel as GetHotels
from reseller.exceptions import GstDoesNotExist
from reseller.repository.invoice_sequence import get_invoice_sequence_repository
from reseller.services.treebo_gst import TreeboGetGSTDetails
from reseller.workflows.hotel.filter_reseller_hotels import ResellerService


class GetHotel:
    def hotel_gst_address(self, hotel_id, date, pms_type):
        hotel = CatalogueGetHotel.from_pms_hotel_id(
            hotel_pms_id=hotel_id, pms_type=pms_type
        )
        date = datetime.strptime(date, "%Y-%m-%d")
        is_reseller = ResellerService().is_reseller(hotel.uid, date)
        if is_reseller:
            gst_details = TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details()
            if not gst_details.gstin_number:
                raise GstDoesNotExist(hotel.legal_state_code)
            return self.convert_gst_details_to_json(
                gst_details, hotel.gst_details, True
            )
        empty_gstin_details = GstDetails.empty()
        return self.convert_gst_details_to_json(
            empty_gstin_details, hotel.gst_details, False
        )

    def convert_gst_details_to_json(
        self, treebo_gstin_details, hotel_gstin_details, is_reseller
    ):
        gst_address = treebo_gstin_details.address
        treebo_state_office_address = [
            gst_address.field1,
            gst_address.field2,
            gst_address.city,
            gst_address.state,
            "-",
            gst_address.pincode,
        ]
        hotel_gst_address = hotel_gstin_details.address
        hotel_address = [
            hotel_gst_address.field1,
            hotel_gst_address.field2,
            hotel_gst_address.city,
            hotel_gst_address.state,
            "-",
            hotel_gst_address.pincode,
        ]

        data = {
            "is_reseller": is_reseller,
            "hotel_address": {
                "legal_name": hotel_gstin_details.legal_name,
                "gstin": hotel_gstin_details.gstin_number,
                "address": " ".join(filter(None, hotel_address)),
            },
            "treebo_state_office_address": {
                "legal_name": treebo_gstin_details.legal_name,
                "gstin": treebo_gstin_details.gstin_number,
                "address": " ".join(filter(None, treebo_state_office_address)),
            },
        }
        return data

    def extract_gstin_from_invoice_sequence(self, from_date, to_date, hotel_id):
        date_range_specific_invoice_sequence = (
            get_invoice_sequence_repository().get_date_specific_gstin(
                from_date=from_date, to_date=to_date, hotel_id=hotel_id
            )
        )
        return date_range_specific_invoice_sequence.gstin

    def get_all_hotels(self):
        hotel_sequences = get_invoice_sequence_repository().distinct({}, "hotel_id")
        return [GetHotels(hotel_id).get_hotel() for hotel_id in hotel_sequences]
