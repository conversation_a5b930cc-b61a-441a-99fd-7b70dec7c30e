import uuid

from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from reseller.exceptions import HotelInvoiceNumberNotReceived
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.services.treebo_gst import TreeboGetGSTDetails
from workers import constants
from workers.utils import send_msg_to_slack


class CreateHotelInvoice:
    def __init__(self, hotel, **kwargs):
        self.hotel = hotel
        self.kwargs = kwargs

    def create_invoice(self, created_at=None) -> HotelInvoice:
        uid = str(uuid.uuid4())

        invoice_number = self.kwargs.get("invoice_number")
        gst_details = self.kwargs.get("gst_details")
        if not gst_details:
            gst_details = TreeboGetGSTDetails(
                self.hotel.legal_state_code
            ).get_gst_details()

        if not invoice_number:
            slack_data = (
                "[RESELLER] Creating bare hotel invoice for issued by type hotel, failed for booking id "
                "{booking_id}".format(booking_id=self.kwargs.get("booking_id"))
            )
            send_msg_to_slack(msg=slack_data, url=constants.B2B_APP_ALERTS)
            raise HotelInvoiceNumberNotReceived

        bare_invoice = HotelInvoice(
            invoice_id=uid,
            hotel_id=self.hotel.uid,
            booking_id=self.kwargs["booking_id"],
            pms_type=self.kwargs["pms_type"],
            issued_to_type=self.kwargs["issued_to_type"],
            invoice_number=invoice_number,
            hotel=convert_hotel_to_invoice_hotel(self.hotel, self.kwargs["pms_type"]),
            gst_details=gst_details,
            line_items=[],
            source=self.kwargs.get("source") or "",
            bill_id=self.kwargs.get("bill_id"),
            invoice_date=self.kwargs.get("invoice_date", ""),
            is_locked_in_crs=self.kwargs.get("is_locked_in_crs", False),
        )
        get_hotel_invoice_repository().create(bare_invoice, created_at=created_at)
        return bare_invoice

    @classmethod
    def create_invoice_from_dataclass(cls, hotel_invoice, created_at=None):
        assert isinstance(hotel_invoice, HotelInvoice)
        get_hotel_invoice_repository().create(hotel_invoice, created_at=created_at)
