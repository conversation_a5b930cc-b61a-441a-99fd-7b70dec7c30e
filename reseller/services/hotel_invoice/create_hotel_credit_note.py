import uuid

from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from reseller.exceptions import HotelCreditNoteNumberNotReceived
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.services.treebo_gst import TreeboGetGSTDetails
from workers import constants
from workers.utils import send_msg_to_slack


class CreateHotelCreditNote:
    def __init__(self, hotel, **kwargs):
        self.hotel = hotel
        self.kwargs = kwargs

    def create_hotel_credit_note(self, created_at=None) -> HotelCreditNote:
        uid = str(uuid.uuid4())
        credit_note_number = self.kwargs.get("credit_note_number")
        gst_details = self.kwargs.get("gst_details")
        if not gst_details:
            gst_details = TreeboGetGSTDetails(
                self.hotel.legal_state_code
            ).get_gst_details()

        if not credit_note_number:
            slack_data = (
                "[RESELLER] Creating bare hotel credit note for issued by type hotel, failed for booking id "
                "{booking_id}".format(booking_id=self.kwargs.get("booking_id"))
            )
            send_msg_to_slack(msg=slack_data, url=constants.B2B_APP_ALERTS)
            raise HotelCreditNoteNumberNotReceived

        bare_credit_note = HotelCreditNote(
            credit_note_id=uid,
            hotel_id=self.hotel.uid,
            booking_id=self.kwargs["booking_id"],
            pms_type=self.kwargs["pms_type"],
            issued_to_type=self.kwargs["issued_to_type"],
            credit_note_number=credit_note_number,
            hotel=convert_hotel_to_invoice_hotel(self.hotel, self.kwargs["pms_type"]),
            gst_details=gst_details,
            line_items=[],
            source=self.kwargs.get("source") or "",
            reference_invoice_numbers=self.kwargs["reference_invoice_numbers"],
            bill_id=self.kwargs.get("bill_id") or "",
            credit_note_date=self.kwargs.get("credit_note_date", ""),
        )
        get_hotel_credit_note_repository().create(
            bare_credit_note, created_at=created_at
        )
        return bare_credit_note

    @classmethod
    def create_hotel_credit_note_from_dataclass(
        cls, hotel_credit_note, created_at=None
    ):
        assert isinstance(hotel_credit_note, HotelCreditNote)
        get_hotel_credit_note_repository().create(
            hotel_credit_note, created_at=created_at
        )
