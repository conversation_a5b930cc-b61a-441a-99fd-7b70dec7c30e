# pylint: disable=inconsistent-return-statements

import logging

from core.common.constants import InvoiceStatus, IssuedToTypes
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.services.diff_and_patch_hotel_invoice import (
    diff_hotel_invoice,
    patch_hotel_invoice,
)
from reseller.constants import IngestionTypes
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.invoices_to_be_sent import save_invoices_to_be_sent

logger = logging.getLogger(__name__)


class UpdateHotelInvoice:
    def __init__(self, current_hotel_invoice=None, **kwargs):
        self.current_hotel_invoice = current_hotel_invoice
        self.kwargs = kwargs

    def update_invoice(self, new_hotel_invoice) -> HotelInvoice:
        updated_invoice = self._update_hotel_invoice(new_hotel_invoice)
        if updated_invoice:
            get_hotel_invoice_repository().update(
                {"invoice_id": self.current_hotel_invoice.invoice_id}, updated_invoice
            )
        return new_hotel_invoice

    def update_invoices(self, new_hotel_invoice):
        updated_hotel_invoice = self._update_hotel_invoice(new_hotel_invoice)
        return updated_hotel_invoice

    def _update_hotel_invoice(self, new_hotel_invoice):
        old_hotel_invoice = GetHotelInvoice(
            invoice_id=self.current_hotel_invoice.invoice_id
        ).from_db()
        if diff_hotel_invoice(
            old_hotel_invoice=old_hotel_invoice, new_hotel_invoice=new_hotel_invoice
        ):
            if new_hotel_invoice.status in [
                InvoiceStatus.CANCELLED,
                InvoiceStatus.LOCKED,
            ]:
                save_invoices_to_be_sent(
                    new_hotel_invoice, entity_type=IngestionTypes.HOTEL_INVOICE
                )
            updated_document = patch_hotel_invoice(
                old_hotel_invoice=old_hotel_invoice, new_hotel_invoice=new_hotel_invoice
            )
            return updated_document


class UpdateHotelInvoices:
    def __init__(self, hotel_invoices):
        self.hotel_invoices = hotel_invoices

    def create_or_update_hotel_invoices(self):
        queries = []
        for hotel_invoice in self.hotel_invoices:
            query = {"invoice_id": hotel_invoice.invoice_id}
            queries.append(query)
        get_hotel_invoice_repository().bulk_update(
            queries=queries, data_list=self.hotel_invoices
        )
