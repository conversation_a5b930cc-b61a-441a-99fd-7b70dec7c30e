from core.booking.data_classes.booking import Booking
from core.common.constants import HsnCodes, InvoiceStatus, IssuedByTypes, IssuedToTypes
from core.common.data_classes.customer import Customer
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice
from core.hotel.data_classes.room import Room
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.constants import DEFAULT_TAX_CODES
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.data_classes.line_item import LineItem
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice


class GetHotelInvoiceFromCustomerInvoiceForIssuedToCustomer:
    def __init__(
        self,
        hotel_invoice_id,
        customer_invoice: CustomerInvoice,
        booking: Booking,
        **kwargs,
    ):
        self.hotel_invoice = GetHotelInvoice(hotel_invoice_id).from_db()
        self.customer_invoice = customer_invoice
        self.booking = booking
        self.hotel_invoice_id = hotel_invoice_id
        self.kwargs = kwargs

    def get_invoice(self) -> HotelInvoice:
        hotel_invoice = self.get_data_class()
        return hotel_invoice

    def get_data_class(self):
        hotel = GetHotel(self.booking.hotel_id).get_hotel()
        hotel_invoice = HotelInvoice(
            invoice_id=self.hotel_invoice.invoice_id,
            booking_id=self.hotel_invoice.booking_id,
            hotel_id=self.hotel_invoice.hotel_id,
            pms_type=self.hotel_invoice.pms_type,
            invoice_number=self.hotel_invoice.invoice_number,
            issued_to_type=self._issued_to_type(),
            hotel=convert_hotel_to_invoice_hotel(hotel, self.hotel_invoice.pms_type),
            gst_details=self.customer_invoice.gst_details,
            line_items=self._line_items(),
            status=self._status(),
            source=self.hotel_invoice.source,
            order_id=self.booking.order_id,
            check_in=self.customer_invoice.check_in or self.booking.checkin,
            check_out=self.customer_invoice.check_out or self.booking.checkout,
            bill_id=self.customer_invoice.bill_id,
            invoice_date=self.customer_invoice.invoice_date,
            irn=self.customer_invoice.irn,
            qr_code=self.customer_invoice.qr_code,
            is_einvoice=self.customer_invoice.is_einvoice,
            is_locked_in_crs=self.customer_invoice.is_locked_in_crs,
        )
        hotel_invoice.created_at = self.hotel_invoice.created_at
        return hotel_invoice

    def _issued_to_type(self):
        if self.customer_invoice.issued_by_type == IssuedByTypes.RESELLER:
            return IssuedToTypes.RESELLER
        else:
            return IssuedToTypes.CUSTOMER

    def _status(self):
        if self.customer_invoice.status == InvoiceStatus.CANCELLED:
            status = InvoiceStatus.CANCELLED
        else:
            status = InvoiceStatus.LOCKED
        return status

    def _line_items(self):
        line_items = []
        for item in self.customer_invoice.line_items:
            # blocking booking commission charges to go in hotel invoice
            if item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                continue
            raw_charge = self._charge(item.charge)
            charge = raw_charge
            room = item.room if item.room else self._room(item.room_id)
            customers = (
                item.customers if item.customers else self._customers(item.room_id)
            )
            line_item = LineItem(
                charge,
                room,
                customers,
            )
            line_items.append(line_item)
        return line_items

    def _charge(self, charge_item):
        if not charge_item.tax_breakup:
            charge_item.tax_breakup = frozenset(
                [TaxBreakup(code, 0, percent=0) for code in DEFAULT_TAX_CODES]
            )
        charge_item.sanitize_tax_codes()
        return charge_item

    def _room(self, room_id):
        try:
            room = self.get_room_booking_for_item(room_id)
            number = room.number
            room_type_name = room.type
        except ValueError:
            number = ""
            room_type_name = ""

        return Room(number=number, room_type_name=room_type_name)

    def _customers(self, room_id):
        customers = []

        try:
            room = self.get_room_booking_for_item(room_id)
            customers_in_associated_room = room.guests
        except ValueError:
            customers_in_associated_room = []

        for cus in customers_in_associated_room:
            customer = Customer(
                uid=cus.stay_id,
                name=(cus.first_name, cus.last_name),
                email=getattr(cus, "email", ""),
                phone_number=getattr(cus, "phone_number", ""),
            )
            customers.append(customer)

        return customers

    def get_room_booking_for_item(self, room_id):
        try:
            return [
                room for room in self.booking.rooms if room.room_booking_id == room_id
            ][0]
        except IndexError:
            raise ValueError(
                f"Invalid room booking id {room_id} for booking {self.booking.booking_id}"
            )
