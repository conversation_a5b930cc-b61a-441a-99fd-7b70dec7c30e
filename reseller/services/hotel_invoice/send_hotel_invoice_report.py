# pylint: disable=too-many-locals
import logging
import uuid

from core.common import file_storage_service
from core.common.utils.date import Date, maybe_convert_string_to_datetime
from core.common.utils.files import write_to_csv
from core.hotel.data_classes.hotel_detail import HotelOwner
from core.hotel.exceptions import NoOwnerEmailsFound
from core.hotel.providers.get_hotels_v2.catalogue import CatalogueGetHotelDetails
from core.hotel.services.hotel import GetHotel
from core.hotel.services.hotel_owner import GetHotelOwner
from core.hotel_invoice.constants import ReportConstants
from core.notification import notify
from core.notification.handlers.hotel_invoice_reports.daily_report_dispatcher import (
    HotelInvoiceDailyReportDispatcher,
)
from core.notification.handlers.hotel_invoice_reports.dispatch_failure import (
    HotelInvoiceReportDispatchFailure,
)
from core.notification.handlers.hotel_invoice_reports.monthly_report_dispatcher import (
    HotelInvoiceMonthlyReportDispatcher,
)
from reseller.repository.hotel_detail import get_hotel_detail_repository
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)
from reseller.repository.invoices_to_be_sent import get_invoices_to_be_sent_repository
from reseller.services.hotel_invoice.create_invoice_report import (
    CreateHotelInvoiceReport,
)
from reseller.services.invoice_report_failures_audit import (
    record_invoice_report_failure,
)

logger = logging.getLogger(__name__)

TEMP_ROOT_DIR = "/tmp"
TEMP_INVOICES_REPORT_STORAGE_DIR = f"{TEMP_ROOT_DIR}/hotel_invoice_reports"


def get_reseller_invoice_reports(for_date):
    reseller_invoice_reports = (
        get_hotel_invoice_reports_repository().get_issued_to_reseller_reports(
            for_date=for_date
        )
    )
    return reseller_invoice_reports


def get_csv_report_for_all_hotels(for_date):
    for_date = for_date.strftime("%Y-%m-%d")
    hotel_ids = get_hotel_invoice_reports_repository().get_hotel_ids_for_date_range(
        from_date=for_date, to_date=for_date
    )
    hotel_ids.extend(
        get_invoices_to_be_sent_repository().get_hotel_ids_for_date_range(
            from_date=for_date, to_date=for_date
        )
    )
    hotel_ids = set(hotel_ids)
    file_access_url = get_csv_report(
        maybe_convert_string_to_datetime(for_date), hotel_ids
    )
    return file_access_url


def get_csv_report(for_date, hotel_ids):
    logger.info(
        f"Getting csv for daily report for {for_date} and hotel_ids {hotel_ids}"
    )
    CreateHotelInvoiceReport(
        report_date=for_date, hotel_ids=hotel_ids
    ).create_hotel_invoice_report()
    for_date = for_date.strftime("%Y-%m-%d")

    aggregate_report_for_hotels = []

    file_access_url = ""
    try:
        invoice_reports = (
            get_hotel_invoice_reports_repository().get_reports_for_date_and_hotels(
                for_date=str(for_date), hotel_ids=hotel_ids
            )
        )
        if not invoice_reports:
            logger.info(
                f"For date: {for_date}, No invoices to push for hotels: {hotel_ids}"
            )
            return
        else:
            logger.info(
                f"For date: {for_date}, Number of records fetched for hotel_ids: {hotel_ids} = {len(invoice_reports)}"
            )

        data_rows = _extract_rows_from_invoice_reports(invoice_reports)
        aggregate_report_for_hotels.extend(data_rows)

        archive_file_name = _generate_csv_file(
            cs_id=str(for_date),
            folder_name=str(for_date),
            data_rows=aggregate_report_for_hotels,
        )
        file_access_url = _upload_hotel_invoice_report_to_file_storage(
            archive_file_name
        )
    except Exception as e:
        msg = f"Exception occurred while generating the CSV report for date {for_date} and hotel_ids {hotel_ids}"
        record_invoice_report_failure(
            process_name="get_csv_report",
            hotel_id=hotel_ids,
            error_message=msg,
            error_trace=e,
        )
    return file_access_url


def send_daily_invoice_report_for_all_hotels(for_date):
    hotel_ids = get_hotel_invoice_reports_repository().get_hotel_ids_for_date_range(
        from_date=for_date, to_date=for_date
    )
    logger.info(f"Sending daily report for {for_date} and hotel_ids {hotel_ids}")
    status, data = send_daily_invoice_report(for_date, hotel_ids)
    return status, data


def send_daily_invoice_report(for_date, hotel_ids):
    logger.info(f"Sending daily report for {for_date} and hotel_ids {hotel_ids}")
    failed_hotel_ids = set()
    for cs_id in hotel_ids:
        try:
            hotel = GetHotel(cs_id).get_hotel()
            invoice_reports = []
            for pms_type in ReportConstants.SUPPORTED_PMS_TYPES:
                invoice_reports += (
                    get_hotel_invoice_reports_repository().get_reports_for_date(
                        for_date=str(for_date),
                        hotel_id=hotel.pms_id(pms_type),
                        pms_type=pms_type,
                    )
                )

            logger.info(
                f"Processing hotel: {cs_id}, invoice_report_count: {len(invoice_reports)}"
            )
            if not invoice_reports:
                logger.info(f"No invoices to push for hotel: {cs_id}")
                continue
            data_rows = _extract_rows_from_invoice_reports(invoice_reports)
            archive_file_name = _generate_csv_file(
                cs_id=cs_id, folder_name=str(for_date), data_rows=data_rows
            )
            file_access_url = _upload_hotel_invoice_report_to_file_storage(
                archive_file_name
            )
            hotel_owner_emails = _get_owner_emails(cs_id=cs_id)
            if not hotel_owner_emails:
                message = f"No hotel owner emails found for hotel: {cs_id}, skipping report dispatch"
                logger.warning(message)
                notify(HotelInvoiceReportDispatchFailure(message=message).slack())
                raise NoOwnerEmailsFound
            tracking_id = notify(
                HotelInvoiceDailyReportDispatcher(
                    hotel_owner_emails=hotel_owner_emails,
                    hotel_name=invoice_reports[0].hotel_trade_name,
                    hotel_city=invoice_reports[0].billed_by_city,
                    report_date=for_date.strftime("%d %b %y"),
                    csv_file_url=file_access_url,
                ).email()
            )
            logger.info(
                f"Tracking id for report email hotel_id: {cs_id} is {tracking_id}, on date: {for_date}"
            )
        except Exception as e:
            msg = f"Daily Report Dispatch Error, hotel_id: {cs_id}, error: {e}"
            failed_hotel_ids.add(cs_id)
            logger.exception(msg)
            record_invoice_report_failure(
                process_name="send_daily_invoice_report",
                hotel_id=cs_id,
                error_message=msg,
                error_trace=e,
            )
            notify(HotelInvoiceReportDispatchFailure(message=msg).slack())
            continue
    data = {"failed_hotel_ids": list(failed_hotel_ids)}
    return True, data


def send_monthly_invoice_report_for_all_hotels(month, year):
    first_day_of_month = f"{year}-{month}-01"
    logger.info(
        f"Sending monthly report for month: {month}, year: {year} for all hotels"
    )
    _date = Date(first_day_of_month)
    from_date = _date.date()
    to_date = _date.get_last_day_of_month().date()
    hotel_ids = get_hotel_invoice_reports_repository().get_hotel_ids_for_date_range(
        from_date=from_date, to_date=to_date
    )
    status, data = send_monthly_invoice_report(month, year, hotel_ids)
    return status, data


def send_monthly_invoice_report(month, year, hotel_ids):
    failed_hotel_ids = set()
    first_day_of_month = f"{year}-{month}-01"
    logger.info(f"Sending monthly report for month: {month}, year: {year}")
    _date = Date(first_day_of_month)
    from_date = _date.date()
    to_date = _date.get_last_day_of_month().date()

    for cs_id in hotel_ids:
        try:
            hotel = GetHotel(cs_id).get_hotel()
            invoice_reports = []
            for pms_type in ReportConstants.SUPPORTED_PMS_TYPES:
                invoice_reports += (
                    get_hotel_invoice_reports_repository().get_reports_for_date_range(
                        from_date=str(from_date),
                        to_date=str(to_date),
                        hotel_id=hotel.pms_id(pms_type),
                        pms_type=pms_type,
                    )
                )
            logger.info(
                f"Processing hotel: {cs_id}, invoice_report_count: {len(invoice_reports)}"
            )
            if not invoice_reports:
                logger.info(f"No invoices to push for hotel: {cs_id}")
                continue
            data_rows = _extract_rows_from_invoice_reports(invoice_reports)
            archive_file_name = _generate_csv_file(
                cs_id=cs_id, folder_name=f"{month}-{year}", data_rows=data_rows
            )
            file_access_url = _upload_hotel_invoice_report_to_file_storage(
                archive_file_name
            )
            hotel_owner_emails = _get_owner_emails(cs_id=cs_id)
            if not hotel_owner_emails:
                message = f"No hotel owner emails found for hotel: {cs_id}, skipping report d ispatch"
                logger.warning(message)
                notify(HotelInvoiceReportDispatchFailure(message=message).slack())
                raise NoOwnerEmailsFound
            email_display_date = from_date.strftime("%b %y")
            tracking_id = notify(
                HotelInvoiceMonthlyReportDispatcher(
                    hotel_owner_emails=hotel_owner_emails,
                    hotel_name=invoice_reports[0].hotel_trade_name,
                    hotel_city=invoice_reports[0].billed_by_city,
                    report_date=email_display_date,
                    csv_file_url=file_access_url,
                ).email()
            )
            logger.info(
                f"Tracking id for report email hotel_id: {cs_id} is {tracking_id}, for month {month}, year {year}"
            )
        except Exception as e:
            msg = f"Monthly Report Dispatch Error, hotel_id: {cs_id}, error: {e}"
            failed_hotel_ids.add(cs_id)
            logger.exception(msg)
            record_invoice_report_failure(
                process_name="send_monthly_invoice_report",
                hotel_id=cs_id,
                error_message=msg,
                error_trace=e,
            )
            notify(HotelInvoiceReportDispatchFailure(message=msg).slack())
            continue
    data = {"failed_hotel_ids": list(failed_hotel_ids)}
    return True, data


def _generate_csv_file(cs_id, folder_name, data_rows):
    archive_file_name = (
        f"hotel_invoice_reports/{cs_id}/{folder_name}/{uuid.uuid4()}.csv"
    )
    csv_file_name = f"{TEMP_INVOICES_REPORT_STORAGE_DIR}/{archive_file_name}"
    write_to_csv(file_name=csv_file_name, list_of_rows=data_rows)
    return archive_file_name


def _extract_rows_from_invoice_reports(invoice_reports):
    reports = []
    for report in invoice_reports:
        delattr(report, "created_at")
        delattr(report, "booking_owner_name")
        delattr(report, "customer_invoice_number")
        delattr(report, "hotel_nav_code")
        reports.append(report)
    header_row = list(reports[0].__dict__.keys())
    data_rows = [inv.__dict__.values() for inv in reports]
    return [header_row] + data_rows


def _upload_hotel_invoice_report_to_file_storage(archive_file_name):
    file_location = f"{TEMP_INVOICES_REPORT_STORAGE_DIR}/{archive_file_name}"
    logger.info(f"Uploading from {file_location}, file_name: {archive_file_name}")
    file_storage_service.upload_file(file_location, archive_file_name)
    download_url = file_storage_service.get_signed_url(filepath=archive_file_name)
    logger.info(f"File download url: {download_url}")
    return download_url


def _get_owner_emails(cs_id):
    hotel_detail = get_hotel_detail_repository().get_hotel_detail_by_hotel_id(
        hotel_id=cs_id
    )
    if hotel_detail and hotel_detail.hotel_owners:
        return [ho.email for ho in hotel_detail.hotel_owners if ho.email]
    else:
        if not hotel_detail:
            hotel_detail = CatalogueGetHotelDetails.get_hotels(hotel_ids=[cs_id])
            get_hotel_detail_repository().create_or_update_hotel_detail(hotel_detail[0])
            return [
                hotel_owner.email
                for hotel_owner in hotel_detail[0].hotel_owners
                if hotel_owner.email
            ]
        hotel_owner_details = GetHotelOwner.get_hotel_owner(cs_id)
        hotel_detail.hotel_owners = []
        for hod in hotel_owner_details:
            hotel_detail.hotel_owners.append(
                HotelOwner(
                    first_name=hod.first_name,
                    last_name=hod.last_name,
                    email=hod.email_id,
                    is_primary_owner=True,
                    phone_number=hod.contact_number,
                )
            )
        get_hotel_detail_repository().create_or_update_hotel_details([hotel_detail])
        return [
            hotel_owner.email_id
            for hotel_owner in hotel_owner_details
            if hotel_owner.email_id
        ]
