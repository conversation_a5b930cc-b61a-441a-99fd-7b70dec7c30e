# pylint: disable=too-many-locals,no-member

import datetime
import logging

from core.common.constants import (
    InvoiceStatus,
    IssuedToTypes,
    PurchaseInvoiceReportStatus,
)
from core.common.utils.date import utc_to_ist
from core.notification import Slack, constants, notify
from reseller.constants import IngestionTypes
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.booking import get_booking_repository
from reseller.repository.customer_invoice import get_customer_invoice_repository
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.repository.hotel_customer_credit_note_mapping import (
    get_hotel_customer_credit_note_mapping_repository,
)
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)
from reseller.repository.invoice_mapping import get_invoice_mapping_repository
from reseller.repository.invoices_to_be_sent import get_invoices_to_be_sent_repository
from reseller.services.customer_invoice.get_customer_invoices import GetCustomerInvoices
from reseller.services.invoice_report_failures_audit import (
    mark_invoice_report_generation_success_on_retry,
    record_invoice_report_failure,
)

logger = logging.getLogger(__name__)


class CreateHotelInvoiceReport:
    def __init__(
        self,
        report_date=None,
        hotel_ids=None,
        customer_invoice_numbers=None,
        customer_credit_note_numbers=None,
        hotel_invoice_numbers=None,
        hotel_credit_note_numbers=None,
    ):
        self.report_date = report_date
        self.hotel_ids = hotel_ids
        self.customer_invoice_numbers = customer_invoice_numbers
        self.customer_credit_note_numbers = customer_credit_note_numbers
        self.hotel_invoice_numbers = hotel_invoice_numbers
        self.hotel_credit_note_numbers = hotel_credit_note_numbers

    def create_hotel_invoice_report(self):
        if self.report_date:
            report_date_str = (
                self.report_date.strftime("%Y-%m-%d")
                if not isinstance(self.report_date, str)
                else self.report_date
            )
        else:
            report_date_str = None
        is_selective_retry = False
        if report_date_str:
            created_or_updated_invoice_listing = fetch_invoice_listing(
                report_date_str, self.hotel_ids
            )
        else:
            reseller_ids = get_reseller_ids_from_crs_invoice_number(
                customer_invoice_numbers=self.customer_invoice_numbers,
                customer_credit_note_numbers=self.customer_credit_note_numbers,
                hotel_invoice_numbers=self.hotel_invoice_numbers,
                hotel_credit_note_numbers=self.hotel_credit_note_numbers,
            )
            created_or_updated_invoice_listing = (
                get_invoices_to_be_sent_repository().find_invoices_by_ids(
                    invoice_ids=reseller_ids
                )
            )
            is_selective_retry = True
        for inv_listing in created_or_updated_invoice_listing:
            try:
                if self.is_invoice_entity(inv_listing.entity_type):
                    invoice = get_hotel_invoice_repository().get(
                        invoice_id=inv_listing.invoice_id
                    )
                    invoice_or_credit_note_id = (
                        inv_listing.invoice_number
                        if hasattr(inv_listing, "invoice_number")
                        else invoice.invoice_number
                    )
                    logger.info(
                        f"Starting report generation for invoice {invoice_or_credit_note_id}"
                    )

                elif self.is_credit_note_entity(inv_listing.entity_type):
                    invoice = get_hotel_credit_note_repository().get(
                        credit_note_id=inv_listing.invoice_id
                    )
                    invoice_or_credit_note_id = (
                        inv_listing.invoice_number
                        if hasattr(inv_listing, "invoice_number")
                        else invoice.credit_note_number
                    )
                    logger.info(
                        f"Starting report generation for credit_note {invoice.credit_note_number}"
                    )

                else:
                    logger.exception(
                        f"Invalid invoice listing entity type {inv_listing.entity_type} for id : "
                        f"{inv_listing.invoice_id}"
                    )
                    continue

            except DocumentDoesNotExist as dde:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {inv_listing.invoice_id}: "
                    f"failed to fetch invoice from reseller database, probably due to partial ingestion"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Invoice listing not found for for id : {inv_listing.invoice_id}"
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=inv_listing.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                    error_trace=dde,
                )
                continue
            if invoice.status not in [InvoiceStatus.LOCKED, InvoiceStatus.CANCELLED]:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} booking_id "
                    f"{invoice.booking_id}, hotel: {invoice.hotel_id} : invalid invoice status : {invoice.status}"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Invoice listing id : {inv_listing.invoice_id} not in locked/cancelled state."
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=invoice.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    invoice_number=invoice_or_credit_note_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                )
                continue
            try:
                booking_for_invoice = get_booking_repository().get(
                    booking_id=invoice.booking_id,
                    hotel_id=invoice.hotel_id,
                    pms_type=invoice.pms_type,
                )
            except DocumentDoesNotExist as e:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} booking_id"
                    f" {invoice.booking_id}, hotel: {invoice.hotel_id} : {str(e)}"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Booking not found for Invoice listing id : {inv_listing.invoice_id}"
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=inv_listing.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    invoice_number=invoice_or_credit_note_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                    error_trace=e,
                )
                continue
            try:
                if self.is_invoice_entity(inv_listing.entity_type):
                    hotel_customer_invoice_mapping = (
                        get_invoice_mapping_repository().get(
                            hotel_invoice_id=invoice.invoice_id
                        )
                    )
                    base_report_data = self.get_hotel_invoice_base_report_data(
                        invoice,
                        booking_for_invoice,
                        hotel_customer_invoice_mapping,
                        report_date_str,
                    )
                elif self.is_credit_note_entity(inv_listing.entity_type):
                    hotel_customer_credit_note_mapping = (
                        get_hotel_customer_credit_note_mapping_repository().get(
                            hotel_credit_note_id=invoice.credit_note_id
                        )
                    )
                    base_report_data = self.get_hotel_credit_note_base_report_data(
                        invoice,
                        booking_for_invoice,
                        hotel_customer_credit_note_mapping,
                        report_date_str,
                    )
                else:
                    msg = (
                        f"Invalid invoice listing entity type {inv_listing.entity_type} for id : "
                        f"{inv_listing.invoice_id}"
                    )
                    logger.exception(msg)
                    record_invoice_report_failure(
                        process_name="create_invoice_report",
                        hotel_id=inv_listing.hotel_id,
                        invoice_id=inv_listing.invoice_id,
                        invoice_number=invoice_or_credit_note_id,
                        entity_type=inv_listing.entity_type,
                        error_message=msg,
                    )
                    continue
            except DocumentDoesNotExist as dde:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} "
                    f"booking_id {invoice.booking_id}, hotel: {invoice.hotel_id}"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} "
                    f"booking_id {invoice.booking_id}, hotel: {invoice.hotel_id}"
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=invoice.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    invoice_number=invoice_or_credit_note_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                    error_trace=dde,
                )
                continue
            except Exception as e:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} "
                    f"booking_id {invoice.booking_id}, hotel: {invoice.hotel_id}"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} "
                    f"booking_id {invoice.booking_id}, hotel: {invoice.hotel_id}"
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=invoice.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    invoice_number=invoice_or_credit_note_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                    error_trace=e,
                )
                continue

            try:
                hotel_invoice_reports = {}
                if not invoice.status == InvoiceStatus.CANCELLED:
                    hotel_invoice_reports = (
                        self.update_hotel_invoice_reports_with_line_items(
                            invoice,
                            booking_for_invoice,
                            hotel_invoice_reports,
                            inv_listing.entity_type,
                        )
                    )
                    logger.info(
                        f"Updated hotel invoice reports for invoice_id: {invoice_or_credit_note_id} "
                        f"booking_id {invoice.booking_id}, hotel: {invoice.hotel_id}"
                    )
                else:
                    unique_id = (
                        invoice.invoice_number
                        if self.is_invoice_entity(inv_listing.entity_type)
                        else invoice.credit_note_number
                    )
                    hotel_invoice_reports[
                        "dummy_line_item{0}".format(unique_id)
                    ] = dict(
                        reference_number=invoice.order_id,
                        room_booking_code="",
                        room_number="",
                        room_type="",
                        occupancy="",
                        guest_names="",
                        charge_type="",
                        pre_tax_price="",
                        total_amount="",
                        cgst_value="",
                        sgst_value="",
                        igst_value="",
                        flood_cess_value="",
                        cgst_percent="",
                        sgst_percent="",
                        igst_percent="",
                        flood_cess_percent="",
                        sac_code="",
                    )

                invoice_number = (
                    invoice.invoice_number
                    if inv_listing.entity_type == "hotel_invoice"
                    else (invoice.credit_note_number)
                )
                existing_invoice_reports = get_hotel_invoice_reports_repository().find(
                    invoice_number=invoice_number,
                    hotel_id=base_report_data["hotel_id"],
                    pms_type=base_report_data["pms_type"],
                )
                is_purchase_report_generated = any(
                    x
                    for x in existing_invoice_reports
                    if x.purchase_invoice_report_status
                    == PurchaseInvoiceReportStatus.GENERATED
                )
                if is_purchase_report_generated:
                    logger.info(
                        f"Skipping invoice report regeneration as the purchase report already generated: {invoice_number}"
                    )
                    continue
                logger.info(
                    f"Deleting hotel invoice reports for invoice_number: {invoice_number} "
                )
                get_hotel_invoice_reports_repository().delete(
                    invoice_number=invoice_number,
                    hotel_id=base_report_data["hotel_id"],
                    pms_type=base_report_data["pms_type"],
                )
                for report_key, report_value in hotel_invoice_reports.items():
                    report_value = hotel_invoice_reports[report_key]

                    report_value["pre_tax_price"] = str(report_value["pre_tax_price"])
                    report_value["total_amount"] = str(report_value["total_amount"])
                    report_value["cgst_value"] = str(report_value["cgst_value"])
                    report_value["sgst_value"] = str(report_value["sgst_value"])
                    report_value["igst_value"] = str(report_value["igst_value"])
                    report_value["unique_ref_id"] = report_key
                    report_value["created_on"] = (
                        utc_to_ist(datetime.datetime.now()).date().strftime("%Y-%m-%d")
                    )

                    report_data = {**base_report_data, **report_value}

                    query = {
                        "invoice_number": report_data["invoice_number"],
                        "room_number": report_data["room_number"],
                        "sac_code": report_data["sac_code"],
                        "cgst_percent": report_data["cgst_percent"],
                    }
                    logger.info(
                        f"Updating/Creating hotel invoice reports for invoice {report_data['invoice_number']}"
                    )
                    get_hotel_invoice_reports_repository().update(
                        query=query, dict_or_data_class=report_data, upsert=True
                    )
            except Exception as e:
                msg = (
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} booking_id"
                    f" {invoice.booking_id}, hotel: {invoice.hotel_id} : {str(e)}"
                )
                notify(Slack(constants.Slack.B2B_APP_ALERTS, msg))
                logger.exception(
                    f"Hotel invoice report generation failed:invoice_id: {invoice_or_credit_note_id} booking_id"
                    f" {invoice.booking_id}, hotel: {invoice.hotel_id} : {str(e)}"
                )
                record_invoice_report_failure(
                    process_name="create_invoice_report",
                    hotel_id=invoice.hotel_id,
                    invoice_id=inv_listing.invoice_id,
                    invoice_number=invoice_or_credit_note_id,
                    entity_type=inv_listing.entity_type,
                    error_message=msg,
                    error_trace=e,
                )
                continue
            if is_selective_retry:
                mark_invoice_report_generation_success_on_retry(
                    invoice_number=invoice_or_credit_note_id
                )

    @staticmethod
    def _get_tax_breakup(tax_breakups):
        tax_gst_map = dict()
        for tax_breakup in tax_breakups:
            tax_gst_map[tax_breakup.code] = dict(
                value=round(tax_breakup.amount, 2), percent=str(tax_breakup.percent)
            )
        return tax_gst_map

    @staticmethod
    def _get_stay_days(checkin_datetime, checkout_datetime):
        datetime_diff = checkout_datetime.date() - checkin_datetime.date()
        return datetime_diff.days if datetime_diff.days else 1

    @staticmethod
    def _get_booking_date(booking_date_str):
        booking_date = datetime.datetime.strptime(booking_date_str, "%Y-%m-%d %H:%M:%S")
        return booking_date.strftime("%Y-%m-%d")

    @staticmethod
    def _get_group_code(booking_id):
        if booking_id.startswith("G"):
            return booking_id
        return ""

    @staticmethod
    def _get_room_booking_code(booking, line_item):
        try:
            return [
                room.pms_id
                for room in booking.rooms
                if (
                    room.number == line_item.room.number
                    and room.type == line_item.room.room_type_name
                )
            ][0]
        except IndexError:
            return ""

    def get_hotel_invoice_base_report_data(
        self,
        hotel_invoice,
        booking_for_invoice,
        hotel_customer_invoice_mapping,
        report_date_str,
    ):
        billed_to_address_str = ", ".join(
            filter(
                None,
                [
                    hotel_invoice.gst_details.address.field1,
                    hotel_invoice.gst_details.address.field2,
                ],
            )
        )
        billed_by_address_str = ", ".join(
            filter(
                None,
                [
                    hotel_invoice.hotel.gst_details.address.field1,
                    hotel_invoice.hotel.gst_details.address.field2,
                ],
            )
        )

        check_in = hotel_invoice.check_in or booking_for_invoice.checkin
        check_out = hotel_invoice.check_out or booking_for_invoice.checkout

        base_report_data = dict(
            cs_hotel_id=hotel_invoice.hotel_id,
            hotel_id=getattr(hotel_invoice.hotel, "pms_id", "")
            or hotel_invoice.hotel_id,
            hotel_trade_name=hotel_invoice.hotel.name,
            hotel_nav_code=getattr(hotel_invoice.hotel, "navision_code", ""),
            billed_by_legal_name=hotel_invoice.hotel.gst_details.legal_name,
            billed_by_gstin=hotel_invoice.hotel.gst_details.gstin_number,
            billed_by_address=billed_by_address_str,
            billed_by_city=hotel_invoice.hotel.gst_details.address.city,
            billed_by_state=hotel_invoice.hotel.gst_details.address.state,
            billed_by_pin_code=hotel_invoice.hotel.gst_details.address.pincode,
            billed_to_legal_name=hotel_invoice.gst_details.legal_name,
            billed_to_gstin_number=hotel_invoice.gst_details.gstin_number,
            billed_to_address=billed_to_address_str,
            billed_to_city=hotel_invoice.gst_details.address.city,
            billed_to_state=hotel_invoice.gst_details.address.state,
            billed_to_pin_code=hotel_invoice.gst_details.address.pincode,
            booking_date=self._get_booking_date(
                booking_for_invoice.source_created_date
            ),
            group_code=self._get_group_code(hotel_invoice.booking_id),
            booking_id=booking_for_invoice.booking_id,
            checkin=utc_to_ist(check_in).date().strftime("%Y-%m-%d"),
            checkout=utc_to_ist(check_out).date().strftime("%Y-%m-%d"),
            days=self._get_stay_days(check_in, check_out),
            invoice_number=hotel_invoice.invoice_number,
            invoice_date=hotel_invoice.invoice_date,
            customer_invoice_number=hotel_customer_invoice_mapping.customer_invoice_number,
            booking_owner_name=booking_for_invoice.owner,
            source=getattr(booking_for_invoice, "source", ""),
            sub_source=getattr(booking_for_invoice, "sub_source", ""),
            pms_type=hotel_invoice.pms_type,
            report_date=report_date_str,
            status=hotel_invoice.status,
            issued_to_type=hotel_invoice.issued_to_type,
            total_invoice_amount=str(
                sum(
                    [
                        line_item.charge.pre_tax + line_item.charge.tax
                        for line_item in hotel_invoice.line_items
                    ]
                )
            ),
            original_invoice_number=None,
            entity_type="invoice",
            purchase_invoice_report_status=self._get_purchase_invoice_report_status(
                hotel_invoice.issued_to_type
            ),
        )
        return base_report_data

    @staticmethod
    def _get_purchase_invoice_report_status(issued_to_type):
        if issued_to_type == IssuedToTypes.RESELLER:
            return PurchaseInvoiceReportStatus.PENDING
        return PurchaseInvoiceReportStatus.NOT_APPLICABLE

    def get_hotel_credit_note_base_report_data(
        self,
        hotel_credit_note,
        booking_for_invoice,
        hotel_customer_credit_note_mapping,
        report_date_str,
    ):

        hotel_invoice = self._get_associated_hotel_invoice(hotel_credit_note)

        billed_to_address_str = ", ".join(
            filter(
                None,
                [
                    hotel_credit_note.gst_details.address.field1,
                    hotel_credit_note.gst_details.address.field2,
                ],
            )
        )
        billed_by_address_str = ", ".join(
            filter(
                None,
                [
                    hotel_credit_note.hotel.gst_details.address.field1,
                    hotel_credit_note.hotel.gst_details.address.field2,
                ],
            )
        )

        check_in = hotel_invoice.check_in or booking_for_invoice.checkin
        check_out = hotel_invoice.check_out or booking_for_invoice.checkout

        base_report_data = dict(
            cs_hotel_id=hotel_credit_note.hotel_id,
            hotel_id=getattr(hotel_credit_note.hotel, "pms_id", "")
            or hotel_credit_note.hotel_id,
            hotel_trade_name=hotel_credit_note.hotel.name,
            hotel_nav_code=getattr(hotel_credit_note.hotel, "navision_code", ""),
            billed_by_legal_name=hotel_credit_note.hotel.gst_details.legal_name,
            billed_by_gstin=hotel_credit_note.hotel.gst_details.gstin_number,
            billed_by_address=billed_by_address_str,
            billed_by_city=hotel_credit_note.hotel.gst_details.address.city,
            billed_by_state=hotel_credit_note.hotel.gst_details.address.state,
            billed_by_pin_code=hotel_credit_note.hotel.gst_details.address.pincode,
            billed_to_legal_name=hotel_credit_note.gst_details.legal_name,
            billed_to_gstin_number=hotel_credit_note.gst_details.gstin_number,
            billed_to_address=billed_to_address_str,
            billed_to_city=hotel_credit_note.gst_details.address.city,
            billed_to_state=hotel_credit_note.gst_details.address.state,
            billed_to_pin_code=hotel_credit_note.gst_details.address.pincode,
            booking_date=self._get_booking_date(
                booking_for_invoice.source_created_date
            ),
            group_code=self._get_group_code(hotel_credit_note.booking_id),
            booking_id=booking_for_invoice.booking_id,
            checkin=utc_to_ist(check_in).date().strftime("%Y-%m-%d"),
            checkout=utc_to_ist(check_out).date().strftime("%Y-%m-%d"),
            days=self._get_stay_days(check_in, check_out),
            invoice_number=hotel_credit_note.credit_note_number,
            invoice_date=self._derive_credit_note_date(hotel_credit_note),
            customer_invoice_number=hotel_customer_credit_note_mapping.customer_credit_note_number,
            booking_owner_name=booking_for_invoice.owner,
            source=getattr(booking_for_invoice, "source", ""),
            sub_source=getattr(booking_for_invoice, "sub_source", ""),
            pms_type=hotel_credit_note.pms_type,
            report_date=report_date_str,
            status=hotel_credit_note.status,
            issued_to_type=getattr(hotel_invoice, "issued_to_type", ""),
            total_invoice_amount=str(
                sum(
                    [
                        line_item.charge.pre_tax + line_item.charge.tax
                        for line_item in hotel_credit_note.line_items
                    ]
                )
            ),
            original_invoice_number=hotel_invoice.invoice_number,
            entity_type="credit_note",
            purchase_invoice_report_status=self._get_purchase_invoice_report_status(
                getattr(hotel_invoice, "issued_to_type", "")
            ),
        )
        return base_report_data

    @staticmethod
    def _get_associated_hotel_invoice(hotel_credit_note):
        hotel_invoice = None
        if hotel_credit_note.reference_invoice_numbers:
            hotel_customer_invoice_mapping = get_invoice_mapping_repository().find(
                customer_invoice_number=hotel_credit_note.reference_invoice_numbers[0]
            )
            if len(hotel_customer_invoice_mapping) > 1:
                query = dict(
                    booking_id=hotel_customer_invoice_mapping[0].booking_id,
                    hotel_id=hotel_customer_invoice_mapping[0].hotel_id,
                    pms_type=hotel_customer_invoice_mapping[0].pms_type,
                    invoice_number=hotel_customer_invoice_mapping[
                        0
                    ].customer_invoice_number,
                )
                customer_invoices_for_invoice_number = (
                    get_customer_invoice_repository().find(**query)
                )
                active_invoice_id = [
                    ci.invoice_id
                    for ci in customer_invoices_for_invoice_number
                    if ci.status != InvoiceStatus.CANCELLED
                ]
                if active_invoice_id:
                    for invoice_mapping in hotel_customer_invoice_mapping:
                        if invoice_mapping.customer_invoice_id == active_invoice_id[0]:
                            hotel_customer_invoice_mapping = invoice_mapping
                            break
            elif len(hotel_customer_invoice_mapping) == 1:
                hotel_customer_invoice_mapping = hotel_customer_invoice_mapping[0]
            hotel_invoices = get_hotel_invoice_repository().find(
                invoice_number=hotel_customer_invoice_mapping.hotel_invoice_number
            )
            if len(hotel_invoices) == 1:
                hotel_invoice = hotel_invoices[0]
            elif len(hotel_invoices) > 0:
                active_hotel_invoices = [
                    hi for hi in hotel_invoices if hi.status != InvoiceStatus.CANCELLED
                ]
                hotel_invoice = (
                    active_hotel_invoices[0]
                    if len(active_hotel_invoices) > 0
                    else hotel_invoices[0]
                )
            else:
                raise Exception(
                    f"Unable to find associated invoice for CN {hotel_credit_note.credit_note_number}"
                )
        return hotel_invoice

    def update_hotel_invoice_reports_with_line_items(
        self, hotel_invoice, booking_for_invoice, hotel_invoice_reports, entity_type
    ):
        for line_item in hotel_invoice.line_items:
            tax_gst_map = self._get_tax_breakup(line_item.charge.tax_breakup)
            room_number = str(line_item.room.number)
            hsn_code = str(line_item.charge.hsn_code)
            cgst_percent = str(tax_gst_map.get("CGST", {}).get("percent"))
            report_key = room_number + hsn_code + cgst_percent
            if self.is_invoice_entity(entity_type):
                report_key += hotel_invoice.invoice_number
            else:
                report_key += hotel_invoice.credit_note_number

            line_item_data = dict(
                reference_number=hotel_invoice.order_id,
                room_booking_code=self._get_room_booking_code(
                    booking_for_invoice, line_item
                ),
                room_number=room_number,
                room_type=line_item.room.room_type_name,
                occupancy=len(line_item.customers),
                guest_names=", ".join(
                    [customer.name for customer in line_item.customers if customer.name]
                ),
                charge_type=line_item.charge.name,
                pre_tax_price=round(line_item.charge.pre_tax, 2),
                total_amount=round(
                    (line_item.charge.pre_tax + line_item.charge.tax), 2
                ),
                cgst_value=tax_gst_map.get("CGST", {}).get("value", 0),
                sgst_value=tax_gst_map.get("SGST", {}).get("value", 0),
                igst_value=tax_gst_map.get("IGST", {}).get("value", 0),
                flood_cess_value=tax_gst_map.get("KERALA_FLOOD_CESS", {}).get(
                    "value", 0
                ),
                cgst_percent=tax_gst_map.get("CGST", {}).get("percent"),
                sgst_percent=tax_gst_map.get("SGST", {}).get("percent"),
                igst_percent=tax_gst_map.get("IGST", {}).get("percent"),
                flood_cess_percent=tax_gst_map.get("KERALA_FLOOD_CESS", {}).get(
                    "percent", 0
                ),
                sac_code=hsn_code,
            )

            if report_key not in hotel_invoice_reports:
                hotel_invoice_reports[report_key] = line_item_data
            else:
                report_data = hotel_invoice_reports[report_key]
                report_data["pre_tax_price"] += round(line_item.charge.pre_tax, 2)
                report_data["total_amount"] += round(
                    (line_item.charge.pre_tax + line_item.charge.tax), 2
                )
                report_data["cgst_value"] += tax_gst_map.get("CGST", {}).get("value", 0)
                report_data["sgst_value"] += tax_gst_map.get("SGST", {}).get("value", 0)
                report_data["igst_value"] += tax_gst_map.get("IGST", {}).get("value", 0)
                report_data["flood_cess_value"] += tax_gst_map.get(
                    "KERALA_FLOOD_CESS", {}
                ).get("value", 0)
                hotel_invoice_reports[report_key] = report_data
        return hotel_invoice_reports

    @staticmethod
    def is_credit_note_entity(entity_type):
        return entity_type == IngestionTypes.CREDIT_NOTE

    @staticmethod
    def is_invoice_entity(entity_type):
        return entity_type == IngestionTypes.HOTEL_INVOICE

    @staticmethod
    def _derive_credit_note_date(hotel_credit_note):
        if hotel_credit_note.credit_note_date:
            if type(hotel_credit_note.credit_note_date).__name__ == "str":
                return hotel_credit_note.credit_note_date
            else:
                return hotel_credit_note.credit_note_date.date().strftime("%Y-%m-%d")
        else:
            return utc_to_ist(hotel_credit_note.created_at).date().strftime("%Y-%m-%d")


def fetch_invoice_listing(report_date_str, hotel_ids):
    created_or_updated_invoice_listing = []
    for hotel_id in hotel_ids:
        created_or_updated_invoice_listing.extend(
            get_invoices_to_be_sent_repository().find(
                dispatch_date=report_date_str, hotel_id=hotel_id
            )
        )
    if not hotel_ids:
        created_or_updated_invoice_listing.extend(
            get_invoices_to_be_sent_repository().find(dispatch_date=report_date_str)
        )

    return created_or_updated_invoice_listing


def get_reseller_ids_from_crs_invoice_number(
    customer_invoice_numbers=None,
    customer_credit_note_numbers=None,
    hotel_invoice_numbers=None,
    hotel_credit_note_numbers=None,
):
    reseller_ids = []
    if customer_credit_note_numbers:
        credit_note_mappings = get_hotel_customer_credit_note_mapping_repository().get_by_customer_credit_note_numbers(
            customer_credit_note_numbers=customer_credit_note_numbers
        )
        reseller_ids.extend(
            [
                credit_note_mapping.hotel_credit_note_id
                for credit_note_mapping in credit_note_mappings
            ]
        )
    if customer_invoice_numbers:
        invoice_mappings = (
            get_invoice_mapping_repository().get_by_customer_invoice_numbers(
                customer_invoice_numbers=customer_invoice_numbers
            )
        )
        reseller_ids.extend(
            [invoice_mapping.hotel_invoice_id for invoice_mapping in invoice_mappings]
        )
    if hotel_credit_note_numbers:
        credit_note_mappings = get_hotel_customer_credit_note_mapping_repository().get_by_hotel_credit_note_numbers(
            hotel_credit_note_numbers=hotel_credit_note_numbers
        )
        reseller_ids.extend(
            [
                credit_note_mapping.hotel_credit_note_id
                for credit_note_mapping in credit_note_mappings
            ]
        )
    if hotel_invoice_numbers:
        invoice_mappings = (
            get_invoice_mapping_repository().get_by_hotel_invoice_numbers(
                hotel_invoice_numbers=hotel_invoice_numbers
            )
        )
        reseller_ids.extend(
            [invoice_mapping.hotel_invoice_id for invoice_mapping in invoice_mappings]
        )
    return reseller_ids
