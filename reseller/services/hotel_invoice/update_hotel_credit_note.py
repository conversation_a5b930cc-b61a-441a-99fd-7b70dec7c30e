# pylint: disable=inconsistent-return-statements

import logging

from core.common.constants import CreditNoteStatus
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.services.diff_and_patch_credit_note import (
    diff_hotel_credit_note,
    patch_hotel_credit_note,
)
from reseller.constants import IngestionTypes
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.invoices_to_be_sent import save_hotel_credit_note_to_be_sent

logger = logging.getLogger(__name__)


class UpdateHotelCreditNote:
    def __init__(self, old_hotel_credit_note=None, **kwargs):
        self.old_hotel_credit_note = old_hotel_credit_note
        self.kwargs = kwargs

    def update_hotel_credit_note(self, new_hotel_credit_note) -> HotelCreditNote:
        updated_hotel_credit_note = self._update_hotel_credit_note(
            new_hotel_credit_note
        )
        if updated_hotel_credit_note:
            get_hotel_credit_note_repository().update(
                {"credit_note_id": self.old_hotel_credit_note.credit_note_id},
                updated_hotel_credit_note,
            )
        return new_hotel_credit_note

    def update_hotel_credit_notes(self, new_hotel_credit_note):
        updated_hotel_credit_note = self._update_hotel_credit_note(
            new_hotel_credit_note
        )
        return updated_hotel_credit_note

    def _update_hotel_credit_note(self, new_hotel_credit_note):
        old_hotel_credit_note = GetHotelCreditNote(
            credit_note_id=self.old_hotel_credit_note.credit_note_id
        ).from_db()
        if diff_hotel_credit_note(
            old_credit_note=old_hotel_credit_note, new_credit_note=new_hotel_credit_note
        ):
            if new_hotel_credit_note.status in [
                CreditNoteStatus.CANCELLED,
                CreditNoteStatus.LOCKED,
            ]:
                save_hotel_credit_note_to_be_sent(
                    new_hotel_credit_note, entity_type=IngestionTypes.CREDIT_NOTE
                )
            updated_document = patch_hotel_credit_note(
                old_credit_note=old_hotel_credit_note,
                new_credit_note=new_hotel_credit_note,
            )
            return updated_document


class UpdateHotelCreditNotes:
    def __init__(self, hotel_credit_notes):
        self.hotel_credit_notes = hotel_credit_notes

    def create_or_update_hotel_credit_notes(self):
        queries = []
        for hotel_credit_note in self.hotel_credit_notes:
            query = {"credit_note_id": hotel_credit_note.credit_note_id}
            queries.append(query)
        get_hotel_credit_note_repository().bulk_update(
            queries=queries, data_list=self.hotel_credit_notes
        )
