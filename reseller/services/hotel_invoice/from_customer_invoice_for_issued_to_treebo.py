from datetime import datetime

from core.booking.data_classes.booking import Booking
from core.common.constants import (
    FLOOD_CESS_TAX,
    HsnCodes,
    InvoiceStatus,
    IssuedByTypes,
    IssuedToTypes,
)
from core.common.data_classes.customer import Customer
from core.common.data_classes.tax_breakup import TaxBreakup
from core.common.integrations.tax.treebo_tax_serive import TreeboTax
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice
from core.hotel.data_classes.room import Room
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.constants import DEFAULT_TAX_CODES
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.data_classes.line_item import LineItem
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from core.hotel_invoice.services.tax_markup import TaxMarkup
from integrations.einvoice.cleartax.einvoice_dto import IrpDetailsDTO
from integrations.einvoice.cleartax.einvoicing_service import EInvoicingService
from integrations.tax.treebo_tax_service import TreeboTaxService as Treebo
from object_registry import locate_instance
from reseller import app
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.treebo_gst import TreeboGetGSTDetails


class GetHotelInvoiceFromCustomerInvoiceForIssuedToReseller:
    def __init__(
        self,
        hotel_invoice_id,
        customer_invoice: CustomerInvoice,
        booking: Booking,
        **kwargs,
    ):
        self.hotel_invoice = GetHotelInvoice(hotel_invoice_id).from_db()
        self.customer_invoice = customer_invoice
        self.booking = booking
        self.hotel_invoice_id = hotel_invoice_id
        self.kwargs = kwargs

    def get_invoice(self) -> HotelInvoice:
        hotel_invoice = self.get_data_class()
        if not hotel_invoice.is_einvoice and hotel_invoice.is_locked_in_crs:
            self.generate_irn(hotel_invoice)
        return hotel_invoice

    def get_data_class(self):
        hotel = GetHotel(self.booking.hotel_id).get_hotel()
        hotel_invoice = HotelInvoice(
            invoice_id=self.hotel_invoice.invoice_id,
            booking_id=self.hotel_invoice.booking_id,
            hotel_id=self.hotel_invoice.hotel_id,
            pms_type=self.hotel_invoice.pms_type,
            invoice_number=self.hotel_invoice.invoice_number,
            issued_to_type=self._issued_to_type(),
            hotel=convert_hotel_to_invoice_hotel(hotel, self.hotel_invoice.pms_type),
            gst_details=TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details(),
            line_items=self._line_items(hotel),
            status=self._status(),
            source=self.hotel_invoice.source,
            order_id=self.booking.order_id,
            check_in=self.customer_invoice.check_in or self.booking.checkin,
            check_out=self.customer_invoice.check_out or self.booking.checkout,
            bill_id=self.customer_invoice.bill_id,
            invoice_date=self.customer_invoice.invoice_date,
            is_locked_in_crs=self.customer_invoice.is_locked_in_crs,
        )
        hotel_invoice.created_at = self.hotel_invoice.created_at
        return hotel_invoice

    def _issued_to_type(self):
        if self.customer_invoice.issued_by_type == IssuedByTypes.RESELLER:
            return IssuedToTypes.RESELLER
        else:
            return IssuedToTypes.CUSTOMER

    def _status(self):
        if self.customer_invoice.status == InvoiceStatus.CANCELLED:
            status = InvoiceStatus.CANCELLED
        else:
            status = InvoiceStatus.LOCKED
        return status

    def _line_items(self, hotel):
        if self.customer_invoice.gst_details.is_sez:
            line_items_json = TreeboTax().create_hotel_invoice_line_item_json(
                self.customer_invoice
            )
            updated_line_item_with_new_charge = []
            if line_items_json:
                tax_response = Treebo().get_line_item_with_new_charge(
                    line_items_json,
                    self.customer_invoice.hotel_id,
                    hotel.gst_details.gstin_number,
                )
                line_item_with_new_charge_json = (
                    TreeboTax().map_response_data_with_line_item_json(
                        line_items_json, tax_response
                    )
                )
                updated_line_item_with_new_charge = (
                    TreeboTax().create_line_items_object(line_item_with_new_charge_json)
                )
            for line_item in updated_line_item_with_new_charge:
                # blocking booking commission charges to go in hotel invoice
                if line_item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                    continue
                raw_charge = self._charge(line_item.charge)
                charge = self.sanitize_charge(raw_charge)
                line_item.charge = charge
            return updated_line_item_with_new_charge
        else:
            line_items = []
            for item in self.customer_invoice.line_items:
                # blocking booking commission charges to go in hotel invoice
                if item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                    continue
                raw_charge = self._charge(item.charge)
                charge = self.sanitize_charge(raw_charge)
                room = item.room if item.room else self._room(item.room_id)
                customers = (
                    item.customers if item.customers else self._customers(item.room_id)
                )
                line_item = LineItem(
                    charge,
                    room,
                    customers,
                )
                line_items.append(line_item)
            return line_items

    def _charge(self, charge_item):
        if not charge_item.tax_breakup:
            charge_item.tax_breakup = frozenset(
                [TaxBreakup(code, 0, percent=0) for code in DEFAULT_TAX_CODES]
            )
        charge = (
            TaxMarkup(charge_item).maybe_markup()
            if datetime.date(self.hotel_invoice.created_at)
            < datetime.strptime(
                app.config["INPUT_TAX_CREDIT_APPLICABILITY_DATE"], "%Y-%m-%d"
            ).date()
            else charge_item
        )
        charge.sanitize_tax_codes()
        return charge

    def _room(self, room_id):
        try:
            room = self.get_room_booking_for_item(room_id)
            number = room.number
            room_type_name = room.type
        except ValueError:
            number = ""
            room_type_name = ""

        return Room(number=number, room_type_name=room_type_name)

    def _customers(self, room_id):
        customers = []

        try:
            room = self.get_room_booking_for_item(room_id)
            customers_in_associated_room = room.guests
        except ValueError:
            customers_in_associated_room = []

        for cus in customers_in_associated_room:
            customer = Customer(
                uid=cus.stay_id,
                name=(cus.first_name, cus.last_name),
                email=getattr(cus, "email", ""),
                phone_number=getattr(cus, "phone_number", ""),
            )
            customers.append(customer)

        return customers

    def get_room_booking_for_item(self, room_id):
        try:
            return [
                room for room in self.booking.rooms if room.room_booking_id == room_id
            ][0]
        except IndexError:
            raise ValueError(
                f"Invalid room booking id {room_id} for booking {self.booking.booking_id}"
            )

    @classmethod
    def from_invoice_number(cls, invoice_number):
        raise NotImplementedError("Not implemented for GetBuyInvoice from SellInvoice")

    @staticmethod
    def sanitize_charge(raw_charge):
        raw_charge_tax_breakup = list(raw_charge.tax_breakup)
        for tax in raw_charge_tax_breakup:
            if tax.code.lower() == FLOOD_CESS_TAX.lower():
                raw_charge.tax -= tax.amount
        sanitized_tax_breakup = [
            tax
            for tax in raw_charge_tax_breakup
            if tax.code.lower() != FLOOD_CESS_TAX.lower()
        ]
        raw_charge.tax_breakup = frozenset(sanitized_tax_breakup)
        return raw_charge

    @staticmethod
    def generate_irn(invoice: HotelInvoice):
        e_invoice_service: EInvoicingService = locate_instance(EInvoicingService)
        if e_invoice_service.is_einvoicing_applicable(invoice).should_submit_to_irp():
            irp_details: IrpDetailsDTO = e_invoice_service.generate_irn(invoice)
            invoice.set_irp_details(irp_details)
