import logging

from core.booking.data_classes.booking import Booking
from core.common.constants import (
    CreditNoteStatus,
    HsnCodes,
    IssuedByTypes,
    IssuedToTypes,
)
from core.common.data_classes.customer import Customer
from core.common.data_classes.tax_breakup import TaxBreakup
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.hotel.data_classes.room import Room
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.constants import DEFAULT_TAX_CODES
from core.hotel_invoice.data_classes.credit_note_line_item import LineItem
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote

logger = logging.getLogger(__name__)


class GetHotelCreditNoteFromCustomerCreditNoteForIssuedToCustomer:
    def __init__(
        self,
        invoice_id,
        customer_credit_note: CustomerCreditNote,
        booking: Booking,
        **kwargs,
    ):
        self.hotel_credit_note = GetHotelCreditNote(invoice_id).from_db()
        self.customer_credit_note = customer_credit_note
        self.booking = booking
        self.invoice_id = invoice_id
        self.kwargs = kwargs

    def get_hotel_credit_note(self) -> HotelCreditNote:
        hotel_credit_note = self.get_data_class()
        return hotel_credit_note

    def get_data_class(self):
        hotel = GetHotel(self.booking.hotel_id).get_hotel()
        hotel_credit_note = HotelCreditNote(
            credit_note_id=self.hotel_credit_note.credit_note_id,
            booking_id=self.hotel_credit_note.booking_id,
            hotel_id=self.hotel_credit_note.hotel_id,
            pms_type=self.hotel_credit_note.pms_type,
            credit_note_number=self.hotel_credit_note.credit_note_number,
            issued_to_type=self._issued_to_type(),
            hotel=convert_hotel_to_invoice_hotel(
                hotel, self.hotel_credit_note.pms_type
            ),
            gst_details=self.customer_credit_note.gst_details,
            line_items=self._line_items(),
            status=self._status(),
            source=self.hotel_credit_note.source,
            order_id=self.booking.order_id,
            reference_invoice_numbers=self.customer_credit_note.reference_invoice_numbers,
            bill_id=self.customer_credit_note.bill_id,
            credit_note_date=self.customer_credit_note.credit_note_date,
            irn=self.customer_credit_note.irn,
            qr_code=self.customer_credit_note.qr_code,
            is_einvoice=self.customer_credit_note.is_einvoice,
        )
        hotel_credit_note.created_at = self.hotel_credit_note.created_at
        return hotel_credit_note

    def _issued_to_type(self):
        if self.customer_credit_note.issued_by_type == IssuedByTypes.RESELLER:
            return IssuedToTypes.RESELLER
        else:
            return IssuedToTypes.CUSTOMER

    def _status(self):
        if self.customer_credit_note.status == CreditNoteStatus.CANCELLED:
            status = CreditNoteStatus.CANCELLED
        else:
            status = CreditNoteStatus.LOCKED
        return status

    def _line_items(self):
        line_items = []
        for item in self.customer_credit_note.line_items:
            # blocking booking commission charges to go in hotel invoice
            if item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                continue
            raw_charge = self._charge(item.charge)
            charge = raw_charge
            room = self._room(item.room_id)
            customers = self._customers(item.room_id)
            line_item = LineItem(
                charge,
                room,
                customers,
            )
            line_items.append(line_item)
        return line_items

    def _charge(self, charge_item):
        if not charge_item.tax_breakup:
            charge_item.tax_breakup = frozenset(
                [TaxBreakup(code, 0, percent=0) for code in DEFAULT_TAX_CODES]
            )
        charge_item.sanitize_tax_codes()
        return charge_item

    def _room(self, room_id):
        try:
            room = self.get_room_booking_for_item(room_id)
            number = room.number
            room_type_name = room.type
        except ValueError:
            number = ""
            room_type_name = ""

        return Room(number=number, room_type_name=room_type_name)

    def _customers(self, room_id):
        customers = []

        try:
            room = self.get_room_booking_for_item(room_id)
            customers_in_associated_room = room.guests
        except ValueError:
            customers_in_associated_room = []

        for cus in customers_in_associated_room:
            customer = Customer(
                uid=cus.stay_id,
                name=(cus.first_name, cus.last_name),
                email=getattr(cus, "email", ""),
                phone_number=getattr(cus, "phone_number", ""),
            )
            customers.append(customer)

        return customers

    def get_room_booking_for_item(self, room_id):
        try:
            return [
                room for room in self.booking.rooms if room.room_booking_id == room_id
            ][0]
        except IndexError:
            raise ValueError(
                f"Invalid room booking id {room_id} for booking {self.booking.booking_id}"
            )

    @classmethod
    def from_invoice_number(cls, invoice_number):
        raise NotImplementedError("Not implemented for GetBuyInvoice from SellInvoice")
