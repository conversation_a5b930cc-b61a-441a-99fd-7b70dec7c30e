from core.common.constants import CreditNoteStatus
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.integrations.crs.hotel_credit_note import (
    HotelCreditNote as CRSHotelCreditNote,
)
from reseller.repository.base_repository import DocumentNotFoundException
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.services.treebo_gst import TreeboGetGSTDetails


class GetHotelCreditNote:
    def __init__(self, credit_note_id, **kwargs):
        self.credit_note_id = credit_note_id
        self.kwargs = kwargs

    @classmethod
    def from_credit_note_number(cls, credit_note_number, hotel_id) -> HotelCreditNote:
        query = {
            "credit_note_number": credit_note_number,
            "hotel_id": hotel_id,
            "status": {"$ne": CreditNoteStatus.CANCELLED},
        }
        hotel_credit_note = get_hotel_credit_note_repository().get(**query)
        return hotel_credit_note

    def from_db(self) -> HotelCreditNote:
        hotel_credit_note = get_hotel_credit_note_repository().get(
            credit_note_id=self.credit_note_id
        )
        return hotel_credit_note

    def get_pms_credit_note_url(self, hotel_credit_note):
        url = CRSHotelCreditNote.get_hotel_credit_note_url(
            hotel_credit_note.credit_note_id
        )
        return url

    def get_pms_credit_note_signed_url(self, hotel_credit_note):
        signed_url = CRSHotelCreditNote.get_hotel_credit_note_signed_url(
            hotel_credit_note.credit_note_id
        )
        return signed_url

    @classmethod
    def from_pms(cls, customer_credit_note_id, hotel, booking_id):
        hotel_credit_note = CRSHotelCreditNote.get_from_customer_credit_note(
            customer_credit_note_id=customer_credit_note_id,
            booking_id=booking_id,
            invoice_hotel=hotel,
            gst_details=TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details(),
        )
        return hotel_credit_note

    @classmethod
    def get_hotel_credit_note(cls, credit_note_number, hotel_id) -> HotelCreditNote:
        query = {
            "credit_note_number": credit_note_number,
            "hotel_id": hotel_id,
            "status": {"$ne": CreditNoteStatus.CANCELLED},
        }
        try:
            hotel_credit_note = (
                get_hotel_credit_note_repository().get_hotel_credit_note(**query)
            )
        except Exception as e:
            raise DocumentNotFoundException(e)
        return hotel_credit_note
