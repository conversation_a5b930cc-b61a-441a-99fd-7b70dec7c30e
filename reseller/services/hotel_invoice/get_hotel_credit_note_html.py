from collections import defaultdict

from flask import render_template
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils.dateutils import current_datetime, local_timezone

from core.common.pdf_generator import PdfGenerator
from core.hotel_invoice.data_classes.totals import InvoiceTotals
from reseller import app
from reseller.constants import PIS_TENANT_ID
from reseller.repository.booking import get_booking_repository
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.repository.invoice_mapping import get_invoice_mapping_repository
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote


def as_html(credit_note_number, hotel_id):
    hotel_credit_note = GetHotelCreditNote.from_credit_note_number(
        credit_note_number, hotel_id
    )
    html_context = {
        "hotel_credit_note": hotel_credit_note,
        "line_items": _group_line_items_by_customer_and_sort_charges_by_date(
            hotel_credit_note.line_items
        ),
        "totals": InvoiceTotals(hotel_credit_note),
        "hotel_invoice_reference": _get_hotel_invoice_reference_details(
            hotel_credit_note
        )[0],
        "printed_on": current_datetime(local_timezone()),
        "booking_owner": _get_booking_owner(hotel_credit_note.booking_id),
    }
    html_context.update(
        {
            "tax_code_to_charge_details": group_charges_by_tax_codes(
                html_context["line_items"]
            )
        }
    )
    with app.app_context(), app.test_request_context():
        if get_current_tenant_id() == PIS_TENANT_ID:
            return render_template("tntpis/credit_note.html", **html_context)

        return render_template("hotel_credit_note/invoice.html", **html_context)


def as_pdf(credit_note_number, hotel_id):
    html = as_html(credit_note_number, hotel_id)
    pdf_generator = PdfGenerator(html=html)
    pdf_generator.generate_pdf()
    return pdf_generator.output_file


# pylint: disable=invalid-name
def _group_line_items_by_customer_and_sort_charges_by_date(line_items):
    # Groups charges by customer and returns a tuple of the structure
    # (customer, room, charges_grouped_by_customer_and_room)
    charges_grouped_by_customer_and_room = defaultdict(list)
    for line_item in line_items:
        key = (line_item.customers, line_item.room)
        charges_grouped_by_customer_and_room[key].append(line_item.charge)

    customer_room_and_charges = [
        (customer, room, sorted(charges, key=lambda charge: charge.applicable_date))
        for (customer, room), charges in charges_grouped_by_customer_and_room.items()
    ]
    return customer_room_and_charges


def _get_hotel_invoice_reference_details(hotel_credit_note):
    hotel_invoices = []
    for reference_number in hotel_credit_note.reference_invoice_numbers:
        invoice_mapping = get_invoice_mapping_repository().get(
            customer_invoice_number=reference_number
        )
        hotel_invoice = get_hotel_invoice_repository().get(
            invoice_number=invoice_mapping.hotel_invoice_number
        )
        hotel_invoices.append(hotel_invoice)
    return hotel_invoices


def _get_booking_owner(booking_id):
    booking_details = get_booking_repository().get(booking_id=booking_id)
    return booking_details.owner


def group_charges_by_tax_codes(line_items):
    tax_code_to_charge_details = {}

    for _, _, charges in line_items:
        for charge in charges:
            tax_code = charge.hsn_code
            charge_details = tax_code_to_charge_details.get(tax_code, {})
            for tax_item in charge.tax_breakup:
                charge_details[tax_item.code] = tax_item.amount + charge_details.get(
                    tax_item.code, 0
                )
            charge_details["sales_value"] = (
                charge_details.get("sales_value", 0) + charge.pre_tax
            )
            tax_code_to_charge_details[tax_code] = charge_details

    return tax_code_to_charge_details
