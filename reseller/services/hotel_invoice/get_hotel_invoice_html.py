import logging
from collections import defaultdict

from flask import render_template
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils.dateutils import current_datetime, local_timezone

from core.common.constants import InvoiceStatus
from core.common.pdf_generator import PdfGenerator
from core.common.utils.timeit import timeit
from core.hotel_invoice.data_classes.totals import InvoiceTotals
from reseller import app
from reseller.constants import PIS_TENANT_ID
from reseller.repository.booking import get_booking_repository
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice

logger = logging.getLogger(__name__)


def as_html(invoice_number):
    hotel_invoice = GetHotelInvoice.from_invoice_number(invoice_number)
    html_context = {
        "invoice": hotel_invoice,
        "line_items": _group_line_items_by_customer_and_sort_charges_by_date(
            hotel_invoice.line_items, hotel_invoice.status
        ),
        "totals": InvoiceTotals(hotel_invoice),
        "printed_on": current_datetime(local_timezone()),
        "booking_owner": _get_booking_owner(hotel_invoice.booking_id),
    }
    html_context.update(
        {
            "tax_code_to_charge_details": group_charges_by_tax_codes(
                html_context["line_items"]
            ),
        }
    )

    with app.app_context(), app.test_request_context():
        if get_current_tenant_id() == PIS_TENANT_ID:
            return render_template("tntpis/hotel_invoice.html", **html_context)

        if hotel_invoice.status == InvoiceStatus.CANCELLED:
            return render_template(
                "hotel_invoice/cancelled_invoice.html", **html_context
            )
        return render_template("hotel_invoice/invoice.html", **html_context)


@timeit
def as_pdf(invoice_number):
    html = as_html(invoice_number)
    # logger.info(f"Html content for hotel invoice {invoice_number} : {html}")
    pdf_generator = PdfGenerator(html=html)
    pdf_generator.generate_pdf()
    return pdf_generator.output_file


# pylint: disable=invalid-name
def _group_line_items_by_customer_and_sort_charges_by_date(line_items, status):
    # Groups charges by customer and returns a tuple of the structure
    # (customer, room, charges_grouped_by_customer_and_room)
    if status == InvoiceStatus.CANCELLED:
        return frozenset()
    charges_grouped_by_customer_and_room = defaultdict(list)
    for line_item in line_items:
        key = (line_item.customers, line_item.room)
        charges_grouped_by_customer_and_room[key].append(line_item.charge)

    customer_room_and_charges = [
        (customer, room, sorted(charges, key=lambda charge: charge.applicable_date))
        for (customer, room), charges in charges_grouped_by_customer_and_room.items()
    ]
    return customer_room_and_charges


def group_charges_by_tax_codes(line_items):
    tax_code_to_charge_details = {}

    for _, _, charges in line_items:
        for charge in charges:
            tax_code = charge.hsn_code
            charge_details = tax_code_to_charge_details.get(tax_code, {})
            for tax_item in charge.tax_breakup:
                charge_details[tax_item.code] = tax_item.amount + charge_details.get(
                    tax_item.code, 0
                )
            charge_details["sales_value"] = (
                charge_details.get("sales_value", 0) + charge.pre_tax
            )
            tax_code_to_charge_details[tax_code] = charge_details

    return tax_code_to_charge_details


def _get_booking_owner(booking_id):
    booking_details = get_booking_repository().get(booking_id=booking_id)
    return booking_details.owner
