import logging

from core.common import file_storage_service
from core.common.constants import IssuedToTypes
from reseller import app
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.invoices_to_be_sent import InvoiceDispatchService

logger = logging.getLogger(__name__)


def get_hotel_credit_note_pdf_url(hotel_id, credit_note_number):
    hotel_credit_note = GetHotelCreditNote.get_hotel_credit_note(
        credit_note_number, hotel_id
    )

    if hotel_credit_note:
        signed_url = _get_signed_url_for_hotel_credit_note(hotel_id, hotel_credit_note)
    else:
        msg = f"Document not found: {credit_note_number}"
        logger.info(msg)
        raise Exception(msg)

    if not signed_url:
        msg = f"PDF for Document: {credit_note_number} not found."
        logger.info(msg)
        raise Exception(msg)
    logger.info(f"Signed URL for document: {credit_note_number} is: {signed_url}")

    return signed_url


def _get_signed_url_for_hotel_credit_note(hotel_id, credit_note):
    signed_url = None

    if credit_note.issued_to_type == IssuedToTypes.CUSTOMER:
        signed_url = GetHotelCreditNote(
            credit_note_id=credit_note.credit_note_id
        ).get_pms_credit_note_signed_url(credit_note)
    else:
        if not credit_note.credit_note_url:
            invoice_dispatch_service = InvoiceDispatchService()
            invoice_dispatch_service.setup_invoices_storage_dir()
            credit_note_url_map = (
                invoice_dispatch_service.copy_hotel_credit_note_to_temp_dir(
                    hotel_credit_notes=[credit_note],
                    hotel_id=hotel_id,
                    failed_invoices=[],
                )
            )
            invoice_dispatch_service.teardown_invoices_storage_dir()
            if credit_note_url_map.items():
                signed_url = file_storage_service.sign_url(
                    next(iter(credit_note_url_map.values()))
                )
        else:
            signed_url = file_storage_service.sign_url(credit_note.credit_note_url)

    return signed_url
