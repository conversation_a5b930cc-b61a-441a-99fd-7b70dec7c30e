import copy
import logging

from core.common.constants import InvoiceStatus
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.services.hotel_invoice.update_hotel_invoice import UpdateHotelInvoice

logger = logging.getLogger(__name__)


def cancel_initiated_hotel_invoices_for_booking(hotel, booking_id):
    hotel_invoices_to_cancel = get_hotel_invoice_repository().find(
        hotel_id=hotel.uid, booking_id=booking_id, status=InvoiceStatus.INITIATED
    )
    for old_inv in hotel_invoices_to_cancel:
        logger.info(
            f"Cancelling initiated invoice for booking {booking_id}, "
            f"hotel {hotel.uid} and invoice_number as {old_inv.invoice_number}"
        )
        updated_invoice = copy.deepcopy(old_inv)
        updated_invoice.line_items = frozenset()
        updated_invoice.status = InvoiceStatus.CANCELLED
        UpdateHotelInvoice(old_inv).update_invoice(updated_invoice)
