import logging

from core.common import file_storage_service
from core.common.constants import IssuedToTypes
from reseller import app
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.invoices_to_be_sent import InvoiceDispatchService

logger = logging.getLogger(__name__)


def get_hotel_invoice_pdf_url(hotel_id, invoice_number):
    hotel_invoice = GetHotelInvoice.get_hotel_invoice(hotel_id, invoice_number)

    if hotel_invoice:
        signed_url = _get_signed_url_for_hotel_invoice(hotel_id, hotel_invoice)
    else:
        msg = f"Document not found: {invoice_number}"
        logger.info(msg)
        raise Exception(msg)

    if not signed_url:
        msg = f"PDF for Document: {invoice_number} not found."
        logger.info(msg)
        raise Exception(msg)
    logger.info(f"Signed URL for document: {invoice_number} is: {signed_url}")

    return signed_url


def _get_signed_url_for_hotel_invoice(hotel_id, invoice):
    signed_url = None

    if invoice.issued_to_type == IssuedToTypes.CUSTOMER:
        signed_url = GetHotelInvoice(
            invoice_id=invoice.invoice_id
        ).get_pms_invoice_signed_url(invoice)
    else:
        if not invoice.invoice_url:
            invoice_dispatch_service = InvoiceDispatchService()
            invoice_dispatch_service.setup_invoices_storage_dir()
            invoice_url_map = invoice_dispatch_service.copy_hotel_invoices_to_temp_dir(
                hotel_invoices=[invoice], hotel_id=hotel_id, failed_invoices=[]
            )
            invoice_dispatch_service.teardown_invoices_storage_dir()
            if invoice_url_map.items():
                signed_url = file_storage_service.sign_url(
                    next(iter(invoice_url_map.values()))
                )
        else:
            signed_url = file_storage_service.sign_url(invoice.invoice_url)

    return signed_url
