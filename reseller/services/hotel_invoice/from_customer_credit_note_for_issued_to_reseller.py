import logging
from datetime import datetime
from typing import List

from core.booking.data_classes.booking import Booking
from core.common.constants import (
    FLOOD_CESS_TAX,
    CreditNoteStatus,
    HsnCodes,
    IssuedByTypes,
    IssuedToTypes,
)
from core.common.data_classes.customer import Customer
from core.common.data_classes.tax_breakup import TaxBreakup
from core.common.integrations.tax.treebo_tax_serive import TreeboTax
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.hotel.data_classes.room import Room
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.constants import DEFAULT_TAX_CODES
from core.hotel_invoice.data_classes.credit_note_line_item import LineItem
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.services.convert_hotel_to_invoice_hotel import (
    convert_hotel_to_invoice_hotel,
)
from core.hotel_invoice.services.tax_markup import TaxMarkup
from core.invoice_mapping.data_class.invoice_mapping import InvoiceMapping
from integrations.einvoice.cleartax.einvoice_dto import IrpDetailsDTO
from integrations.einvoice.cleartax.einvoicing_service import EInvoicingService
from integrations.tax.treebo_tax_service import TreeboTaxService as Treebo
from object_registry import locate_instance
from reseller import app
from reseller.repository.invoice_mapping import get_invoice_mapping_repository
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.treebo_gst import TreeboGetGSTDetails

logger = logging.getLogger(__name__)


class GetHotelCreditNoteFromCustomerCreditNoteForIssuedToReseller:
    def __init__(
        self,
        invoice_id,
        customer_credit_note: CustomerCreditNote,
        booking: Booking,
        **kwargs,
    ):
        self.hotel_credit_note = GetHotelCreditNote(invoice_id).from_db()
        self.customer_credit_note = customer_credit_note
        self.booking = booking
        self.invoice_id = invoice_id
        self.kwargs = kwargs

    def get_hotel_credit_note(self) -> HotelCreditNote:
        hotel_credit_note = self.get_data_class()
        if not hotel_credit_note.is_einvoice:
            self.generate_irn(hotel_credit_note)
        return hotel_credit_note

    def get_data_class(self):
        hotel = GetHotel(self.booking.hotel_id).get_hotel()
        hotel_credit_note = HotelCreditNote(
            credit_note_id=self.hotel_credit_note.credit_note_id,
            booking_id=self.hotel_credit_note.booking_id,
            hotel_id=self.hotel_credit_note.hotel_id,
            pms_type=self.hotel_credit_note.pms_type,
            credit_note_number=self.hotel_credit_note.credit_note_number,
            issued_to_type=self._issued_to_type(),
            hotel=convert_hotel_to_invoice_hotel(
                hotel, self.hotel_credit_note.pms_type
            ),
            gst_details=TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details(),
            line_items=self._line_items(hotel),
            status=self._status(),
            source=self.hotel_credit_note.source,
            order_id=self.booking.order_id,
            reference_invoice_numbers=self.customer_credit_note.reference_invoice_numbers,
            bill_id=self.customer_credit_note.bill_id,
            credit_note_date=self.customer_credit_note.credit_note_date,
        )
        hotel_credit_note.created_at = self.hotel_credit_note.created_at
        return hotel_credit_note

    def _issued_to_type(self):
        if self.customer_credit_note.issued_by_type == IssuedByTypes.RESELLER:
            return IssuedToTypes.RESELLER
        else:
            return IssuedToTypes.CUSTOMER

    def _status(self):
        if self.customer_credit_note.status == CreditNoteStatus.CANCELLED:
            status = CreditNoteStatus.CANCELLED
        else:
            status = CreditNoteStatus.LOCKED
        return status

    def _line_items(self, hotel):
        customer_invoice_ids_linked_with_credit_notes = (
            self.customer_credit_note.get_linked_invoice_ids()
        )
        invoice_mappings: List[
            InvoiceMapping
        ] = get_invoice_mapping_repository().get_by_customer_invoice_ids(
            customer_invoice_ids=customer_invoice_ids_linked_with_credit_notes
        )
        customer_invoice_id_to_hotel_invoice_id_mapping = {
            invoice_mapping.customer_invoice_id: invoice_mapping.hotel_invoice_id
            for invoice_mapping in invoice_mappings
        }
        if self.customer_credit_note.gst_details.is_sez:
            line_items_json = TreeboTax().create_hotel_credit_note_line_item_json(
                self.customer_credit_note,
                customer_invoice_id_to_hotel_invoice_id_mapping,
            )
            updated_line_item_with_new_charge = []
            if line_items_json:
                tax_response = Treebo().get_line_item_with_new_charge(
                    line_items_json,
                    self.customer_credit_note.hotel_id,
                    hotel.gst_details.gstin_number,
                )
                line_item_with_new_charge_json = (
                    TreeboTax().map_response_data_with_line_item_json(
                        line_items_json, tax_response
                    )
                )
                updated_line_item_with_new_charge = (
                    TreeboTax().create_line_items_object(line_item_with_new_charge_json)
                )
                self._negatize_charges(updated_line_item_with_new_charge)
            for line_item in updated_line_item_with_new_charge:
                # blocking booking commission charges to go in hotel invoice
                if line_item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                    continue
                raw_charge = self._charge(line_item.charge)
                charge = self.sanitize_charge(raw_charge)
                line_item.charge = charge
            return updated_line_item_with_new_charge

        line_items = []
        for item in self.customer_credit_note.line_items:
            # blocking booking commission charges to go in hotel invoice
            if item.charge.hsn_code == HsnCodes.OtherAccommodationServices:
                continue
            raw_charge = self._charge(item.charge)
            charge = self.sanitize_charge(raw_charge)
            room = item.room or self._room(item.room_id)
            customers = self._customers(item.room_id)
            line_item = LineItem(
                charge,
                room,
                customers,
                original_invoice_id=customer_invoice_id_to_hotel_invoice_id_mapping.get(
                    item.original_invoice_id
                ),
            )
            line_items.append(line_item)
        return line_items

    def _charge(self, charge_item):
        if not charge_item.tax_breakup:
            charge_item.tax_breakup = frozenset(
                [TaxBreakup(code, 0, percent=0) for code in DEFAULT_TAX_CODES]
            )
        charge = (
            TaxMarkup(charge_item).maybe_markup()
            if datetime.date(self.hotel_credit_note.created_at)
            < datetime.strptime(
                app.config["INPUT_TAX_CREDIT_APPLICABILITY_DATE"], "%Y-%m-%d"
            ).date()
            else charge_item
        )
        charge.sanitize_tax_codes()
        return charge

    def _room(self, room_id):
        try:
            room = self.get_room_booking_for_item(room_id)
            number = room.number
            room_type_name = room.type
        except ValueError:
            number = ""
            room_type_name = ""

        return Room(number=number, room_type_name=room_type_name)

    def _customers(self, room_id):
        customers = []

        try:
            room = self.get_room_booking_for_item(room_id)
            customers_in_associated_room = room.guests
        except ValueError:
            customers_in_associated_room = []

        for cus in customers_in_associated_room:
            customer = Customer(
                uid=cus.stay_id,
                name=(cus.first_name, cus.last_name),
                email=getattr(cus, "email", ""),
                phone_number=getattr(cus, "phone_number", ""),
            )
            customers.append(customer)

        return customers

    def get_room_booking_for_item(self, room_id):
        try:
            return [
                room for room in self.booking.rooms if room.room_booking_id == room_id
            ][0]
        except IndexError:
            raise ValueError(
                f"Invalid room booking id {room_id} for booking {self.booking.booking_id}"
            )

    @classmethod
    def from_invoice_number(cls, invoice_number):
        raise NotImplementedError("Not implemented for GetBuyInvoice from SellInvoice")

    @staticmethod
    def sanitize_charge(raw_charge):
        raw_charge_tax_breakup = list(raw_charge.tax_breakup)
        for tax in raw_charge_tax_breakup:
            if tax.code.lower() == FLOOD_CESS_TAX.lower():
                raw_charge.tax -= tax.amount
        sanitized_tax_breakup = [
            tax
            for tax in raw_charge_tax_breakup
            if tax.code.lower() != FLOOD_CESS_TAX.lower()
        ]
        raw_charge.tax_breakup = frozenset(sanitized_tax_breakup)
        return raw_charge

    @staticmethod
    def _negatize_charges(line_items):
        for line_item in line_items:
            line_item.charge.tax = -abs(line_item.charge.tax)
            for tax in line_item.charge.tax_breakup:
                tax.amount = -abs(tax.amount)

    @staticmethod
    def generate_irn(credit_note: HotelCreditNote):
        e_invoice_service: EInvoicingService = locate_instance(EInvoicingService)
        if e_invoice_service.is_einvoicing_applicable(
            credit_note
        ).should_submit_to_irp():
            irp_details: IrpDetailsDTO = e_invoice_service.generate_irn(credit_note)
            credit_note.set_irp_details(irp_details)
