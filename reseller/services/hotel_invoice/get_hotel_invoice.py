from core.common.constants import InvoiceStatus
from core.common.utils.timeit import timeit
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice
from core.hotel_invoice.integrations.crs.hotel_invoice import (
    HotelInvoice as CRSHotelInvoice,
)
from reseller.repository.base_repository import DocumentNotFoundException
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.services.treebo_gst import TreeboGetGSTDetails


class GetHotelInvoice:
    def __init__(self, invoice_id, **kwargs):
        self.invoice_id = invoice_id
        self.kwargs = kwargs

    @classmethod
    def from_invoice_number(cls, invoice_number) -> HotelInvoice:
        query = {
            "invoice_number": invoice_number,
            "status": {"$ne": InvoiceStatus.CANCELLED},
        }
        hotel_invoice = get_hotel_invoice_repository().get(**query)
        return hotel_invoice

    def from_db(self) -> HotelInvoice:
        hotel_invoice = get_hotel_invoice_repository().get(invoice_id=self.invoice_id)
        return hotel_invoice

    @staticmethod
    def get_pms_meta(hotel_invoice, hotel, source):
        meta_data = CRSHotelInvoice.get_hotel_invoice_metadata(
            hotel_invoice, hotel, source
        )
        return meta_data

    @staticmethod
    @timeit
    def get_pms_invoice_url(hotel_invoice):
        url = CRSHotelInvoice.get_invoice_url(hotel_invoice.invoice_id)
        return url

    @staticmethod
    def get_pms_invoice_signed_url(hotel_invoice):
        signed_url = CRSHotelInvoice.get_invoice_signed_url(hotel_invoice.invoice_id)
        return signed_url

    @classmethod
    def from_pms(cls, customer_invoice_id, hotel, booking_id):
        hotel_invoice = CRSHotelInvoice.get_from_customer_invoice(
            customer_invoice_id=customer_invoice_id,
            booking_id=booking_id,
            invoice_hotel=hotel,
            gst_details=TreeboGetGSTDetails(hotel.legal_state_code).get_gst_details(),
        )
        return hotel_invoice

    @classmethod
    def get_hotel_invoice(cls, hotel_id, invoice_number):
        query = {
            "invoice_number": invoice_number,
            "hotel_id": hotel_id,
            "status": {"$ne": InvoiceStatus.CANCELLED},
        }
        try:
            hotel_invoice = get_hotel_invoice_repository().get_hotel_invoice(**query)
        except Exception as e:
            raise DocumentNotFoundException(e)
        return hotel_invoice
