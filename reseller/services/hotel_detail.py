import logging
from datetime import datetime

from core.hotel.data_classes.hotel_detail import HotelDetail
from core.notification import Slack, constants, notify
from reseller.repository.hotel_detail import get_hotel_detail_repository

logger = logging.getLogger(__name__)


def get_hotel_detail(hotel_id):
    try:
        hotel_detail = get_hotel_detail_repository().get_hotel_detail_by_hotel_id(
            hotel_id=hotel_id
        )
        if not hotel_detail:
            raise Exception(f"Hotel {hotel_id} is not on reseller")
        return hotel_detail
    except Exception as e:
        logger.error(
            f"Failed to get hotel detail for hotel {hotel_id} due to: {str(e)}"
        )
        raise


def save_hotel_detail(hotel_detail: HotelDetail):

    logger.info(f"Saving Hotel Details for Hotel: {hotel_detail.hotel_id}")
    try:
        get_hotel_detail_repository().create_or_update_hotel_detail(hotel_detail)
    except Exception as e:
        message = f"Error occurred while saving Hotel {hotel_detail.hotel_id}: {str(e)}"
        logger.info(message)
        notify(Slack(constants.Slack.B2B_APP_ALERTS, message))
