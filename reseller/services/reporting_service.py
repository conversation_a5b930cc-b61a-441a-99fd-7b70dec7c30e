import datetime
import logging

from core.common.utils.date import utc_to_ist
from core.notification.handlers.navision import (
    send_email_notification,
    send_slack_notification,
)
from object_registry import register_instance
from reseller.reporting.nav_purchase_report.purchase_report_service import (
    PurchaseInvoiceDataPushService,
)
from reseller.services.hotel_invoice.create_invoice_report import (
    CreateHotelInvoiceReport,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        PurchaseInvoiceDataPushService,
    ]
)
class ReportingService:
    def __init__(
        self,
        purchase_invoice_data_push_service: PurchaseInvoiceDataPushService,
    ):
        self.purchase_invoice_data_push_service = purchase_invoice_data_push_service

    def push_purchase_report_to_erp(self, report_data):
        for_date = report_data.get("date")
        invoice_numbers = report_data.get("invoice_numbers")
        unique_ref_id = report_data.get("unique_ref_id")
        try:
            logger.info(
                f"Started invoice pushing for args date: {for_date}, inv_nums: {invoice_numbers or unique_ref_id}"
            )
            if invoice_numbers or unique_ref_id:
                self.purchase_invoice_data_push_service.push_hotel_invoices(
                    invoice_numbers=invoice_numbers, unique_ref_id=unique_ref_id
                )
                return "Data push requested successfully"
            if not for_date:
                for_date = (
                    utc_to_ist(datetime.datetime.utcnow() - datetime.timedelta(days=1))
                    .date()
                    .strftime("%Y-%m-%d")
                )
            self.purchase_invoice_data_push_service.push_hotel_invoices_for_date(
                date=for_date
            )
            logger.info("Data push requested successfully")
        except Exception as e:
            msg = (
                f"Purchase report push to finance dashboard has failed with error: {e.args[0]} ARGS: date: {for_date}, "
                f"inv_nums: {invoice_numbers} "
            )
            logger.exception(msg)
            send_slack_notification(msg)
            send_email_notification(msg, for_date)
            raise e

    @staticmethod
    def generate_hotel_invoice_report(report_data):
        report_date = report_data.get("date")
        customer_invoice_numbers = report_data.get("invoice_numbers")
        customer_credit_note_numbers = report_data.get("credit_note_numbers")
        hotel_invoice_numbers = report_data.get("hotel_invoice_numbers")
        hotel_credit_note_numbers = report_data.get("hotel_credit_note_numbers")
        if (
            customer_invoice_numbers
            or customer_credit_note_numbers
            or hotel_invoice_numbers
            or hotel_credit_note_numbers
        ):
            logger.info(
                f"Running report for invoice ids {customer_invoice_numbers} {hotel_invoice_numbers} "
                f"and credit_note_numbers {customer_credit_note_numbers} {hotel_credit_note_numbers}"
            )
            CreateHotelInvoiceReport(
                customer_invoice_numbers=customer_invoice_numbers,
                customer_credit_note_numbers=customer_credit_note_numbers,
                hotel_invoice_numbers=hotel_invoice_numbers,
                hotel_credit_note_numbers=hotel_credit_note_numbers,
            ).create_hotel_invoice_report()
            return f"Invoice reports created successfully"
        if report_date:
            report_date = utc_to_ist(
                datetime.datetime.strptime(report_date, "%Y-%m-%d")
            ).date()
        else:
            report_date = utc_to_ist(
                datetime.datetime.utcnow()
            ).date() - datetime.timedelta(1)
        hotel_ids = report_data.get("hotel_ids", [])
        logger.info(f"Running report for date {report_date} and hotel_ids {hotel_ids}")
        CreateHotelInvoiceReport(
            report_date=report_date, hotel_ids=hotel_ids
        ).create_hotel_invoice_report()
        return f"Invoice reports created successfully"
