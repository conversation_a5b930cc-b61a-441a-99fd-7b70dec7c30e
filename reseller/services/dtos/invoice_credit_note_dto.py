from dataclasses import dataclass

from core.common.constants import EntityType
from core.hotel_invoice.data_classes.hotel_credit_note import HotelCreditNote
from core.hotel_invoice.data_classes.hotel_invoice import HotelInvoice


@dataclass
class Invoice(HotelInvoice):
    entity_type: str = None
    invoice_amount: str = None
    checkin_date: str = None
    checkout_date: str = None
    booking_reference_number: str = None

    @staticmethod
    def from_hotel_invoice_and_reports(hotel_invoice, hotel_invoice_reports):
        return Invoice(
            invoice_id=hotel_invoice.invoice_id,
            booking_id=hotel_invoice.booking_id,
            hotel_id=hotel_invoice.hotel_id,
            pms_type=hotel_invoice.pms_type,
            invoice_number=hotel_invoice.invoice_number,
            bill_id=hotel_invoice.bill_id,
            issued_to_type=hotel_invoice.issued_to_type,
            hotel=hotel_invoice.hotel,
            gst_details=hotel_invoice.gst_details,
            line_items=hotel_invoice.line_items,
            status=hotel_invoice.status,
            source=hotel_invoice.source,
            order_id=hotel_invoice.order_id,
            check_in=hotel_invoice.check_in,
            check_out=hotel_invoice.check_out,
            invoice_url=hotel_invoice.invoice_url,
            entity_type=EntityType.Invoice,
            invoice_date=hotel_invoice.invoice_date
            or hotel_invoice_reports.get("invoice_date"),
            invoice_amount=hotel_invoice_reports.get("total_invoice_amount", "")
            if hotel_invoice_reports
            else "",
            checkin_date=hotel_invoice.check_in.strftime("%Y-%m-%d")
            or (
                hotel_invoice_reports.get("checkin", "")
                if hotel_invoice_reports
                else ""
            ),
            checkout_date=hotel_invoice.check_out.strftime("%Y-%m-%d")
            or (
                hotel_invoice_reports.get("checkout", "")
                if hotel_invoice_reports
                else ""
            ),
            booking_reference_number=hotel_invoice.order_id,
        )

    @staticmethod
    def from_hotel_invoice_reports(hotel_invoice_report):
        return Invoice(
            invoice_id="",
            booking_id=hotel_invoice_report.booking_id,
            hotel_id=hotel_invoice_report.hotel_id,
            pms_type=hotel_invoice_report.pms_type,
            invoice_number=hotel_invoice_report.invoice_number,
            bill_id="",
            issued_to_type=hotel_invoice_report.issued_to_type,
            hotel=None,
            gst_details=None,
            line_items=[],
            status=hotel_invoice_report.status,
            source=hotel_invoice_report.source,
            entity_type=hotel_invoice_report.entity_type,
            invoice_date=hotel_invoice_report.invoice_date,
            invoice_amount=hotel_invoice_report.total_invoice_amount,
            checkin_date=hotel_invoice_report.checkin,
            checkout_date=hotel_invoice_report.checkout,
            booking_reference_number=hotel_invoice_report.reference_number,
        )


@dataclass
class CreditNote(HotelCreditNote):
    entity_type: str = None
    credit_note_amount: str = None
    checkin_date: str = None
    checkout_date: str = None
    booking_reference_number: str = None

    @staticmethod
    def from_hotel_credit_note_and_reports(hotel_credit_note, hotel_invoice_reports):
        return CreditNote(
            credit_note_id=hotel_credit_note.credit_note_id,
            booking_id=hotel_credit_note.booking_id,
            hotel_id=hotel_credit_note.hotel_id,
            pms_type=hotel_credit_note.pms_type,
            credit_note_number=hotel_credit_note.credit_note_number,
            bill_id=hotel_credit_note.bill_id,
            issued_to_type=hotel_credit_note.issued_to_type,
            hotel=hotel_credit_note.hotel,
            gst_details=hotel_credit_note.gst_details,
            line_items=hotel_credit_note.line_items,
            reference_invoice_numbers=hotel_credit_note.reference_invoice_numbers,
            status=hotel_credit_note.status,
            source=hotel_credit_note.source,
            order_id=hotel_credit_note.order_id,
            entity_type=EntityType.CreditNote,
            credit_note_url=hotel_credit_note.credit_note_url,
            credit_note_date=hotel_credit_note.credit_note_date
            or hotel_invoice_reports.get("invoice_date"),
            credit_note_amount=hotel_invoice_reports.get("total_invoice_amount", "")
            if hotel_invoice_reports
            else "",
            checkin_date=hotel_invoice_reports.get("checkin", "")
            if hotel_invoice_reports
            else "",
            checkout_date=hotel_invoice_reports.get("checkout", "")
            if hotel_invoice_reports
            else "",
            booking_reference_number=hotel_credit_note.order_id,
        )

    @staticmethod
    def from_hotel_invoice_reports(hotel_invoice_report):
        return CreditNote(
            credit_note_id="",
            booking_id=hotel_invoice_report.booking_id,
            hotel_id=hotel_invoice_report.hotel_id,
            pms_type=hotel_invoice_report.pms_type,
            credit_note_number=hotel_invoice_report.invoice_number,
            bill_id="",
            issued_to_type=hotel_invoice_report.issued_to_type,
            hotel=None,
            gst_details=None,
            line_items=[],
            status=hotel_invoice_report.status,
            source=hotel_invoice_report.source,
            entity_type=hotel_invoice_report.entity_type,
            credit_note_date=hotel_invoice_report.invoice_date,
            credit_note_amount=hotel_invoice_report.total_invoice_amount,
            checkin_date=hotel_invoice_report.checkin,
            checkout_date=hotel_invoice_report.checkout,
            booking_reference_number=hotel_invoice_report.reference_number,
        )


@dataclass
class InvoicesAndCreditNotes:
    invoices: [Invoice]
    credit_notes: [CreditNote]

    def __init__(self, invoices, credit_notes):
        self.invoices = invoices
        self.credit_notes = credit_notes
