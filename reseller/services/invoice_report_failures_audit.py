from datetime import datetime

from core.common.constants import InvoiceReportStatus
from core.failure_audit.data_classes.invoice_report_failure import InvoiceReportFailure
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.invoice_report_failure import (
    get_invoice_report_failure_repository,
)


def record_invoice_report_failure(
    process_name,
    hotel_id=None,
    invoice_id=None,
    invoice_number=None,
    entity_type=None,
    error_message=None,
    error_trace=None,
):
    failed_on = datetime.now().strftime("%Y-%m-%d")
    try:
        get_invoice_report_failure_repository().get_failure(
            failed_on=failed_on, hotel_id=hotel_id, invoice_id=invoice_id
        )
        data_to_update = dict(
            failure_description=str(error_message),
            failure_trace=str(error_trace),
            status=InvoiceReportStatus.FAILED_ON_RETRY,
        )
        query = {
            "failed_on": failed_on,
            "hotel_id": hotel_id,
            "invoice_id": invoice_id,
            "invoice_number": invoice_number,
        }
        get_invoice_report_failure_repository().update(query, data_to_update)
    except DocumentDoesNotExist as e:
        invoice_report_failure = InvoiceReportFailure(
            process_name=process_name,
            hotel_id=hotel_id,
            invoice_id=invoice_id,
            invoice_number=invoice_number,
            entity_type=entity_type,
            failure_description=str(error_message),
            failure_trace=str(error_trace),
            failed_on=datetime.now().strftime("%Y-%m-%d"),
            status=InvoiceReportStatus.FAILED,
        )
        get_invoice_report_failure_repository().create(invoice_report_failure)


def mark_invoice_report_generation_success_on_retry(invoice_number):
    try:
        get_invoice_report_failure_repository().get_failure_from_invoice_number(
            invoice_number=invoice_number
        )
        data_to_update = dict(
            last_retry_at=datetime.utcnow(),
            status=InvoiceReportStatus.SUCCESS_ON_RETRY,
            failure_description="Ingestion success after retry",
        )
        query = {"invoice_number": invoice_number}
        get_invoice_report_failure_repository().update(query, data_to_update)
    except DocumentDoesNotExist as e:
        return None
