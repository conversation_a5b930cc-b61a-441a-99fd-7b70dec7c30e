import logging

from core.hotel.data_classes.hotel_detail import HotelDetail
from object_registry import register_instance
from reseller.services.hotel_detail import save_hotel_detail
from reseller.services.invoice_counter import save_hotel_gstin
from reseller.workflows.hotel.dto.catalog_event import CalatlogHotelSyncDTO

logger = logging.getLogger(__name__)


@register_instance()
class CatalogApplicationService:
    @staticmethod
    def sync_hotel_details(hotel_data):
        CalatlogHotelSyncDTO().load(
            data=dict(
                hotel_id=hotel_data["cs_property_id"],
                gstin=hotel_data["data"]["property_details"]["gstin"],
            )
        )
        hotel_id = hotel_data["cs_property_id"]
        gstin = hotel_data["data"]["property_details"]["gstin"]
        save_hotel_gstin(hotel_id=hotel_id, gstin=gstin)
        save_hotel_detail(hotel_detail=HotelDetail.from_json(hotel_data.get("data")))
        logger.info(f"Successfully acked catalog_event for hotel {hotel_id}")
