import datetime
import logging
from datetime import timedelta

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.date import utc_to_ist
from core.hotel.services.hotel import GetHotel
from core.hotel_invoice.constants import ReportConstants
from core.notification import notify
from core.notification.handlers.hotel_invoice_reports.missing_report_handler import (
    MissingReportHandler,
)
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)

logger = logging.getLogger(__name__)


def calculate_prefix_and_suffix_count(invoice_report_number):
    rev_invoice_seq_suffix = ""
    rev_report_invoice_number = invoice_report_number[::-1]
    for c in rev_report_invoice_number:
        if c.isdigit():
            rev_invoice_seq_suffix += c
        else:
            break
    prefix = rev_report_invoice_number.split(rev_invoice_seq_suffix, 1)[1][::-1]
    return prefix, 0 if len(rev_invoice_seq_suffix) < 8 else 8


class FindMissingReports(TreeboBaseAPI):
    def get(self):
        for_date = utc_to_ist(datetime.datetime.utcnow() - timedelta(days=1)).date()
        logger.info(f"Running report check for {for_date}")
        hotel_ids = get_hotel_invoice_reports_repository().get_hotel_ids_for_date_range(
            from_date=for_date, to_date=for_date
        )
        missing_reports_aggregate = []
        for cs_id in hotel_ids:
            try:
                logger.info(f"Checking for hotel_id {cs_id}")
                hotel = GetHotel(cs_id).get_hotel()
                invoice_reports = []
                latest_invoice_number = ""
                for pms_type in ReportConstants.SUPPORTED_PMS_TYPES:
                    query = {
                        "$query": {
                            "$and": [
                                {
                                    "$or": [
                                        {"report_date": str(for_date)},
                                        {"invoice_date": str(for_date)},
                                    ]
                                },
                                {"hotel_id": hotel.pms_id(pms_type)},
                            ]
                        },
                        "$orderby": {"invoice_date": -1},
                    }
                    pms_invoice_reports = get_hotel_invoice_reports_repository().find(
                        **query
                    )
                    invoice_reports += pms_invoice_reports
                    if pms_type == ReportConstants.CRS_PMS:
                        latest_invoice_number = pms_invoice_reports[0].invoice_number
                report_invoice_numbers = sorted(
                    set(
                        [
                            report.invoice_number
                            for report in invoice_reports
                            if not report.invoice_number.startswith("CN")
                        ]
                    )
                )
                (
                    invoice_seq_prefix,
                    invoice_seq_suffix_count,
                ) = calculate_prefix_and_suffix_count(latest_invoice_number)
                invoice_number_suffices = sorted(
                    [
                        int(inv_number.split(invoice_seq_prefix)[1])
                        for inv_number in report_invoice_numbers
                        if invoice_seq_prefix in inv_number
                    ]
                )
                first_seq = invoice_number_suffices[0]
                last_seq = invoice_number_suffices[-1]
                invoice_range = last_seq - first_seq
                if not invoice_range == len(invoice_number_suffices) - 1:
                    generated_invoice_numbers_in_range = [
                        f'{invoice_seq_prefix}{"%0*d" % (invoice_seq_suffix_count, seq)}'
                        for seq in list(range(first_seq, last_seq))
                    ]
                    reports_in_range = get_hotel_invoice_reports_repository().find(
                        **{
                            "invoice_number": {
                                "$in": generated_invoice_numbers_in_range
                            }
                        }
                    )
                    report_invoice_numbers_in_range = set(
                        [report.invoice_number for report in reports_in_range]
                    )
                    if not invoice_range == len(report_invoice_numbers_in_range):
                        missing_reports = (
                            set(generated_invoice_numbers_in_range)
                            - report_invoice_numbers_in_range
                        )
                        missing_reports_aggregate.append({cs_id: missing_reports})
                        logger.info(
                            f"Missing reports for hotel id: {cs_id}; {missing_reports}"
                        )

            except Exception as e:
                msg = f"Failed to check reports for hotel id: {cs_id}; `Exception:` {e}"
                logger.exception(msg)
                failure_handler = MissingReportHandler(message=msg)
                notify(failure_handler.slack())

        if missing_reports_aggregate:
            msg = f"`Missing reports for {for_date}`: {missing_reports_aggregate}"
            failure_handler = MissingReportHandler(message=msg)
            notify(failure_handler.slack())
        logger.info(
            f"Completed running check for reports. Missing reports: {missing_reports_aggregate}"
        )

        return f"Completed running report check"
