from reseller.repository.booking import get_booking_repository


class CreateOrUpdateBooking:
    def __init__(self, booking, hotel, **kwargs):
        self.booking = booking
        self.hotel = hotel
        self.kwargs = kwargs

    def create_or_update_booking(self):
        query = {
            "booking_id": self.booking.booking_id,
            "hotel_id": self.hotel.uid,
            "pms_type": self.booking.pms_type,
        }
        get_booking_repository().update(
            query=query, dict_or_data_class=self.booking, upsert=True
        )
