from reseller.services.booking.create_or_update_booking import CreateOrUpdateBooking
from reseller.services.booking.get_booking import GetBooking


def ingest_booking(booking_id, hotel, pms_type):
    booking = GetBooking(
        booking_id=booking_id, hotel=hotel, pms_type=pms_type
    ).from_pms()
    CreateOrUpdateBooking(booking=booking, hotel=hotel).create_or_update_booking()
    return booking
