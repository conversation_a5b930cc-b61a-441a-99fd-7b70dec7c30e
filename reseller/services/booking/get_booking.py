from core.booking.data_classes.booking import Booking
from core.booking.integrations.crs.booking import Booking as CRSBooking
from reseller.repository.booking import get_booking_repository


class GetBooking:
    def __init__(self, booking_id, hotel, pms_type):
        self.booking_id = booking_id
        self.hotel = hotel
        self.pms_type = pms_type

    def from_db(self) -> Booking:
        return get_booking_repository().get(
            booking_id=self.booking_id, hotel_id=self.hotel.uid, pms_type=self.pms_type
        )

    def from_pms(self) -> Booking:
        booking = CRSBooking.get_booking(booking_id=self.booking_id, hotel=self.hotel)
        return booking
