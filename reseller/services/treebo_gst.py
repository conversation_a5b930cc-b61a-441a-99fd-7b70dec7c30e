from core.common.data_classes.gst_details import GstDetails
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.treebo_gst import get_treebo_gst_details_repository


class TreeboGetGSTDetails:
    def __init__(self, state_code):
        self.state_code = state_code

    def get_gst_details(self):
        try:
            return get_treebo_gst_details_repository().get(state_code=self.state_code)
        except DocumentDoesNotExist:
            return GstDetails.empty()
