from datetime import datetime

from core.common.constants import IngestionStatus
from core.failure_audit.data_classes.ingestion_failure import IngestionFailure
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.ingestion_audit import get_ingestion_failure_repository


def record_ingestion_failure(resource_id, resource_type, error_description):
    try:
        get_ingestion_failure_repository().get_failure(resource_id=resource_id)
        data_to_update = dict(
            last_retry_at=datetime.utcnow(),
            status=IngestionStatus.FAILED_ON_RETRY,
            failure_description=error_description,
        )
        query = {"resource_id": resource_id, "resource_type": resource_type}
        get_ingestion_failure_repository().update(query, data_to_update)

    except DocumentDoesNotExist as e:
        failed_on = datetime.now().strftime("%Y-%m-%d")
        ingestion_failure = IngestionFailure(
            resource_id=resource_id,
            resource_type=resource_type,
            status=IngestionStatus.FAILED,
            failure_description=error_description,
            failed_on=failed_on,
        )
        get_ingestion_failure_repository().create(ingestion_failure)


def mark_ingestion_success(resource_id):
    try:
        get_ingestion_failure_repository().get_failure(resource_id=resource_id)
        data_to_update = dict(
            last_retry_at=datetime.now(),
            status=IngestionStatus.SUCCESS_ON_RETRY,
            failure_description="Ingestion success after retry",
        )
        query = {"resource_id": resource_id}
        get_ingestion_failure_repository().update(query, data_to_update)

    except DocumentDoesNotExist as e:
        return None
