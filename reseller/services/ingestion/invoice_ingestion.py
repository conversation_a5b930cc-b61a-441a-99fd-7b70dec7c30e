import logging

from core.booking.integrations.crs.booking import Booking as CRSBooking
from core.common.constants import InvoiceStatus, IssuedByTypes, IssuedToTypes, PmsType
from core.common.exceptions import NonSupportedChannelType
from core.customer_invoice.data_classes.customer_invoice import CustomerInvoice
from core.customer_invoice.integrations.crs.invoice import Invoice
from core.hotel.services.hotel import GetHotel
from integrations.crs.treebo_crs.exceptions import InvalidHotelInvoiceId
from integrations.crs.treebo_crs.invoice.get_invoice import CRSGetInvoice
from object_registry import register_instance
from reseller.constants import ResourceTypes
from reseller.exceptions import ConcurrentIngestionException
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.repository.ingestion_lock_repository import (
    get_ingestion_process_locks_repository,
)
from reseller.repository.invoice_mapping import get_invoice_mapping_repository
from reseller.services.customer_invoice.create_or_update_customer_invoice import (
    CreateOrUpdateCustomerInvoice,
)
from reseller.services.hotel_invoice.create_hotel_invoice import CreateHotelInvoice
from reseller.services.hotel_invoice.from_customer_invoice_for_issued_to_customer import (
    GetHotelInvoiceFromCustomerInvoiceForIssuedToCustomer,
)
from reseller.services.hotel_invoice.from_customer_invoice_for_issued_to_treebo import (
    GetHotelInvoiceFromCustomerInvoiceForIssuedToReseller,
)
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.hotel_invoice.update_hotel_invoice import UpdateHotelInvoice
from reseller.services.ingestion import ingestion_audit
from reseller.services.ingestion.booking_ingestion import BookingIngestionService
from reseller.services.ingestion.ingestion_helper import IngestionHelper
from reseller.services.invoice_mapping import MappingService
from reseller.workflows.hotel.filter_reseller_hotels import ResellerService

logger = logging.getLogger(__name__)


@register_instance(dependencies=[BookingIngestionService])
class InvoiceIngestionService:
    def __init__(
        self,
        booking_ingestion_service: BookingIngestionService,
    ):
        self.booking_ingestion_service = booking_ingestion_service
        self.pms_type = PmsType.CRS
        self.resource_type = ResourceTypes.INVOICE

    def ingest_invoice(self, resource_id):
        try:
            invoice_data = CRSGetInvoice(invoice_id=resource_id).get()
            if not invoice_data:
                raise Exception(
                    f"Invoice with given invoice_id: {resource_id} doesnt Exist"
                )
            if not invoice_data.get("invoice_number"):
                logger.info(
                    f"Ignoring invoice ingestion for resource {resource_id}, as it is preview invoice"
                )
                return
            hotel_id = invoice_data["vendor_details"]["hotel_id"]
            bill_id = invoice_data["bill_id"]
            hotel = GetHotel(hotel_id).get_hotel()
            booking = CRSBooking.get_booking_from_bill_id(bill_id, hotel)
            invoice = Invoice.get_invoice(
                booking, resource_id, hotel, thsc_invoice_data=invoice_data
            )

            existing_invoice = self._find_existing_hotel_invoice_from_customer_invoice(
                customer_invoice=invoice, hotel=hotel
            )

            if existing_invoice and existing_invoice.is_locked_in_crs:
                logger.info(
                    f"Skipping ingestion/re-ingestion for invoice {resource_id} as it is already locked"
                )
                self._mark_ingestion_completion(resource_id)
                return "Skipping ingestion/re-ingestion for e-invoiced invoice"

            self.booking_ingestion_service.ingest_booking_if_not_present(
                booking.booking_id, hotel
            )

            if IngestionHelper.acquire_lock_for_critical_section(
                self.resource_type, resource_id
            ):
                logger.info(
                    f"Starting ingestion for customer invoice {resource_id}, hotel {hotel_id}"
                )
                self._ingest_customer_invoice(invoice, booking.booking_id, hotel)

                logger.info(
                    f"Starting ingestion for hotel invoice for hotel {hotel_id}"
                )
                self._ingest_hotel_invoice(
                    customer_invoice=invoice, booking=booking, hotel=hotel
                )

                self._mark_ingestion_completion(resource_id)

                logger.info(
                    f"Hotel and Customer Invoice: {resource_id} successfully ingested"
                )
                return "Ingestion successful"
            else:
                raise ConcurrentIngestionException(self.resource_type, resource_id)
        except Exception as e:
            get_ingestion_process_locks_repository().remove_lock_if_present(
                self.resource_type, resource_id
            )
            raise e

    def _find_existing_hotel_invoice_from_customer_invoice(
        self, customer_invoice, hotel
    ):
        try:
            mapping_service = MappingService(
                hotel=hotel,
                pms_type=self.pms_type,
            )
            mapping = mapping_service.get(customer_invoice.invoice_id, proforma=False)
            return GetHotelInvoice(mapping.hotel_invoice_id).from_db()
        except DocumentDoesNotExist:
            return None

    def _mark_ingestion_completion(self, resource_id):
        ingestion_audit.mark_ingestion_success(resource_id)
        get_ingestion_process_locks_repository().remove_lock_if_present(
            self.resource_type, resource_id
        )

    def _ingest_hotel_invoice(self, customer_invoice, booking, hotel):
        if customer_invoice.status != InvoiceStatus.LOCKED:
            logger.info(
                f"Received sell invoice {customer_invoice.invoice_id} for booking {booking.booking_id} "
                f"with status {customer_invoice.status}"
            )
        hotel_invoice_id = self._get_hotel_invoice_id_from_mapping(
            customer_invoice, booking.booking_id, hotel
        )
        if hotel_invoice_id:
            logger.info(
                f"Starting ingestion for Hotel invoice belonging to booking: {booking.booking_id}, "
                f"hotel_invoice_id {hotel_invoice_id}, customer_invoice_number: {customer_invoice.invoice_number}"
            )
            current_hotel_invoice, new_hotel_invoice = self._get_new_hotel_invoice(
                booking, customer_invoice, hotel_invoice_id
            )
            UpdateHotelInvoice(current_hotel_invoice).update_invoice(new_hotel_invoice)
        else:
            logger.info(f"No hotel invoice to ingest")

    @staticmethod
    def _get_new_hotel_invoice(booking, customer_invoice, hotel_invoice_id):
        logger.info(
            f"Ingesting buy invoice {hotel_invoice_id} for booking {booking.booking_id} and "
            f"sell invoice {customer_invoice.invoice_number}"
        )
        current_hotel_invoice = GetHotelInvoice(hotel_invoice_id).from_db()
        if current_hotel_invoice.issued_to_type == IssuedToTypes.RESELLER:
            new_hotel_invoice = GetHotelInvoiceFromCustomerInvoiceForIssuedToReseller(
                hotel_invoice_id, customer_invoice=customer_invoice, booking=booking
            ).get_invoice()
        elif current_hotel_invoice.issued_to_type == IssuedToTypes.CUSTOMER:
            new_hotel_invoice = GetHotelInvoiceFromCustomerInvoiceForIssuedToCustomer(
                hotel_invoice_id, customer_invoice=customer_invoice, booking=booking
            ).get_invoice()
        else:
            raise Exception
        new_hotel_invoice.is_btt_invoice = customer_invoice.is_btt_invoice()
        return current_hotel_invoice, new_hotel_invoice

    def _get_hotel_invoice_id_from_mapping(
        self, customer_invoice: CustomerInvoice, booking_id, hotel
    ) -> str:
        mapping_service = MappingService(
            hotel=hotel,
            pms_type=self.pms_type,
        )
        try:
            mapping = mapping_service.get(
                customer_invoice_id=customer_invoice.invoice_id, proforma=False
            )
            return mapping.hotel_invoice_id
        except DocumentDoesNotExist:
            try:
                return self._create_bare_hotel_invoice_and_mapping(
                    customer_invoice, booking_id, hotel
                )
            except InvalidHotelInvoiceId:
                logger.error(
                    f"Skipping customer invoice {customer_invoice.invoice_id} as customer invoice doesn't have"
                    f" hotel invoice attached to it"
                )

    def _create_bare_hotel_invoice_and_mapping(
        self, customer_invoice, booking_id, hotel
    ):
        mapping_service = MappingService(
            hotel=hotel,
            pms_type=self.pms_type,
        )

        if customer_invoice.issued_by_type == IssuedByTypes.RESELLER:
            hotel_invoice = GetHotelInvoice.from_pms(
                customer_invoice.invoice_id, hotel, booking_id
            )
            # lock corresponding buy side hotel invoice if sell side customer invoice is locked in CRS
            hotel_invoice.is_locked_in_crs = customer_invoice.is_locked_in_crs

            try:
                self._update_hotel_invoice_and_remove_old_mapping(hotel_invoice)
            except DocumentDoesNotExist:
                CreateHotelInvoice.create_invoice_from_dataclass(hotel_invoice)
        elif customer_invoice.issued_by_type == IssuedByTypes.HOTEL:
            hotel_invoice = CreateHotelInvoice(
                hotel=hotel,
                pms_type=self.pms_type,
                booking_id=booking_id,
                source="",
                issued_to_type=IssuedToTypes.CUSTOMER,
                invoice_number=customer_invoice.invoice_number,
                gst_details=customer_invoice.gst_details,
                bill_id=customer_invoice.bill_id,
                invoice_date=customer_invoice.invoice_date,
                is_locked_in_crs=customer_invoice.is_locked_in_crs,
            ).create_invoice()

        else:
            raise NonSupportedChannelType(hotel_id=hotel.uid, booking_id=booking_id)

        mapping_service.create(
            customer_invoice_id=customer_invoice.invoice_id,
            customer_invoice_number=customer_invoice.invoice_number,
            hotel_invoice_id=hotel_invoice.invoice_id,
            hotel_invoice_number=hotel_invoice.invoice_number,
            booking_id=booking_id,
            proforma=False,
        )
        return hotel_invoice.invoice_id

    @staticmethod
    def _update_hotel_invoice_and_remove_old_mapping(hotel_invoice):
        query = {"invoice_number": hotel_invoice.invoice_number}
        current_hotel_invoice = get_hotel_invoice_repository().get(**query)
        hotel_invoice.invoice_id = current_hotel_invoice.invoice_id
        get_hotel_invoice_repository().update(
            {"invoice_id": current_hotel_invoice.invoice_id}, hotel_invoice
        )
        query = {"hotel_invoice_id": current_hotel_invoice.invoice_id}
        get_invoice_mapping_repository().delete(**query)

    def _ingest_customer_invoice(self, customer_invoice, booking_id, hotel):
        logger.info(
            f"Received following invoice {customer_invoice.invoice_id} from {self.pms_type} "
            f"for booking {booking_id}"
        )

        if ResellerService().is_reseller(
            hotel_id=hotel.uid, datetime=customer_invoice.source_created_at
        ):
            logger.info(f"Keeping invoice if hotel is on reseller model")
            CreateOrUpdateCustomerInvoice(
                customer_invoice=customer_invoice
            ).create_or_update_invoice()

        else:
            logger.info(f"No operations to execute for updating customer invoices")
