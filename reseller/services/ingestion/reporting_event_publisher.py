import logging

from kombu import Exchange, Producer
from ths_common.exceptions import CRSException
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from reseller.infrastructure.consumers.consumer_config import (
    IngestionEventPublisherConfig,
    ResellerReportingConfig,
)
from reseller.infrastructure.messaging.queue_service import BaseQueueService

logger = logging.getLogger(__name__)


@register_instance()
class ReportingEventPublisher(BaseQueueService):
    def _setup_entities(self):
        config = ResellerReportingConfig()
        self._reporting_event_exchange = Exchange(
            config.exchange_name, type=config.exchange_type, durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._reporting_event_exchange
            )

    def publish(self, report_data, routing_key):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        logger.info("Publishing integration event for tenant: {0}".format(tenant_id))
        self._initialize()
        if not self._tenant_wise_producers[tenant_id]:
            raise CRSException(
                description=f"RMQ Producer not configured for tenant_id: {tenant_id}"
            )
        self._publish(
            self._tenant_wise_producers[tenant_id],
            report_data,
            routing_key,
        )
