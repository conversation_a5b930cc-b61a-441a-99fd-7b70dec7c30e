import logging

from core.common.utils.date import maybe_convert_string_to_datetime
from core.hotel.data_classes.hotel_detail import HotelDeta<PERSON>, HotelOwner
from core.hotel.services.hotel import GetHotel
from reseller.services.hotel_detail import save_hotel_detail
from reseller.services.invoice_counter import save_hotel_gstin

logger = logging.getLogger(__name__)


class HotelIngestionService:
    def __init__(self, hotel_id, date, sold_as):
        self.hotel = GetHotel(hotel_id).get_hotel()
        self.date = maybe_convert_string_to_datetime(date)
        self.sold_as = sold_as.lower()

    def ingest_hotels(self):
        logger.info(
            f"Starting ingestion for Hotel, hotel_id: {self.hotel.uid}, "
            f"and gstin: {self.hotel.gst_details.gstin_number}"
        )
        save_hotel_gstin(
            hotel_id=self.hotel.uid, gstin=self.hotel.gst_details.gstin_number
        )
        save_hotel_detail(
            hotel_detail=HotelDetail(
                hotel_id=self.hotel.uid,
                hotel_name=self.hotel.name,
                hotel_owners=[
                    HotelOwner(
                        email=self.hotel.email, phone_number=self.hotel.phone_number
                    )
                ],
            )
        )
