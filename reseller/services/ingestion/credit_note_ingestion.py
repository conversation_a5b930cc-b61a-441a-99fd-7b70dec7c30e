import logging

from thsc.crs.entities.billing import CreditNote as THSCCreditNote

from core.booking.integrations.crs.booking import Booking as CRSBooking
from core.common.constants import (
    CreditNoteStatus,
    IssuedByTypes,
    IssuedToTypes,
    PmsType,
)
from core.common.exceptions import NonSupportedChannelType
from core.customer_invoice.data_classes.customer_credit_note import CustomerCreditNote
from core.customer_invoice.integrations.crs.credit_note import CreditNote
from core.hotel.services.hotel import GetHotel
from integrations.crs.treebo_crs.exceptions import InvalidHotelCreditNoteId
from object_registry import register_instance
from reseller.constants import ResourceTypes
from reseller.exceptions import ConcurrentIngestionException
from reseller.repository.base_repository import DocumentDoesNotExist
from reseller.repository.ingestion_lock_repository import (
    get_ingestion_process_locks_repository,
)
from reseller.services.credit_note_mapping import CreditNoteMappingService
from reseller.services.customer_invoice.create_or_update_customer_credit_note import (
    CreateOrUpdateCustomerCreditNote,
)
from reseller.services.hotel_invoice.create_hotel_credit_note import (
    CreateHotelCreditNote,
)
from reseller.services.hotel_invoice.from_customer_credit_note_for_issued_to_customer import (
    GetHotelCreditNoteFromCustomerCreditNoteForIssuedToCustomer,
)
from reseller.services.hotel_invoice.from_customer_credit_note_for_issued_to_reseller import (
    GetHotelCreditNoteFromCustomerCreditNoteForIssuedToReseller,
)
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.hotel_invoice.update_hotel_credit_note import (
    UpdateHotelCreditNote,
)
from reseller.services.ingestion import ingestion_audit
from reseller.services.ingestion.booking_ingestion import BookingIngestionService
from reseller.services.ingestion.ingestion_helper import IngestionHelper
from reseller.workflows.hotel.filter_reseller_hotels import ResellerService

logger = logging.getLogger(__name__)


@register_instance(dependencies=[BookingIngestionService])
class CreditNoteIngestionService:
    def __init__(
        self,
        booking_ingestion_service: BookingIngestionService,
    ):
        self.booking_ingestion_service = booking_ingestion_service
        self.pms_type = PmsType.CRS
        self.resource_type = ResourceTypes.CREDIT_NOTE

    def ingest_credit_note(self, resource_id):
        try:
            credit_note_data = THSCCreditNote.get(credit_note_id=resource_id)
            if not credit_note_data:
                raise Exception(
                    f"Credit Note with given credit_note_id: {resource_id} doesnt Exist"
                )
            hotel_id = credit_note_data.vendor_details["hotel_id"]
            bill_id = credit_note_data.bill_id
            hotel = GetHotel(hotel_id).get_hotel()
            booking = CRSBooking.get_booking_from_bill_id(bill_id, hotel)
            credit_note = CreditNote.get_credit_note(
                booking,
                resource_id,
                hotel,
                bill_id,
            )

            self.booking_ingestion_service.ingest_booking_if_not_present(
                booking.booking_id, hotel
            )

            if IngestionHelper.acquire_lock_for_critical_section(
                self.resource_type, resource_id
            ):
                logger.info(
                    f"Starting ingestion for credit_note {resource_id}, hotel {hotel_id}"
                )
                self._ingest_credit_note(credit_note, booking.booking_id, hotel)

                logger.info(
                    f"Starting ingestion for hotel credit note for hotel {hotel_id}"
                )
                self._ingest_hotel_credit_note(
                    customer_credit_note=credit_note, booking=booking, hotel=hotel
                )

                ingestion_audit.mark_ingestion_success(resource_id)
                get_ingestion_process_locks_repository().remove_lock_if_present(
                    self.resource_type, resource_id
                )

                logger.info(
                    f"Hotel and Customer Credit Note: {resource_id} successfully ingested"
                )
            else:
                raise ConcurrentIngestionException(self.resource_type, resource_id)
        except Exception as e:
            get_ingestion_process_locks_repository().remove_lock_if_present(
                self.resource_type, resource_id
            )
            raise e

    def _ingest_hotel_credit_note(self, customer_credit_note, booking, hotel):
        if customer_credit_note.status != CreditNoteStatus.LOCKED:
            logger.info(
                f"Received customer credit note {customer_credit_note.credit_note_id} "
                f"for booking {booking.booking_id} with status {customer_credit_note.status}"
            )
        hotel_credit_note_id = self._get_hotel_credit_note_id_from_mapping(
            customer_credit_note, booking.booking_id, hotel
        )
        if hotel_credit_note_id:
            logger.info(
                f"Starting ingestion for Credit note belonging to booking: {booking.booking_id}, "
                f"hotel_credit_note_id {hotel_credit_note_id}, "
                f"customer_credit_note_id: {customer_credit_note.credit_note_id}"
            )
            current_credit_note, new_credit_note = self._get_new_credit_note(
                booking, customer_credit_note, hotel_credit_note_id
            )
            UpdateHotelCreditNote(current_credit_note).update_hotel_credit_note(
                new_credit_note
            )
        else:
            logger.info(f"No hotel credit note to ingest")

    def _get_hotel_credit_note_id_from_mapping(
        self, customer_credit_note: CustomerCreditNote, booking_id, hotel
    ) -> str:
        mapping_service = CreditNoteMappingService(
            hotel=hotel,
            pms_type=self.pms_type,
        )
        try:
            mapping = mapping_service.get(
                customer_credit_note_id=customer_credit_note.credit_note_id
            )
            return mapping.hotel_credit_note_id
        except DocumentDoesNotExist:
            try:
                return self._create_bare_hotel_credit_note_mapping(
                    customer_credit_note, booking_id, hotel
                )
            except InvalidHotelCreditNoteId:
                logger.error(
                    f"Skipping credit note {customer_credit_note.credit_note_id} as credit note doesn't have "
                    f"hotel credit note attached to it"
                )

    def _create_bare_hotel_credit_note_mapping(
        self, customer_credit_note, booking_id, hotel
    ):
        mapping_service = CreditNoteMappingService(
            hotel=hotel,
            pms_type=self.pms_type,
        )

        if customer_credit_note.issued_by_type == IssuedByTypes.RESELLER:
            hotel_credit_note = GetHotelCreditNote.from_pms(
                customer_credit_note.credit_note_id, hotel, booking_id
            )
            if hotel_credit_note is None:
                return None
            CreateHotelCreditNote.create_hotel_credit_note_from_dataclass(
                hotel_credit_note
            )
        elif customer_credit_note.issued_by_type == IssuedByTypes.HOTEL:
            hotel_credit_note = CreateHotelCreditNote(
                hotel=hotel,
                pms_type=self.pms_type,
                booking_id=booking_id,
                source="",
                issued_to_type=IssuedToTypes.CUSTOMER,
                credit_note_number=customer_credit_note.credit_note_number,
                reference_invoice_numbers=customer_credit_note.reference_invoice_numbers,
                gst_details=customer_credit_note.gst_details,
                bill_id=customer_credit_note.bill_id,
                credit_note_date=customer_credit_note.credit_note_date,
            ).create_hotel_credit_note()
        else:
            raise NonSupportedChannelType(hotel_id=hotel.uid, booking_id=booking_id)

        mapping_service.create(
            customer_credit_note_number=customer_credit_note.credit_note_number,
            hotel_credit_note_number=hotel_credit_note.credit_note_number,
            hotel_credit_note_id=hotel_credit_note.credit_note_id,
            booking_id=booking_id,
            customer_credit_note_id=customer_credit_note.credit_note_id,
        )
        return hotel_credit_note.credit_note_id

    def _ingest_credit_note(self, customer_credit_note, booking_id, hotel):
        logger.info(
            f"Received following credit note {customer_credit_note.credit_note_id} from {self.pms_type} "
            f"for booking {booking_id}"
        )

        if ResellerService().is_reseller(
            hotel_id=hotel.uid, datetime=customer_credit_note.source_created_at
        ):
            logger.info(f"Keeping credit note if hotel is on reseller model")
            CreateOrUpdateCustomerCreditNote(
                customer_credit_note
            ).create_or_update_customer_credit_note()

        else:
            logger.info(f"No operations to execute for updating customer credit notes")

    @staticmethod
    def _get_new_credit_note(booking, customer_credit_note, hotel_credit_note_id):
        logger.info(
            f"Ingesting hotel credit note for booking {booking.booking_id} and "
            f"sell invoice {customer_credit_note.credit_note_id} and "
            f"buy credit note id: {hotel_credit_note_id}"
        )
        current_credit_note = GetHotelCreditNote(hotel_credit_note_id).from_db()
        if current_credit_note.issued_to_type == IssuedToTypes.RESELLER:
            new_credit_note = (
                GetHotelCreditNoteFromCustomerCreditNoteForIssuedToReseller(
                    hotel_credit_note_id,
                    customer_credit_note=customer_credit_note,
                    booking=booking,
                ).get_hotel_credit_note()
            )
        elif current_credit_note.issued_to_type == IssuedToTypes.CUSTOMER:
            new_credit_note = (
                GetHotelCreditNoteFromCustomerCreditNoteForIssuedToCustomer(
                    hotel_credit_note_id,
                    customer_credit_note=customer_credit_note,
                    booking=booking,
                ).get_hotel_credit_note()
            )
        else:
            raise Exception
        return current_credit_note, new_credit_note
