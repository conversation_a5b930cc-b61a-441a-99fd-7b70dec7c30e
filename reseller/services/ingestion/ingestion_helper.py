import logging

from reseller.repository.ingestion_lock_repository import (
    get_ingestion_process_locks_repository,
)

logger = logging.getLogger(__name__)


class IngestionHelper:
    @staticmethod
    def acquire_lock_for_critical_section(resource_type, resource_id):
        if get_ingestion_process_locks_repository().is_lock_present(
            resource_type, resource_id
        ):
            # already locked
            return False
        if get_ingestion_process_locks_repository().acquire_lock(
            resource_type, resource_id
        ):
            # lock obtained successfully
            return True
        return False
