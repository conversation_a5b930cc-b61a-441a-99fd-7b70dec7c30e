import logging

from core.common.constants import PmsType
from core.hotel.services.hotel import GetHotel
from integrations.crs.treebo_crs.booking.get_booking import TreeboCRSGetBooking
from object_registry import register_instance
from reseller.constants import ResourceTypes
from reseller.exceptions import ConcurrentIngestionException
from reseller.repository.ingestion_lock_repository import (
    get_ingestion_process_locks_repository,
)
from reseller.services.booking.get_booking import GetBooking
from reseller.services.booking.ingest_booking import ingest_booking
from reseller.services.ingestion import ingestion_audit
from reseller.services.ingestion.ingestion_helper import IngestionHelper

logger = logging.getLogger(__name__)


@register_instance()
class BookingIngestionService:
    def __init__(self):
        self.pms_type = PmsType.CRS
        self.resource_type = ResourceTypes.BOOKING

    def ingest_booking(self, resource_id):
        try:
            crs_booking = TreeboCRSGetBooking(resource_id).get()
            if not crs_booking:
                raise Exception(
                    f"Booking with given booking_id: {resource_id} doesnt Exist"
                )
            hotel_id = crs_booking.hotel_id
            hotel = GetHotel(hotel_id).get_hotel()

            if IngestionHelper.acquire_lock_for_critical_section(
                self.resource_type, resource_id
            ):
                logger.info(
                    f"Starting ingestion for booking {resource_id}, hotel {hotel_id}, source {self.pms_type}"
                )
                ingest_booking(resource_id, hotel, self.pms_type)
                ingestion_audit.mark_ingestion_success(resource_id)
                get_ingestion_process_locks_repository().remove_lock_if_present(
                    self.resource_type, resource_id
                )
                logger.info(
                    f"Successfully ingested {resource_id}- {hotel_id} - {self.pms_type}"
                )
            else:
                raise ConcurrentIngestionException(self.resource_type, resource_id)
        except Exception as e:
            get_ingestion_process_locks_repository().remove_lock_if_present(
                self.resource_type, resource_id
            )
            raise e

    def ingest_booking_if_not_present(self, booking_id, hotel):
        try:
            GetBooking(
                booking_id=booking_id, hotel=hotel, pms_type=self.pms_type
            ).from_db()
        except Exception:
            self.ingest_booking(booking_id)
