import logging

from object_registry import register_instance
from reseller.constants import ResourceTypes
from reseller.services.ingestion.booking_ingestion import BookingIngestionService
from reseller.services.ingestion.credit_note_ingestion import CreditNoteIngestionService
from reseller.services.ingestion.invoice_ingestion import InvoiceIngestionService

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingIngestionService,
        InvoiceIngestionService,
        CreditNoteIngestionService,
    ]
)
class IngestionService:
    def __init__(
        self,
        booking_ingestion_service: BookingIngestionService,
        invoice_ingestion_service: InvoiceIngestionService,
        credit_note_ingestion_service: CreditNoteIngestionService,
    ):
        self.booking_ingestion_service = booking_ingestion_service
        self.invoice_ingestion_service = invoice_ingestion_service
        self.credit_note_ingestion_service = credit_note_ingestion_service

    def ingest(self, resource_type, resource_id):
        if resource_type == ResourceTypes.BOOKING:
            return self.booking_ingestion_service.ingest_booking(resource_id)
        if resource_type == ResourceTypes.INVOICE:
            return self.invoice_ingestion_service.ingest_invoice(resource_id)
        if resource_type == ResourceTypes.CREDIT_NOTE:
            return self.credit_note_ingestion_service.ingest_credit_note(resource_id)
