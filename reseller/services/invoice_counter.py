import logging
from datetime import datetime

from core.notification import Slack, constants, notify
from reseller.exceptions import GstDoesNotExistInCatalog
from reseller.repository.invoice_sequence import get_invoice_sequence_repository

logger = logging.getLogger(__name__)


def get_latest_invoice_sequence(hotel_id):
    query = {"hotel_id": hotel_id, "active": True}

    try:
        hotel_invoice_sequence = get_invoice_sequence_repository().get(**query)
        if not hotel_invoice_sequence:
            raise Exception(f"Hotel {hotel_id} is not on reseller")
        return hotel_invoice_sequence
    except Exception as e:
        logger.error(
            f"Failed to get latest invoice sequence for hotel {hotel_id} due to: {str(e)}"
        )
        raise


def save_hotel_gstin(hotel_id, gstin):
    if not gstin:
        raise GstDoesNotExistInCatalog(hotel_id)

    if not get_invoice_sequence_repository().exists(hotel_id=hotel_id, gstin=gstin):
        _make_existing_gstin_inactive(hotel_id)
        total_count = get_invoice_sequence_repository().count()
        data = {
            "hotel_seq_id": total_count + 1,
            "invoice_number": 0,
            "credit_note_number": 0,
            "hotel_id": hotel_id,
            "gstin": gstin,
            "active": True,
            "course_start": datetime.utcnow(),
            "course_end": "",
        }

        logger.info(f"Saving GSTIN {gstin} for hotel {hotel_id} in invoice sequence")
        try:
            get_invoice_sequence_repository().create(data)
        except Exception as e:
            message = (
                f"Error occurred while saving GSTIN for hotel {hotel_id}: {str(e)}"
            )
            logger.info(message)
            notify(Slack(constants.Slack.B2B_APP_ALERTS, message))


def _make_existing_gstin_inactive(hotel_id):
    query = {"hotel_id": hotel_id, "active": True}

    update_data = {"active": False, "course_end": datetime.utcnow()}

    get_invoice_sequence_repository().update(
        query=query, dict_or_data_class=update_data, suppress_error=True
    )
