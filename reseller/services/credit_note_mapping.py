from core.invoice_mapping.data_class.hotel_customer_credit_note_mapping import (
    HotelCustomerCreditNoteMapping,
)
from reseller.repository.hotel_customer_credit_note_mapping import (
    get_hotel_customer_credit_note_mapping_repository,
)


class CreditNoteMappingService:
    def __init__(self, hotel, pms_type):
        self.hotel = hotel
        self.pms_type = pms_type

    def get(self, customer_credit_note_id) -> HotelCustomerCreditNoteMapping:
        query = self._make_query(customer_credit_note_id)
        return get_hotel_customer_credit_note_mapping_repository().get(**query)

    def create(
        self,
        customer_credit_note_id,
        customer_credit_note_number,
        hotel_credit_note_number,
        hotel_credit_note_id,
        booking_id,
    ):

        mapping = HotelCustomerCreditNoteMapping(
            hotel_id=self.hotel.uid,
            pms_type=self.pms_type,
            booking_id=booking_id,
            hotel_credit_note_number=hotel_credit_note_number,
            hotel_credit_note_id=hotel_credit_note_id,
            customer_credit_note_id=customer_credit_note_id,
            customer_credit_note_number=customer_credit_note_number,
        )
        get_hotel_customer_credit_note_mapping_repository().create(mapping)

    def update(self, customer_credit_note_number, data_to_be_updated: dict):
        query = self._make_query(customer_credit_note_number)
        mapping = get_hotel_customer_credit_note_mapping_repository().update(
            query, data_to_be_updated
        )
        return mapping

    def _make_query(self, customer_credit_note_id):
        query = {
            "hotel_id": self.hotel.uid,
            "pms_type": self.pms_type,
            "customer_credit_note_id": customer_credit_note_id,
        }
        return query
