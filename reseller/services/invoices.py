import logging
from concurrent.futures import Thread<PERSON>oolExecutor

from ths_common.constants.billing_constants import IssuedToType
from treebo_commons.utils.dateutils import date_range, date_to_ymd_str, ymd_str_to_date

from core.common import file_storage_service
from core.common.constants import EntityType, IssuedToTypes
from core.common.utils.timeit import timeit
from reseller import app
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)
from reseller.services.dtos.invoice_credit_note_dto import (
    CreditNote,
    Invoice,
    InvoicesAndCreditNotes,
)
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.invoices_to_be_sent import InvoiceDispatchService

logger = logging.getLogger(__name__)


class InvoiceAndCreditNoteService:
    def __init__(
        self,
    ):
        pass

    def get_invoices_and_credit_notes(self, hotel_id, parsed_data):
        from_date = parsed_data.get("from_date")
        to_date = (
            parsed_data.get("to_date")
            if parsed_data.get("to_date")
            else parsed_data.get("from_date")
        )
        invoice_number = parsed_data.get("invoice_number")
        entity_type = parsed_data.get("entity_type")
        booking_id = parsed_data.get("booking_id")
        booking_reference_number = parsed_data.get("booking_reference_number")
        issued_to_type = parsed_data.get("issued_to_type")
        if (entity_type or issued_to_type) and not from_date:
            logger.error(
                "Invoice Type/Issued to type should be accompanied by Invoice Date"
            )
            raise Exception(
                "Invoice Type/Issued to type should be accompanied by Invoice Date"
            )

        (
            hotel_invoices,
            hotel_credit_notes,
        ) = self._fetch_hotel_invoices_and_credit_notes(
            hotel_id=hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            entity_type=entity_type,
            booking_reference_number=booking_reference_number,
        )

        if hotel_invoices:
            hotel_invoices = self._enrich_hotel_invoices_data(hotel_invoices)

        hotel_invoices_from_report = self._fetch_hotel_invoices_from_report(
            hotel_id=hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            booking_reference_number=booking_reference_number,
        )
        hotel_invoices.extend(
            [
                hotel_invoice_from_report
                for hotel_invoice_from_report in hotel_invoices_from_report
                if hotel_invoice_from_report.invoice_number
                not in [
                    hotel_invoice.invoice_number for hotel_invoice in hotel_invoices
                ]
            ]
        )

        if hotel_credit_notes:
            hotel_credit_notes = self._enrich_hotel_credit_notes_data(
                hotel_credit_notes
            )

        hotel_credit_notes_from_report = self._fetch_hotel_credit_notes_from_report(
            hotel_id=hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            booking_reference_number=booking_reference_number,
        )
        hotel_credit_notes.extend(
            [
                hotel_credit_note_from_report
                for hotel_credit_note_from_report in hotel_credit_notes_from_report
                if hotel_credit_note_from_report.credit_note_number
                not in [
                    hotel_credit_note.credit_note_number
                    for hotel_credit_note in hotel_credit_notes
                ]
            ]
        )

        logger.info(
            f"A total of {len(hotel_invoices)} Invoices and {len(hotel_credit_notes)} Credit Notes were found "
            f"for {parsed_data}"
        )
        return InvoicesAndCreditNotes(
            invoices=hotel_invoices, credit_notes=hotel_credit_notes
        )

    @timeit
    def get_invoices_and_credit_notes_pdfs(self, hotel_id, parsed_data):
        from_date = parsed_data.get("from_date")
        to_date = (
            parsed_data.get("to_date") if parsed_data.get("to_date") else from_date
        )
        issued_to_type = parsed_data.get("issued_to_type")
        (
            hotel_invoices,
            hotel_credit_notes,
        ) = self._fetch_hotel_invoices_and_credit_notes(
            hotel_id,
            from_date=from_date,
            to_date=to_date,
            issued_to_type=issued_to_type,
        )

        # if not hotel_invoices:
        invoices = self._fetch_hotel_invoices_from_report(
            hotel_id=hotel_id,
            from_date=from_date,
            to_date=to_date,
            issued_to_type=issued_to_type,
        )
        hotel_invoices.extend(
            self._get_hotel_invoices_data(
                [
                    invoice.invoice_number
                    for invoice in invoices
                    if invoice.invoice_number
                    not in [
                        hotel_invoice.invoice_number for hotel_invoice in hotel_invoices
                    ]
                ]
            )
        )

        # if not hotel_credit_notes:
        credit_notes = self._fetch_hotel_credit_notes_from_report(
            hotel_id=hotel_id,
            from_date=from_date,
            to_date=to_date,
            issued_to_type=issued_to_type,
        )
        hotel_credit_notes.extend(
            self._get_hotel_credit_notes_data(
                [
                    credit_note.credit_note_number
                    for credit_note in credit_notes
                    if credit_note.credit_note_number
                    not in [
                        hotel_credit_note for hotel_credit_note in hotel_credit_notes
                    ]
                ]
            )
        )

        archive_signed_url = ""
        if hotel_invoices or hotel_credit_notes:
            archive_signed_url = self._generate_pdf_archive(
                hotel_id,
                ymd_str_to_date(from_date),
                ymd_str_to_date(to_date),
                hotel_invoices,
                hotel_credit_notes,
            )
            logger.info(
                f"Download URL for PDF Archive is: {archive_signed_url} for criteria: {parsed_data}"
            )
        else:
            logger.info(f"No PDF were found for specified criteria: {parsed_data}")
        return archive_signed_url

    def get_pdf_url(self, hotel_id, document_number):
        (
            hotel_invoices,
            hotel_credit_notes,
        ) = self._fetch_hotel_invoices_and_credit_notes(
            hotel_id, invoice_number=document_number
        )

        if hotel_invoices:
            signed_url = self._get_signed_url_for_hotel_invoice(
                hotel_id, hotel_invoices[0]
            )
        elif hotel_credit_notes:
            signed_url = self._get_signed_url_for_hotel_credit_notes(
                hotel_id, hotel_credit_notes[0]
            )
        else:
            msg = f"Document not found: {document_number}"
            logger.info(msg)
            raise Exception(msg)

        if not signed_url:
            msg = f"PDF for Document: {document_number} not found."
            logger.info(msg)
            raise Exception(msg)
        logger.info(f"Signed URL for document: {document_number} is: {signed_url}")

        return signed_url

    @staticmethod
    @timeit
    def _build_query(
        hotel_id,
        from_date=None,
        to_date=None,
        booking_id=None,
        invoice_number=None,
        issued_to_type=None,
        entity_type=None,
        booking_reference_number=None,
    ):
        invoice_query, credit_note_query = dict(), dict()
        invoice_query["hotel_id"] = hotel_id
        credit_note_query["hotel_id"] = hotel_id
        if from_date:
            dates = [
                date_to_ymd_str(date)
                for date in date_range(
                    ymd_str_to_date(from_date),
                    ymd_str_to_date(to_date),
                    end_inclusive=True,
                )
            ]
            if entity_type == EntityType.Invoice:
                invoice_query["invoice_date"] = {"$in": dates}
            elif entity_type == EntityType.CreditNote:
                credit_note_query["credit_note_date"] = {"$in": dates}
            else:
                invoice_query["invoice_date"] = {"$in": dates}
                credit_note_query["credit_note_date"] = {"$in": dates}
        if booking_reference_number:
            invoice_query["order_id"] = booking_reference_number
            credit_note_query["order_id"] = booking_reference_number
        if booking_id:
            invoice_query["booking_id"] = booking_id
            credit_note_query["booking_id"] = booking_id
        if invoice_number:
            invoice_query["invoice_number"] = invoice_number
            credit_note_query["credit_note_number"] = invoice_number
        if issued_to_type:
            invoice_query["issued_to_type"] = issued_to_type
            credit_note_query["issued_to_type"] = issued_to_type
        return invoice_query, credit_note_query

    @staticmethod
    def _enrich_hotel_invoices_data(hotel_invoices):
        hotel_invoice_report_data = (
            get_hotel_invoice_reports_repository().get_invoice_data(
                [hotel_invoice.invoice_number for hotel_invoice in hotel_invoices]
            )
        )
        hotel_invoice_report_data_by_invoice_number = dict()
        for data in hotel_invoice_report_data:
            hotel_invoice_report_data_by_invoice_number[
                data.get("invoice_number")
            ] = data

        enriched_data = []
        for hotel_invoice in hotel_invoices:
            enriched_data.append(
                Invoice.from_hotel_invoice_and_reports(
                    hotel_invoice,
                    hotel_invoice_report_data_by_invoice_number.get(
                        hotel_invoice.invoice_number
                    ),
                )
            )

        return enriched_data

    @staticmethod
    def _enrich_hotel_credit_notes_data(hotel_credit_notes):
        hotel_invoice_report_data = (
            get_hotel_invoice_reports_repository().get_invoice_data(
                [
                    hotel_credit_note.credit_note_number
                    for hotel_credit_note in hotel_credit_notes
                ]
            )
        )
        hotel_invoice_report_data_by_invoice_number = dict()
        for data in hotel_invoice_report_data:
            hotel_invoice_report_data_by_invoice_number[
                data.get("invoice_number")
            ] = data

        enriched_data = []
        for hotel_credit_note in hotel_credit_notes:
            enriched_data.append(
                CreditNote.from_hotel_credit_note_and_reports(
                    hotel_credit_note,
                    hotel_invoice_report_data_by_invoice_number.get(
                        hotel_credit_note.credit_note_number
                    ),
                )
            )

        return enriched_data

    def _enrich_data(self, hotel_invoices, hotel_credit_notes):
        invoices, credit_notes = [], []
        if hotel_invoices:
            invoices = self._enrich_hotel_invoices_data(hotel_invoices)
        if hotel_credit_notes:
            credit_notes = self._enrich_hotel_credit_notes_data(hotel_credit_notes)
        return InvoicesAndCreditNotes(invoices=invoices, credit_notes=credit_notes)

    @timeit
    def _fetch_hotel_invoices_and_credit_notes(
        self,
        hotel_id,
        from_date=None,
        to_date=None,
        booking_id=None,
        invoice_number=None,
        issued_to_type=None,
        entity_type=None,
        booking_reference_number=None,
    ):
        invoice_query, credit_note_query = self._build_query(
            hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            entity_type=entity_type,
            booking_reference_number=booking_reference_number,
        )

        hotel_invoices, hotel_credit_notes = [], []
        if entity_type == EntityType.Invoice:
            hotel_invoices = get_hotel_invoice_repository().find(**invoice_query)
        elif entity_type == EntityType.CreditNote:
            hotel_credit_notes = get_hotel_credit_note_repository().find(
                **credit_note_query
            )
        else:
            hotel_invoices = get_hotel_invoice_repository().find(**invoice_query)
            hotel_credit_notes = get_hotel_credit_note_repository().find(
                **credit_note_query
            )
        return hotel_invoices, hotel_credit_notes

    @timeit
    def _fetch_hotel_invoices_from_report(
        self,
        hotel_id,
        from_date=None,
        to_date=None,
        booking_id=None,
        invoice_number=None,
        issued_to_type=None,
        booking_reference_number=None,
    ):
        invoice_query, _ = self._build_query(
            hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            entity_type=EntityType.Invoice,
        )

        invoice_query["entity_type"] = EntityType.Invoice
        if booking_reference_number:
            invoice_query["reference_number"] = booking_reference_number
        hotel_invoice_reports_data = get_hotel_invoice_reports_repository().find(
            **invoice_query
        )
        invoices = dict()
        if hotel_invoice_reports_data:
            for record in hotel_invoice_reports_data:
                invoices[record.invoice_number] = Invoice.from_hotel_invoice_reports(
                    record
                )

        return list(invoices.values()) if invoices else []

    @timeit
    def _fetch_hotel_credit_notes_from_report(
        self,
        hotel_id,
        from_date=None,
        to_date=None,
        booking_id=None,
        invoice_number=None,
        issued_to_type=None,
        booking_reference_number=None,
    ):
        invoice_query, _ = self._build_query(
            hotel_id,
            from_date=from_date,
            to_date=to_date,
            booking_id=booking_id,
            invoice_number=invoice_number,
            issued_to_type=issued_to_type,
            entity_type=EntityType.Invoice,
        )

        invoice_query["entity_type"] = EntityType.CreditNote
        if booking_reference_number:
            invoice_query["reference_number"] = booking_reference_number
        hotel_invoice_reports_data = get_hotel_invoice_reports_repository().find(
            **invoice_query
        )
        credit_notes = dict()
        if hotel_invoice_reports_data:
            for record in hotel_invoice_reports_data:
                credit_notes[
                    record.invoice_number
                ] = CreditNote.from_hotel_invoice_reports(record)

        return list(credit_notes.values()) if credit_notes else []

    @timeit
    def _generate_pdf_archive(
        self, hotel_id, from_date, to_date, invoices, credit_notes
    ):
        invoice_dispatch_service = InvoiceDispatchService()
        [
            invoice_dispatch_service.setup_invoices_storage_dir(sub_directory=date)
            for date in set(
                [invoice.invoice_date for invoice in invoices]
                + [credit_note.credit_note_date for credit_note in credit_notes]
            )
        ]

        if [invoice for invoice in invoices if invoice.invoice_url] + [
            credit_note for credit_note in credit_notes if credit_note.credit_note_url
        ]:

            with ThreadPoolExecutor(
                max_workers=int(app.config["MAX_THREAD_FOR_PDF_GENERATION"])
            ) as executor:
                executor.map(
                    invoice_dispatch_service.download_pdf,
                    [
                        dict(
                            file_url=invoice.invoice_url,
                            file_name=invoice.invoice_number,
                            file_date=invoice.invoice_date,
                            is_private_s3_url=invoice.issued_to_type
                            != IssuedToType.CUSTOMER.value,
                        )
                        for invoice in invoices
                        if invoice.invoice_url
                    ]
                    + [
                        dict(
                            file_url=credit_note.credit_note_url,
                            file_name=credit_note.credit_note_number,
                            file_date=credit_note.credit_note_date,
                            is_private_s3_url=credit_note.issued_to_type
                            != IssuedToType.CUSTOMER.value,
                        )
                        for credit_note in credit_notes
                        if credit_note.credit_note_url
                    ],
                )

        if [invoice for invoice in invoices if not invoice.invoice_url] + [
            credit_note
            for credit_note in credit_notes
            if not credit_note.credit_note_url
        ]:
            with ThreadPoolExecutor(
                max_workers=int(app.config["MAX_THREAD_FOR_PDF_GENERATION"])
            ) as executor1:
                executor1.map(
                    self._invoke_copy_hotel_credit_note_to_temp_dir,
                    [
                        dict(
                            entity=invoice,
                            hotel_id=hotel_id,
                            entity_type=EntityType.Invoice,
                            service=invoice_dispatch_service,
                        )
                        for invoice in invoices
                        if not invoice.invoice_url
                    ]
                    + [
                        dict(
                            entity=credit_note,
                            hotel_id=hotel_id,
                            entity_type=EntityType.CreditNote,
                            service=invoice_dispatch_service,
                        )
                        for credit_note in credit_notes
                        if not credit_note.credit_note_url
                    ],
                )

        pdf_archive_url = invoice_dispatch_service.upload_hotel_invoices_and_credit_notes_for_date_range_to_file_storage(
            hotel_id, from_date, to_date
        )
        invoice_dispatch_service.teardown_invoices_storage_dir()

        return pdf_archive_url

    @staticmethod
    def _get_hotel_invoices_data(invoice_numbers):
        query = {"invoice_number": {"$in": invoice_numbers}}
        return get_hotel_invoice_repository().find(**query)

    @staticmethod
    @timeit
    def _get_hotel_credit_notes_data(credit_note_numbers):
        query = {"credit_note_number": {"$in": credit_note_numbers}}
        return get_hotel_credit_note_repository().find(**query)

    @staticmethod
    def _invoke_copy_hotel_credit_note_to_temp_dir(request_meta):
        entity = request_meta.get("entity")
        hotel_id = request_meta.get("hotel_id")
        entity_type = request_meta.get("entity_type")
        invoice_dispatch_service = request_meta.get("service")
        failed_invoices = []
        if entity_type == EntityType.Invoice:
            invoice_dispatch_service.copy_hotel_invoices_to_temp_dir(
                hotel_invoices=[entity],
                hotel_id=hotel_id,
                failed_invoices=failed_invoices,
                group_by_date=True,
            )
        if entity_type == EntityType.CreditNote:
            invoice_dispatch_service.copy_hotel_credit_note_to_temp_dir(
                hotel_credit_notes=[entity],
                hotel_id=hotel_id,
                failed_invoices=failed_invoices,
                group_by_date=True,
            )

    @staticmethod
    def _get_signed_url_for_hotel_invoice(hotel_id, invoice):
        signed_url = None

        if invoice.issued_to_type == IssuedToTypes.CUSTOMER:
            signed_url = GetHotelInvoice(
                invoice_id=invoice.invoice_id
            ).get_pms_invoice_signed_url(invoice)
        else:
            if not invoice.invoice_url:
                invoice_dispatch_service = InvoiceDispatchService()
                invoice_dispatch_service.setup_invoices_storage_dir()
                invoice_url_map = (
                    invoice_dispatch_service.copy_hotel_invoices_to_temp_dir(
                        hotel_invoices=[invoice], hotel_id=hotel_id, failed_invoices=[]
                    )
                )
                invoice_dispatch_service.teardown_invoices_storage_dir()
                if invoice_url_map.items():
                    signed_url = file_storage_service.sign_url(
                        next(iter(invoice_url_map.values()))
                    )
            else:
                signed_url = file_storage_service.sign_url(invoice.invoice_url)

        return signed_url

    @staticmethod
    def _get_signed_url_for_hotel_credit_notes(hotel_id, credit_note):
        signed_url = None

        if credit_note.issued_to_type == IssuedToTypes.CUSTOMER:
            signed_url = GetHotelCreditNote(
                credit_note_id=credit_note.credit_note_id
            ).get_pms_credit_note_signed_url(credit_note)
        else:
            if not credit_note.credit_note_url:
                invoice_dispatch_service = InvoiceDispatchService()
                invoice_dispatch_service.setup_invoices_storage_dir()
                credit_note_url_map = (
                    invoice_dispatch_service.copy_hotel_credit_note_to_temp_dir(
                        hotel_credit_notes=[credit_note],
                        hotel_id=hotel_id,
                        failed_invoices=[],
                    )
                )
                invoice_dispatch_service.teardown_invoices_storage_dir()
                if credit_note_url_map.items():
                    signed_url = file_storage_service.sign_url(
                        next(iter(credit_note_url_map.values()))
                    )
            else:
                signed_url = file_storage_service.sign_url(credit_note.credit_note_url)

        return signed_url
