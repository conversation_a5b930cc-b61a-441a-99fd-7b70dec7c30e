# pylint: disable=too-many-locals

import itertools
import logging
import os
import shutil
import uuid
from collections import defaultdict

import requests
from treebo_commons.utils.dateutils import date_to_ymd_str

from core.common import file_storage_service
from core.common.constants import CreditNoteStatus, InvoiceStatus
from core.common.utils.date import utc_to_ist
from core.common.utils.timeit import timeit
from core.hotel.exceptions import NoOwnerEmailsFound
from core.hotel.services.hotel import GetHotel
from core.hotel.services.hotel_owner import GetHotelOwner
from core.notification import notify
from core.notification.handlers.invoices.invoice_dispatch_failure import (
    InvoiceDispatchFailureHandler,
)
from core.notification.handlers.invoices.invoice_dispatcher import (
    InvoiceDispactherHandler,
)
from reseller import app
from reseller.constants import IngestionTypes, IssuedTo
from reseller.repository.hotel_credit_note import get_hotel_credit_note_repository
from reseller.repository.hotel_detail import get_hotel_detail_repository
from reseller.repository.hotel_invoice import get_hotel_invoice_repository
from reseller.repository.invoices_to_be_sent import get_invoices_to_be_sent_repository
from reseller.services.hotel_invoice import (
    get_hotel_credit_note_html,
    get_hotel_invoice_html,
)
from reseller.services.hotel_invoice.get_hotel_credit_note import GetHotelCreditNote
from reseller.services.hotel_invoice.get_hotel_invoice import GetHotelInvoice
from reseller.services.hotel_invoice.send_hotel_invoice_report import _get_owner_emails
from reseller.services.invoice_report_failures_audit import (
    record_invoice_report_failure,
)

TEMP_ROOT_DIR = "/tmp"
TEMP_INVOICES_STORAGE_DIR = f"{TEMP_ROOT_DIR}/invoices_for_today"

logger = logging.getLogger(__name__)


def save_invoices_to_be_sent(hotel_invoice, entity_type):
    query = dict(
        hotel_id=hotel_invoice.hotel_id,
        pms_type=hotel_invoice.pms_type,
        invoice_id=hotel_invoice.invoice_id,
        invoice_number=hotel_invoice.invoice_number,
        dispatch_date=utc_to_ist(hotel_invoice.created_at).date().strftime("%Y-%m-%d"),
        entity_type=entity_type,
    )
    get_invoices_to_be_sent_repository().update(
        query=query, dict_or_data_class=query, upsert=True
    )


def save_hotel_credit_note_to_be_sent(hotel_credit_note, entity_type):
    query = dict(
        hotel_id=hotel_credit_note.hotel_id,
        pms_type=hotel_credit_note.pms_type,
        invoice_id=hotel_credit_note.credit_note_id,
        invoice_number=hotel_credit_note.credit_note_number,
        dispatch_date=utc_to_ist(hotel_credit_note.created_at)
        .date()
        .strftime("%Y-%m-%d"),
        entity_type=entity_type,
    )
    get_invoices_to_be_sent_repository().update(
        query=query, dict_or_data_class=query, upsert=True
    )


def upload_hotel_invoices_to_file_storage(hotel_id, today):
    """
    :param today: date to create invoice zip
    :param hotel_id: hotel id
    :return: Downloadable url for the file storage to download the file
    """
    archive_file_name = f"hotel_invoices/{hotel_id}/{today}/{uuid.uuid4()}"
    shutil.make_archive(
        f"{TEMP_ROOT_DIR}/{archive_file_name}", "zip", TEMP_INVOICES_STORAGE_DIR
    )
    file_storage_service.upload_file(
        f"{TEMP_ROOT_DIR}/{archive_file_name}.zip", f"{archive_file_name}.zip"
    )
    download_url = file_storage_service.get_signed_url(
        filepath=f"{archive_file_name}.zip"
    )
    return download_url


def _setup_invoices_storage_dir():
    if os.path.exists(TEMP_INVOICES_STORAGE_DIR):
        shutil.rmtree(TEMP_INVOICES_STORAGE_DIR)
    os.makedirs(TEMP_INVOICES_STORAGE_DIR)


def _teardown_invoices_storage_dir():
    try:
        shutil.rmtree(TEMP_INVOICES_STORAGE_DIR)
    except FileNotFoundError:
        logger.info(f"No temp storage directory found")


def fetch_invoices_to_be_sent(date, hotel_ids):
    invoices_to_be_sent_for_hotels = []
    for hotel_id in hotel_ids:
        invoices_to_be_sent_for_hotels.extend(
            get_invoices_to_be_sent_repository().find(
                dispatch_date=date, hotel_id=hotel_id
            )
        )
    if not hotel_ids:
        invoices_to_be_sent_for_hotels = get_invoices_to_be_sent_repository().find(
            dispatch_date=date
        )

    return invoices_to_be_sent_for_hotels


class InvoiceDispatchService:
    def __init__(self):
        self.temp_invoices_storage_dir = f"{TEMP_INVOICES_STORAGE_DIR}/{uuid.uuid4()}"

    def send_invoices_old(self, date, hotel_ids):
        failed_hotel_ids = set()

        invoices_to_be_sent_for_hotels = fetch_invoices_to_be_sent(date, hotel_ids)

        hotel_to_invoice_mapping = defaultdict(list)

        for invoice_to_be_sent in invoices_to_be_sent_for_hotels:
            hotel_to_invoice_mapping[invoice_to_be_sent.hotel_id].append(
                {
                    "invoice_id": invoice_to_be_sent.invoice_id,
                    "invoice_type": invoice_to_be_sent.entity_type,
                }
            )
        for hotel_id, invoice_mappings in hotel_to_invoice_mapping.items():
            self.setup_invoices_storage_dir()
            failed_invoices = []
            hotel_invoice_ids = []
            hotel_credit_note_ids = []
            update_invoice_dir_link = []
            try:
                for invoice_mapping in invoice_mappings:
                    if invoice_mapping["invoice_type"] == IngestionTypes.HOTEL_INVOICE:
                        hotel_invoice_ids.append(invoice_mapping["invoice_id"])
                    elif invoice_mapping["invoice_type"] == IngestionTypes.CREDIT_NOTE:
                        hotel_credit_note_ids.append(invoice_mapping["invoice_id"])
                    update_invoice_dir_link.append(invoice_mapping["invoice_id"])

                hotel_invoices = get_hotel_invoice_repository().find(
                    **{"invoice_id": {"$in": hotel_invoice_ids}}
                )
                hotel_credit_notes = get_hotel_credit_note_repository().find(
                    **{"credit_note_id": {"$in": hotel_credit_note_ids}}
                )
                invoices = ",".join(
                    [invoice.invoice_number for invoice in hotel_invoices]
                )
                hotel_credit_note_numbers = ",".join(
                    [invoice.credit_note_number for invoice in hotel_credit_notes]
                )
                hotel = GetHotel(hotel_id).get_hotel()
                self.copy_hotel_invoices_to_temp_dir(
                    hotel_invoices, hotel_id, failed_invoices
                )
                self.copy_hotel_credit_note_to_temp_dir(
                    hotel_credit_notes, hotel_id, failed_invoices
                )
                downloadable_url = self._upload_hotel_invoices_to_file_storage(
                    hotel_id, date
                )
                n_modified = get_invoices_to_be_sent_repository().update_many(
                    {"invoice_id": {"$in": update_invoice_dir_link}},
                    {"storage_path": downloadable_url},
                )
                logger.info(
                    f"Updated storage path to {downloadable_url} for invoices: {invoices}, and "
                    f"credit_notes {hotel_credit_note_numbers} count: {n_modified}"
                )

                try:
                    hotel_owner_details = GetHotelOwner.get_hotel_owner(hotel_id)
                    hotel_owner_emails = [
                        hotel_owner.email_id
                        for hotel_owner in hotel_owner_details
                        if hotel_owner.email_id
                    ]
                except Exception:
                    msg = f"No hotel owner emails found for hotel: {hotel_id}"
                    logger.error(msg)
                    failure_handler = InvoiceDispatchFailureHandler(message=msg)
                    notify(failure_handler.slack())
                    continue
                if not hotel_owner_emails:
                    logger.error(msg=f"Empty email received for hotel : {hotel_id}")
                    raise NoOwnerEmailsFound

                handler = InvoiceDispactherHandler(
                    downloadable_url, hotel.name, hotel_owner_emails, date=date
                )
                notify(handler.email())
                logger.info(
                    f"Sent invoices for hotel id : {hotel_id} for date : {date}"
                )
                if failed_invoices:
                    msg = f"Failed to send Invoices for {failed_invoices}"
                    failed_hotel_ids.add(hotel_id)
                    failure_handler = InvoiceDispatchFailureHandler(message=msg)
                    notify(failure_handler.slack())
            except Exception as e:
                msg = f"Failed to send Invoices for hotel id {hotel_id} with exception {e}"
                failed_hotel_ids.add(hotel_id)
                logger.exception(msg)
                failure_handler = InvoiceDispatchFailureHandler(message=msg)
                notify(failure_handler.slack())
        self.teardown_invoices_storage_dir()
        data = {
            "passed_hotel_ids": [
                hotel_id
                for hotel_id in hotel_ids
                if hotel_id not in list(failed_hotel_ids)
            ],
            "failed_hotel_ids": list(failed_hotel_ids),
        }
        return data

    def _upload_hotel_invoices_to_file_storage(self, hotel_id, today):
        """
        :param today: date to create invoice zip
        :param hotel_id: hotel id
        :return: Downloadable url for the file storage to download the file
        """
        archive_file_name = f"hotel_invoices/{hotel_id}/{today}/{uuid.uuid4()}"
        shutil.make_archive(
            f"{TEMP_ROOT_DIR}/{archive_file_name}",
            "zip",
            self.temp_invoices_storage_dir,
        )
        file_storage_service.upload_file(
            f"{TEMP_ROOT_DIR}/{archive_file_name}.zip", f"{archive_file_name}.zip"
        )
        download_url = file_storage_service.get_signed_url(
            filepath=f"{archive_file_name}.zip"
        )
        return download_url

    def setup_invoices_storage_dir(self, sub_directory=None):
        if not sub_directory:
            if os.path.exists(self.temp_invoices_storage_dir):
                shutil.rmtree(self.temp_invoices_storage_dir)
            os.makedirs(self.temp_invoices_storage_dir)
        else:
            if os.path.exists(f"{self.temp_invoices_storage_dir}/{sub_directory}"):
                shutil.rmtree(f"{self.temp_invoices_storage_dir}/{sub_directory}")
            os.makedirs(f"{self.temp_invoices_storage_dir}/{sub_directory}")

    def teardown_invoices_storage_dir(self):
        try:
            shutil.rmtree(self.temp_invoices_storage_dir)
        except FileNotFoundError:
            logger.info(f"No temp storage directory found")

    @staticmethod
    def _upload_pdf(local_file_path, file_name, target_path):
        file_storage_service.upload_file(
            f"{local_file_path}/{file_name}.pdf", f"{target_path}/{file_name}.pdf"
        )
        download_url = file_storage_service.get_signed_url(
            filepath=f"{target_path}/{file_name}.pdf"
        )
        return download_url

    @timeit
    def copy_hotel_invoices_to_temp_dir(
        self, hotel_invoices, hotel_id, failed_invoices, group_by_date=False
    ):
        invoice_url_map = dict()
        for invoice in hotel_invoices:
            try:
                if invoice.status not in [InvoiceStatus.LOCKED]:
                    continue
                file_size = 0
                retry_count = -1
                download_url = ""
                while file_size < int(
                    app.config["THRESHOLD_PDF_SIZE"]
                ) and retry_count < int(
                    app.config["MAX_RETRY_COUNT_FOR_PDF_GENERATION"]
                ):
                    retry_count += 1
                    if invoice.issued_to_type == IssuedTo.CUSTOMER.value:
                        if not invoice.invoice_url:
                            download_url = GetHotelInvoice(
                                invoice_id=invoice.invoice_id
                            ).get_pms_invoice_url(invoice)
                        else:
                            download_url = invoice.invoice_url
                        html_content = requests.get(download_url).content
                        if group_by_date:
                            with open(
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_date}/{invoice.invoice_number}.pdf",
                                "wb",
                            ) as file:
                                file_size = file.write(html_content)
                        else:
                            with open(
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_number}.pdf",
                                "wb",
                            ) as file:
                                file_size = file.write(html_content)
                    else:
                        pdf_file = get_hotel_invoice_html.as_pdf(invoice.invoice_number)
                        if group_by_date:
                            shutil.copy(
                                pdf_file.name,
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_date}/{invoice.invoice_number}.pdf",
                            )
                            file_size = os.path.getsize(
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_date}/{invoice.invoice_number}.pdf"
                            )
                        else:
                            shutil.copy(
                                pdf_file.name,
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_number}.pdf",
                            )
                            file_size = os.path.getsize(
                                f"{self.temp_invoices_storage_dir}/{invoice.invoice_number}.pdf"
                            )
                        if file_size > int(app.config["THRESHOLD_PDF_SIZE"]):
                            download_url = self._upload_pdf(
                                local_file_path=self.temp_invoices_storage_dir
                                if not group_by_date
                                else f"{self.temp_invoices_storage_dir}/{invoice.invoice_date}",
                                file_name=invoice.invoice_number,
                                target_path=f'{app.config["RESELLER_INVOICE_PATH"]}/{hotel_id}/{invoice.invoice_date}',
                            )
                if file_size < int(app.config["THRESHOLD_PDF_SIZE"]):
                    logger.info(
                        f"Invoice: {invoice.invoice_number}, Size: {file_size} is less than threshold."
                        f"Removing file."
                    )
                    os.remove(
                        f"{self.temp_invoices_storage_dir}/{invoice.invoice_number}.pdf"
                    )
                else:
                    get_hotel_invoice_repository().update(
                        {"invoice_id": invoice.invoice_id},
                        {"invoice_url": download_url},
                    )
                    invoice_url_map[invoice.invoice_number] = download_url
            except Exception as e:
                logger.exception(
                    f"Failed to send Invoices for hotel id {hotel_id} "
                    f"and invoice id {invoice.invoice_number} with execption: {e}"
                )
                failed_invoices.append(
                    {
                        "hotel_id": hotel_id,
                        "invoice_id": invoice.invoice_number,
                        "exc": str(e),
                    }
                )
        return invoice_url_map

    @timeit
    def copy_hotel_credit_note_to_temp_dir(
        self, hotel_credit_notes, hotel_id, failed_invoices, group_by_date=False
    ):
        credit_note_url_map = dict()
        for hotel_credit_note in hotel_credit_notes:
            try:
                if hotel_credit_note.status != CreditNoteStatus.LOCKED:
                    continue
                file_size = 0
                retry_count = -1
                download_url = ""
                while file_size < int(
                    app.config["THRESHOLD_PDF_SIZE"]
                ) and retry_count < int(
                    app.config["MAX_RETRY_COUNT_FOR_PDF_GENERATION"]
                ):
                    retry_count += 1
                    if hotel_credit_note.issued_to_type == IssuedTo.CUSTOMER.value:
                        if not hotel_credit_note.credit_note_url:
                            download_url = GetHotelCreditNote(
                                credit_note_id=hotel_credit_note.credit_note_id
                            ).get_pms_credit_note_url(hotel_credit_note)
                        else:
                            download_url = hotel_credit_note.credit_note_url
                        html_content = requests.get(download_url).content
                        logger.info(
                            f"Html content for customer credit note {hotel_credit_note.credit_note_id} from URL: "
                            f"{download_url} is: {html_content}"
                        )
                        if group_by_date:
                            with open(
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_date}/{hotel_credit_note.credit_note_number}.pdf",
                                "wb",
                            ) as file:
                                file_size = file.write(html_content)
                        else:
                            with open(
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_number}.pdf",
                                "wb",
                            ) as file:
                                file_size = file.write(html_content)
                    else:
                        pdf_file = get_hotel_credit_note_html.as_pdf(
                            hotel_credit_note.credit_note_number, hotel_id
                        )
                        if group_by_date:
                            shutil.copy(
                                pdf_file.name,
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_date}/{hotel_credit_note.credit_note_number}.pdf",
                            )
                            file_size = os.path.getsize(
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_date}/{hotel_credit_note.credit_note_number}.pdf"
                            )
                        else:
                            shutil.copy(
                                pdf_file.name,
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_number}.pdf",
                            )
                            file_size = os.path.getsize(
                                f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_number}.pdf"
                            )
                        if file_size > int(app.config["THRESHOLD_PDF_SIZE"]):
                            download_url = self._upload_pdf(
                                local_file_path=self.temp_invoices_storage_dir
                                if not group_by_date
                                else f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_date}",
                                file_name=hotel_credit_note.credit_note_number,
                                target_path=f'/{app.config["RESELLER_INVOICE_PATH"]}/{hotel_id}/{hotel_credit_note.credit_note_date}',
                            )
                if file_size < int(app.config["THRESHOLD_PDF_SIZE"]):
                    logger.info(
                        f"Credit Note: {hotel_credit_note.credit_note_number}, Size: {file_size} is less than "
                        f"threshold. Removing file."
                    )

                    os.remove(
                        f"{self.temp_invoices_storage_dir}/{hotel_credit_note.credit_note_number}.pdf"
                    )
                else:
                    get_hotel_credit_note_repository().update(
                        {"credit_note_number": hotel_credit_note.credit_note_number},
                        {"credit_note_url": download_url},
                    )
                    credit_note_url_map[
                        hotel_credit_note.credit_note_number
                    ] = download_url
            except Exception as e:
                logger.exception(
                    f"Failed to send Invoices for hotel id {hotel_id} "
                    f"and invoice id {hotel_credit_note.credit_note_number}"
                )
                failed_invoices.append(
                    {
                        "hotel_id": hotel_id,
                        "invoice_id": hotel_credit_note.credit_note_number,
                        "exc": str(e),
                    }
                )
        return credit_note_url_map

    def send_invoices(self, date, hotel_ids):
        failed_hotel_ids = set()

        invoices_to_be_sent_for_hotels = fetch_invoices_to_be_sent(date, hotel_ids)

        hotel_to_invoice_mapping = defaultdict(list)
        hotel_invoice_mapping = defaultdict(list)
        hotel_credit_note_mapping = defaultdict(list)

        for invoice_to_be_sent in invoices_to_be_sent_for_hotels:
            hotel_to_invoice_mapping[invoice_to_be_sent.hotel_id].append(
                {
                    "invoice_id": invoice_to_be_sent.invoice_id,
                    "invoice_type": invoice_to_be_sent.entity_type,
                }
            )
            if invoice_to_be_sent.entity_type == IngestionTypes.HOTEL_INVOICE:
                hotel_invoice_mapping.setdefault(
                    invoice_to_be_sent.hotel_id, []
                ).append(invoice_to_be_sent.invoice_id)
            elif invoice_to_be_sent.entity_type == IngestionTypes.CREDIT_NOTE:
                hotel_credit_note_mapping.setdefault(
                    invoice_to_be_sent.hotel_id, []
                ).append(invoice_to_be_sent.invoice_id)

        hotel_invoices = get_hotel_invoice_repository().find(
            **{
                "invoice_id": {
                    "$in": list(
                        itertools.chain.from_iterable(
                            list(hotel_invoice_mapping.values())
                        )
                    )
                }
            }
        )
        hotel_invoices_by_hotel_map = defaultdict(list)
        for hotel_invoice in hotel_invoices:
            hotel_invoices_by_hotel_map.setdefault(hotel_invoice.hotel_id, []).append(
                hotel_invoice
            )

        hotel_credit_notes = get_hotel_credit_note_repository().find(
            **{
                "credit_note_id": {
                    "$in": list(
                        itertools.chain.from_iterable(
                            list(hotel_credit_note_mapping.values())
                        )
                    )
                }
            }
        )
        hotel_credit_notes_by_hotel_map = defaultdict(list)
        for hotel_credit_note in hotel_credit_notes:
            hotel_credit_notes_by_hotel_map.setdefault(
                hotel_credit_note.hotel_id, []
            ).append(hotel_credit_note)

        hotel_details = (
            get_hotel_detail_repository().get_multiple_hotel_detail_by_hotel_id(
                list(hotel_to_invoice_mapping.keys())
            )
        )
        hotel_detail_map = dict()
        for hotel_detail in hotel_details:
            hotel_detail_map[hotel_detail.hotel_id] = hotel_detail

        error_details = []
        for hotel_id in hotel_to_invoice_mapping.keys():
            try:
                self.setup_invoices_storage_dir()
                failed_invoices = []
                update_invoice_dir_link = []

                update_invoice_dir_link.extend(
                    [
                        invoice.get("invoice_id")
                        for invoice in hotel_to_invoice_mapping.get(hotel_id)
                    ]
                )
                invoices = ""
                hotel_credit_note_numbers = ""
                if hotel_invoices_by_hotel_map.get(hotel_id):
                    invoices = ",".join(
                        [
                            invoice.invoice_number
                            for invoice in hotel_invoices_by_hotel_map.get(hotel_id)
                        ]
                    )
                    self.copy_hotel_invoices_to_temp_dir(
                        hotel_invoices_by_hotel_map.get(hotel_id),
                        hotel_id,
                        failed_invoices,
                    )

                if hotel_credit_notes_by_hotel_map.get(hotel_id):
                    hotel_credit_note_numbers = ",".join(
                        [
                            invoice.credit_note_number
                            for invoice in hotel_credit_notes_by_hotel_map.get(hotel_id)
                        ]
                    )
                    self.copy_hotel_credit_note_to_temp_dir(
                        hotel_credit_notes_by_hotel_map.get(hotel_id),
                        hotel_id,
                        failed_invoices,
                    )

                downloadable_url = self._upload_hotel_invoices_to_file_storage(
                    hotel_id, date
                )

                n_modified = get_invoices_to_be_sent_repository().update_many(
                    {"invoice_id": {"$in": update_invoice_dir_link}},
                    {"storage_path": downloadable_url},
                )
                logger.info(
                    f"Updated storage path to {downloadable_url} for invoices: {invoices}, and "
                    f"credit_notes {hotel_credit_note_numbers} count: {n_modified}"
                )

                email_ids = []
                if (
                    hotel_detail_map.get(hotel_id)
                    and hotel_detail_map.get(hotel_id).hotel_owners
                    and hotel_detail_map.get(hotel_id).hotel_owners[0].email
                ):
                    email_ids = [
                        ho.email
                        for ho in hotel_detail_map.get(hotel_id).hotel_owners
                        if ho.email
                    ]
                else:
                    try:
                        email_ids = _get_owner_emails(cs_id=hotel_id)
                        hotel_detail_map[
                            hotel_id
                        ] = get_hotel_detail_repository().get_hotel_detail_by_hotel_id(
                            hotel_id
                        )
                    except Exception as e:
                        msg = f"Exception occured while fetching hotel owner email for Hotel_Id: {hotel_id}"
                        logger.error(msg)
                        error_details.append(
                            dict(
                                hotel_id=hotel_id,
                                error_message=msg,
                                error_trace=e,
                            )
                        )
                if email_ids:
                    handler = InvoiceDispactherHandler(
                        downloadable_url,
                        hotel_detail_map.get(hotel_id).hotel_name,
                        email_ids,
                        date=date,
                    )
                    notify(handler.email())
                else:
                    msg = f"No hotel owner emails found for hotel: {hotel_id}"
                    logger.error(msg)
                    error_details.append(
                        dict(
                            hotel_id=hotel_id,
                            error_message=msg,
                            error_trace=msg,
                        )
                    )
                    failure_handler = InvoiceDispatchFailureHandler(message=msg)
                    notify(failure_handler.slack())
                    record_invoice_report_failure(
                        process_name="send_invoices",
                        hotel_id=hotel_id,
                        invoice_id=invoices + "," + hotel_credit_note_numbers,
                        error_message=msg,
                    )
                    continue

                logger.info(
                    f"Sent invoices for hotel id : {hotel_id} for date : {date}"
                )

                if failed_invoices:
                    msg = f"Failed to send Invoices for {failed_invoices}"
                    failed_hotel_ids.add(hotel_id)
                    failure_handler = InvoiceDispatchFailureHandler(message=msg)
                    notify(failure_handler.slack())
            except Exception as e:
                msg = f"Failed to send Invoices for hotel id {hotel_id} with exception {e}"
                failed_hotel_ids.add(hotel_id)
                logger.exception(msg)
                failure_handler = InvoiceDispatchFailureHandler(message=msg)
                record_invoice_report_failure(
                    process_name="send_invoices",
                    hotel_id=hotel_id,
                    error_message=msg,
                    error_trace=e,
                )
                notify(failure_handler.slack())

        self.teardown_invoices_storage_dir()
        data = {
            "passed_hotel_ids": [
                hotel_id
                for hotel_id in hotel_ids
                if hotel_id not in list(failed_hotel_ids)
            ],
            "failed_hotel_ids": list(failed_hotel_ids),
        }
        return data

    @timeit
    def upload_hotel_invoices_and_credit_notes_for_date_range_to_file_storage(
        self, hotel_id, from_date, to_date
    ):
        range_str = (
            date_to_ymd_str(from_date).replace("-", "")
            + "_"
            + date_to_ymd_str(to_date).replace("-", "")
        )
        archive_file_name = f"hotel_invoices/{hotel_id}/bulk/{range_str}/{uuid.uuid4()}"

        shutil.make_archive(
            f"{TEMP_ROOT_DIR}/{archive_file_name}",
            "zip",
            self.temp_invoices_storage_dir,
        )

        file_storage_service.upload_file(
            f"{TEMP_ROOT_DIR}/{archive_file_name}.zip", f"{archive_file_name}.zip"
        )

        download_url = file_storage_service.get_signed_url(
            filepath=f"{archive_file_name}.zip",
            expires_in=int(app.config["SIGNED_URL_EXPIRY_DURATION"]),
        )
        return download_url

    @timeit
    def download_pdf(self, file_meta):
        retry_count = -1
        file_size = 0
        download_url = file_meta.get("file_url")
        is_private_s3_url = file_meta.get("is_private_s3_url", False)
        if is_private_s3_url:
            download_url = file_storage_service.sign_url(download_url)
        file_date = file_meta.get("file_date")
        if not file_meta.get("file_name"):
            file_name = download_url.split("/")[-1]
        else:
            file_name = file_meta.get("file_name") + ".pdf"

        while file_size < int(app.config["THRESHOLD_PDF_SIZE"]) and retry_count < int(
            app.config["MAX_RETRY_COUNT_FOR_PDF_GENERATION"]
        ):
            retry_count += 1
            html_content = requests.get(download_url).content

            if file_date:
                with open(
                    f"{self.temp_invoices_storage_dir}/{file_date}/{file_name}", "wb"
                ) as file:
                    file_size = file.write(html_content)
            else:
                with open(
                    f"{self.temp_invoices_storage_dir}/{file_name}", "wb"
                ) as file:
                    file_size = file.write(html_content)

        if file_size < int(app.config["MAX_RETRY_COUNT_FOR_PDF_GENERATION"]):
            logger.info(f"{file_name} was not generated")
