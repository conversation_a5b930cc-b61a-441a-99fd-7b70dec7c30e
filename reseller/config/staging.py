# pylint: disable=invalid-name
from reseller import Config


class StagingConf(Config):
    DEBUG = True
    ENV = "staging"
    LOG_ROOT = "/ebs1/logs"
    DEVELOPERS_EMAILS = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    AUTH = {
        "REDIRECT_URI": "https://reseller.treebo.be/callback",
        "AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
        "TOKEN_URI": "https://accounts.google.com/o/oauth2/token",
        "USER_INFO": "https://www.googleapis.com/userinfo/v2/me",
        "SCOPE": ["profile", "email"],
    }
    ALL_E_TECH_ENDPOINT_URL = "https://navtest.treebohotels.com"
    NAVISION_ERROR_REPORT_RECEIVER_LIST = ["<EMAIL>"]
    import os

    os.environ["THSC_ENVIRONMENT"] = "staging"
