# pylint: disable=invalid-name
import os

from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id


class Config:
    ENV = "local"
    DEBUG = True
    TENANT_ID = get_current_tenant_id() or TenantClient.get_default_tenant()
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    LOG_ROOT = f"{root_dir}/logs"
    LOG_REQUEST_ID_GENERATE_IF_NOT_FOUND = True
    DEVELOPERS_EMAILS = []

    FILE_STORAGE_CONFIG = {
        "s3": {
            "region": os.getenv("AWS_REGION", ""),
            "bucket": os.getenv("AWS_BUCKET", ""),
        }
    }

    RABBITMQ_URL = AwsSecretManager.get_rmq_url(tenant_id=TENANT_ID)

    FLASK_ADMIN_SWATCH = "flatly"
    SECRET_KEY = "_bXQcNMv<2J[omci$-J}ptSw>)z+Vw"

    SERVER_EMAIL = "Treebo Hotels <<EMAIL>>"
    GOOGLE_APP_NAME = "Reseller"
    INPUT_TAX_CREDIT_APPLICABILITY_DATE = "2021-01-01"

    AUTH = {
        "REDIRECT_URI": "https://reseller.treebo.be/callback",
        "AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
        "TOKEN_URI": "https://accounts.google.com/o/oauth2/token",
        "USER_INFO": "https://www.googleapis.com/userinfo/v2/me",
        "SCOPE": ["profile", "email"],
    }
    THRESHOLD_PDF_SIZE = os.environ.get("THRESHOLD_PDF_SIZE", 1000)
    MAX_RETRY_COUNT_FOR_PDF_GENERATION = os.environ.get(
        "MAX_RETRY_COUNT_FOR_PDF_GENERATION", 5
    )
    MAX_THREAD_FOR_PDF_GENERATION = os.environ.get("MAX_THREAD_FOR_PDF_GENERATION", 4)
    SIGNED_URL_EXPIRY_DURATION = os.environ.get("SIGNED_URL_EXPIRY_DURATION", 3600)
    RESELLER_INVOICE_PATH = os.environ.get("RESELLER_INVOICE_PATH", "reseller_invoices")
    POOLING_THRESHOLD = os.environ.get("POOLING_THRESHOLD", 10)
