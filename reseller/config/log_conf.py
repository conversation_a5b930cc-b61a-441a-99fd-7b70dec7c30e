import os


def configure_logging(app):
    import logging.config

    environment = os.environ.get("APP_ENV", "local")
    logging_conf = {
        "version": 1,
        "disable_existing_loggers": False,
        "filters": {
            "request_filter": {"()": "flask_log_request_id.filters.RequestIDLogFilter"}
        },
        "formatters": {
            "verbose": {
                "format": "[%(request_id)s] [%(asctime)s] %(levelname)s  [%(name)s:%(lineno)s] %(message)s"
            },
            "simple": {"format": "%(levelname)s %(message)s"},
            "logstash": {"()": "logstash_formatter.LogstashFormatterV1"},
        },
        "handlers": {
            "null": {
                "level": "INFO",
                "class": "logging.NullHandler",
            },
            "console": {
                "level": "INFO",
                "class": "logging.StreamHandler",
                "formatter": "verbose"
                if environment in ("local", "testing")
                else "logstash",
                "filters": ["request_filter"],
            },
        },
        "loggers": {
            "reseller": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
            "core": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
            "integrations": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
            "flask": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
        },
        "root": {
            "handlers": ["console"],
            "level": "INFO" if environment == "production" else "DEBUG",
        },
    }
    logging.config.dictConfig(logging_conf)
