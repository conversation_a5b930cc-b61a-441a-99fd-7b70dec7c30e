# pylint: disable=invalid-name
from reseller import Config


class LocalConfig(Config):
    DEBUG = True
    ENV = "local"
    MONGO_URI = "mongodb://127.0.0.1:27017/reseller"
    # Staging Details
    # MONGO_URI = "*************************************************************************************"

    SERVER_EMAIL = "Treebo Hotels <<EMAIL>>"
    FILE_STORAGE_CONFIG = {
        "s3": {
            "aws_access_key_id": "********************",
            "aws_secret_access_key": "M0K+T8UgOW70+rHM4fONdf20P44nReM/9Yj9u58b",
            "region": "ap-southeast-1",
            "bucket": "b2b-s-reseller",
        }
    }

    AUTH = {
        "REDIRECT_URI": "http://127.0.0.1:5000/callback",
        "AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
        "TOKEN_URI": "https://accounts.google.com/o/oauth2/token",
        "USER_INFO": "https://www.googleapis.com/userinfo/v2/me",
        "SCOPE": ["profile", "email"],
    }

    ALL_E_TECH_ENDPOINT_URL = "https://navtest.treebohotels.com"
    NAVISION_ERROR_REPORT_RECEIVER_LIST = ["<EMAIL>"]
    import os

    os.environ["THSC_ENVIRONMENT"] = "staging"
