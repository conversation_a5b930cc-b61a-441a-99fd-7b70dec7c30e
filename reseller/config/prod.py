from reseller import Config


class ProdConfig(Config):
    DEBUG = False
    ENV = "production"
    LOG_ROOT = "/ebs1/logs"
    DEVELOPERS_EMAILS = []  # pylint: disable=invalid-name
    ALL_E_TECH_ENDPOINT_URL = "https://navtest.treebohotels.com"
    NAVISION_ERROR_REPORT_RECEIVER_LIST = ["<EMAIL>"]
    AUTH = {
        "REDIRECT_URI": "https://reseller.treebo.com/callback",
        "AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
        "TOKEN_URI": "https://accounts.google.com/o/oauth2/token",
        "USER_INFO": "https://www.googleapis.com/userinfo/v2/me",
        "SCOPE": ["profile", "email"],
    }

    import os

    os.environ["THSC_ENVIRONMENT"] = "production"
    DUMMY_HOTEL_IDS = "9907195,1438960,1502702"
