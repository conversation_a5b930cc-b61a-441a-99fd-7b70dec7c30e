"""
dataclasses are good. But they dont support set in their as dict method. The main focus of asdict was for json.
In one way json does not support set. So we morph the set class to list during asdict call
"""

import dataclasses

original_as_dict_inner = (
    dataclasses._asdict_inner
)  # pylint: disable=invalid-name,protected-access


def as_dict_inner(obj, dict_factory):
    if isinstance(obj, (set, frozenset)):
        obj = list(obj)
    return original_as_dict_inner(obj, dict_factory)


dataclasses._asdict_inner = as_dict_inner  # pylint: disable=protected-access
