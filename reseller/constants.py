# pylint: disable=invalid-name
from enum import Enum

GOONJ_CONF = "config/goonj_conf.yaml"


class Timeouts:
    CONNECTION_TIMEOUT = 6  # pylint: disable=invalid-name
    RESPONSE_TIMEOUT = 18  # pylint: disable=invalid-name


class IngestionTypes:
    CREDIT_NOTE = "credit_note"
    HOTEL_INVOICE = "hotel_invoice"


class Subchannel:
    # sub channel types
    BTC = "Corporate (BTC)"
    DIRECT = "Corporate (Direct)"


class IssuedBy(Enum):
    TREEBO = "treebo"
    HOTEL = "hotel"


class IssuedTo(Enum):
    TREEBO = "treebo"
    CUSTOMER = "customer"


class InvoiceSource:
    MANUAL_RESYNC = "manual_resync"


class InvoiceType:
    CUSTOMER_CREDIT_NOTE = "customer_credit_note"
    CUSTOMER_INVOICE = "customer_invoice"
    HOTEL_CREDIT_NOTE = "hotel_credit_note"
    HOTEL_INVOICE = "hotel_invoice"


class BookingQueueStatus:
    PENDING = "pending"
    PROCESSED = "processed"
    FAILED = "failed"


class Modules:
    CRS = "crs"
    Catalog = "catalog"


class ResourceTypes:
    BOOKING = "booking"
    INVOICE = "invoice"
    CREDIT_NOTE = "credit_note"


INGESTION_DELAY_TO_ACHIEVE_MUTUAL_EXCLUSIVITY = 5
PIS_TENANT_ID = "tntpis"
