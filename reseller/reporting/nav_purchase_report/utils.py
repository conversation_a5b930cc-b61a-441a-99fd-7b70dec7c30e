import json
import os


def to_navision_room_type(room_type):
    navision_room_type_map = {
        "acacia": "Acacia(Solo)",
        "mahogany": "Mahogany(Premium)",
        "maple": "Maple(Deluxe)",
        "oak": "Oak(Standard)",
    }
    return navision_room_type_map.get(room_type.lower())


def to_ymd_str(date):
    return date.strftime("%Y-%m-%d")


def sanitize_string(string, length=False, only_alpha=False):
    if length:
        string = string[0:length]
    if only_alpha:
        string = "".join([char for char in string if char.isalpha()])
    return string


def get_state_code(state):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(f"{dir_path}/data/state_code_map.json") as file:
        state_code_map = json.load(file)
        for key, value in state_code_map.items():
            if state.replace(" ", "").lower() in key.replace(" ", "").lower():
                return value
