import json
import logging

from core.common.api.catalog_service_client import CatalogServiceClient
from object_registry import register_instance
from reseller import app
from reseller.infrastructure.service_registry import (
    ServiceEndPointNames,
    ServiceRegistryClient,
)
from reseller.reporting.nav_purchase_report.external_client.core.base_client import (
    BaseExternalClient,
)
from reseller.reporting.nav_purchase_report.serialisers.all_e_tech import (
    PurchaseInvoiceDataPushSchema,
)

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CatalogServiceClient])
class FinanceServiceClient(BaseExternalClient):
    def __init__(self, catalog_service_client):
        super().__init__(timeout=4000)
        self.catalog_service_client = catalog_service_client

    page_map = {
        "json_purchase": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/purchase-data/ingest-async",
        ),
    }

    def get_domain(self):
        return "http://localhost:9002"
        # return ServiceRegistryClient.get_service_url(
        #     ServiceEndPointNames.FINANCE_SERVICE_URL
        # )

    def push_purchase_invoices(
        self, hotel_invoice_report_aggregates, chunked_data_index
    ):
        json_key = "json_purchase"
        data = self.parse_data(hotel_invoice_report_aggregates)
        # self.push_to_finance_admin(
        #     chunked_data=data, json_key=json_key, chunked_data_index=chunked_data_index
        # )

    def parse_data(self, aggregates):
        parsed_data = PurchaseInvoiceDataPushSchema().dump(
            aggregates, many=True
        )
        print("parsed_data", parsed_data.data)
        return parsed_data.data

    def push_to_finance_admin(self, chunked_data, json_key, chunked_data_index):
        env = app.config.get("ENV")
        if "production" not in env and ".com" in self.get_domain():
            return True
        if not chunked_data:
            return True
        logger.info(
            "Data to be sent for %s to Finance admin: %s",
            json_key,
            json.dumps(chunked_data),
        )
        response = self.make_call(json_key, chunked_data)
        logger.info(
            "Finance admin response for %s with chunked data index %s is %s",
            json_key,
            chunked_data_index,
            response.json_response,
        )

        if not response.is_success():
            raise Exception(
                "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                    json_key, response.response_code, response.json_response
                )
            )

    def filter_out_test_data(self, aggregates):
        test_hotel_ids = self.catalog_service_client.get_all_test_property_ids()
        return [
            aggregate
            for aggregate in aggregates
            if not hasattr(aggregate, "hotel_code")
            or getattr(aggregate, "hotel_code") not in test_hotel_ids
        ]
