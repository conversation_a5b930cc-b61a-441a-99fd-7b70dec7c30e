import json
import logging

from object_registry import register_instance
from reseller import app
from reseller.reporting.nav_purchase_report.external_client.core.base_client import (
    BaseExternalClient,
)
from reseller.reporting.nav_purchase_report.external_client.error import (
    NavisionDuplicateException,
)
from reseller.reporting.nav_purchase_report.serialisers.all_e_tech import (
    PurchaseInvoicePushRequestSchema,
)

logger = logging.getLogger(__name__)


@register_instance()
class AllETechClient(BaseExternalClient):
    ALLE_SUCCESS_MESSAGES = ["Requested", "Record Inserted", "Record Updated"]
    ALLE_DUPLICATE_MESSAGES = "Duplicate"

    page_map = {
        "push_to_navision": dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/TreeboAPI"
        )
    }

    page_name = "push_to_navision"

    def __init__(self):
        super().__init__()

    def get_domain(self):
        return app.config["ALL_E_TECH_ENDPOINT_URL"]

    def is_successful_alle_response(self, json_response):
        return bool(
            isinstance(json_response, dict)
            and json_response.get("MESSAGE") in self.ALLE_SUCCESS_MESSAGES
        )

    def is_duplicate_push_response(self, json_response):
        return bool(
            isinstance(json_response, dict)
            and json_response.get("MESSAGE") == self.ALLE_DUPLICATE_MESSAGES
        )

    def push_purchase_invoices(
        self, hotel_invoice_report_aggregates, chunked_data_index
    ):
        json_key = "json_purchase"
        data = self.parse_data(hotel_invoice_report_aggregates, json_key=json_key)
        self.push_to_all_e_tech(
            chunked_data=data, json_key=json_key, chunked_data_index=chunked_data_index
        )

    @staticmethod
    def parse_data(aggregates, json_key):
        # Filtering out test hotel data
        aggregates = [
            {json_key: aggregate}
            for aggregate in aggregates
            if getattr(aggregate, "hotel_code", None)
            not in (app.config.get("DUMMY_HOTEL_IDS") or [])
        ]
        parsed_data = PurchaseInvoicePushRequestSchema().dump(aggregates, many=True)
        return parsed_data.data

    def push_to_all_e_tech(self, chunked_data, json_key, chunked_data_index):
        env = app.config.get("ENV")
        if "production" not in env:
            return True
        if not chunked_data:
            return True
        # Pushing data to all e tech in chunks to avoid request entity too large response
        logger.info(
            "Data to be sent for %s to All e tech: %s",
            json_key,
            json.dumps(chunked_data),
        )
        response = self.make_call(self.page_name, chunked_data)
        logger.info(
            "All e tech response for %s with chunked data index %s is %s",
            json_key,
            chunked_data_index,
            response.json_response,
        )
        if response.is_success() and self.is_duplicate_push_response(
            response.json_response
        ):
            raise NavisionDuplicateException()
        if not response.is_success() or not self.is_successful_alle_response(
            response.json_response
        ):
            raise Exception(
                "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                    json_key, response.response_code, response.json_response
                )
            )
