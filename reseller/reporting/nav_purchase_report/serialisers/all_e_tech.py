from marshmallow import Schema, fields

from reseller.reporting.nav_purchase_report.constants import (
    GSTType,
    NatureOfSupply,
    Structure,
)


class DataKeyField(fields.Field):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        data_key = kwargs.get("data_key")
        if data_key is not None:
            if self.load_from is not None or self.dump_to is not None:
                raise RuntimeError(
                    "load_from and dump_to cannot be given when data_key is given"
                )
            self.load_from = data_key
            self.dump_to = data_key


class StringField(fields.String, DataKeyField):
    pass


class BaseInvoicePushSchema(Schema):
    entry_type = StringField(data_key="EntryType", required=True)
    order_date = StringField(data_key="OrderDate", required=True)
    posting_date = StringField(data_key="PostingDate", required=True)
    reference_number = StringField(data_key="BookingRefNo", required=False)
    state_code = StringField(data_key="State", required=True)
    structure = StringField(
        data_key="Structure", required=False, default=Structure.GST.value
    )
    nature_of_supply = StringField(
        data_key="NatureofSupply", required=False, default=NatureOfSupply.B2B.value
    )
    unit_price = StringField(data_key="UnitPrice", required=True)
    tax_percentage = StringField(data_key="GSTGroupCode", required=True)
    hotel_name = StringField(data_key="HotelName", required=True)
    check_in = StringField(data_key="Check-In", required=True)
    check_out = StringField(data_key="Check-Out", required=True)
    stay_days = StringField(data_key="TotalDays", required=True)
    room_type = StringField(data_key="RoomType", required=False)
    occupancy = StringField(data_key="NoofPax", required=True)
    guest_name = StringField(data_key="GuestName", required=True)
    uvid_date = StringField(data_key="UVIDDate", required=True)
    invoice_number = StringField(data_key="UVIDNo", required=True)
    total_invoice_amount = StringField(data_key="UVAmount", required=True)
    hotel_code = StringField(data_key="Hotelcode", required=False)
    unique_ref_id = StringField(data_key="TransactionRefId", required=True)
    source = StringField(data_key="Source", required=False)
    sub_source = StringField(data_key="SubSource", required=False)


class PurchaseInvoicePushSchema(BaseInvoicePushSchema):
    vendor_number = StringField(data_key="VendorNo", required=True)
    due_date = StringField(data_key="DueDate", required=False)
    gst_vendor_type = StringField(
        data_key="GSTVendorType", required=False, default=GSTType.REGISTERED.value
    )
    remark = StringField(data_key="Remark", required=False)
    hsn_code = StringField(data_key="HSNSACCode", required=True)
    original_invoice_number = StringField(data_key="OriginalInvNo", required=False)

    class Meta:
        ordered = True
        fields = (
            "entry_type",
            "vendor_number",
            "order_date",
            "posting_date",
            "due_date",
            "state_code",
            "structure",
            "nature_of_supply",
            "gst_vendor_type",
            "remark",
            "reference_number",
            "unit_price",
            "tax_percentage",
            "hsn_code",
            "hotel_name",
            "check_in",
            "check_out",
            "stay_days",
            "room_type",
            "occupancy",
            "guest_name",
            "uvid_date",
            "invoice_number",
            "total_invoice_amount",
            "hotel_code",
            "original_invoice_number",
            "unique_ref_id",
            "source",
            "sub_source",
        )


class PurchaseInvoiceDataPushSchema(BaseInvoicePushSchema):
    vendor_number = StringField(data_key="VendorNo", required=True)
    due_date = StringField(data_key="DueDate", required=False)
    source_created_on = StringField(data_key="SourceCreatedOn", required=False)
    gst_vendor_type = StringField(
        data_key="GSTVendorType", required=False, default=GSTType.REGISTERED.value
    )
    remark = StringField(data_key="Remark", required=False)
    hsn_code = StringField(data_key="HSNSACCode", required=True)
    original_invoice_number = StringField(data_key="OriginalInvNo", required=False)
    customer_invoice_number = StringField(
        data_key="CustomerInvoiceNumber", required=False
    )

    class Meta:
        ordered = True
        fields = (
            "entry_type",
            "vendor_number",
            "order_date",
            "posting_date",
            "due_date",
            "state_code",
            "structure",
            "nature_of_supply",
            "gst_vendor_type",
            "remark",
            "reference_number",
            "unit_price",
            "tax_percentage",
            "hsn_code",
            "hotel_name",
            "check_in",
            "check_out",
            "stay_days",
            "room_type",
            "occupancy",
            "guest_name",
            "uvid_date",
            "invoice_number",
            "total_invoice_amount",
            "hotel_code",
            "original_invoice_number",
            "unique_ref_id",
            "source",
            "sub_source",
            "source_created_on",
            "customer_invoice_number",
        )


class PurchaseInvoicePushRequestSchema(Schema):
    json_purchase = fields.Nested(PurchaseInvoicePushSchema, required=True)

    class Meta:
        ordered = True
