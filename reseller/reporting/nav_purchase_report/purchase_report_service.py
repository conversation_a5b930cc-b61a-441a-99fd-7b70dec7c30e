from typing import List

from imapclient.util import chunk

from core.common.constants import IssuedToTypes
from object_registry import register_instance
from reseller.reporting.nav_purchase_report.core.hotel_invoice_report_aggregate import (
    HotelInvoiceReportAggregate,
)
from reseller.reporting.nav_purchase_report.core.hotel_invoice_report_generator import (
    HotelInvoiceReportGenerator,
)
from reseller.reporting.nav_purchase_report.external_client.finance_service_client import (
    FinanceServiceClient,
)
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)


@register_instance(dependencies=[FinanceServiceClient])
class PurchaseInvoiceDataPushService:

    CHUNK_SIZE = 1000000

    def __init__(self, finance_service_client: FinanceServiceClient):
        self.finance_service_client = finance_service_client

    def push_hotel_invoices_for_date(self, date):
        hotel_invoice_reports = get_hotel_invoice_reports_repository().get_issued_to_reseller_reports_by_status(
            date
        )
        # print("hotel_invoice_reports", hotel_invoice_reports)
        res = []
        for report in hotel_invoice_reports:
            res.append(report.__dict__)
        print(res)
        # self._push_hotel_invoice_reports(hotel_invoice_reports)

    def _push_hotel_invoice_reports(self, hotel_invoice_report_data):

        hotel_invoice_report_aggregates: List[
            HotelInvoiceReportAggregate
        ] = HotelInvoiceReportGenerator(
            hotel_invoice_report_data=hotel_invoice_report_data
        ).generate()

        self._push_data(hotel_invoice_report_aggregates)

    def _push_data(self, valid_hotel_invoice_report_aggregates):
        for chunked_data_index, chunked_data in enumerate(
            chunk(valid_hotel_invoice_report_aggregates, self.CHUNK_SIZE)
        ):
            self.finance_service_client.push_purchase_invoices(
                hotel_invoice_report_aggregates=chunked_data,
                chunked_data_index=chunked_data_index,
            )

    def push_hotel_invoices(self, invoice_numbers=None, unique_ref_id=None):
        if not (unique_ref_id or invoice_numbers):
            raise Exception("invalid parameter")
        queries = []
        if invoice_numbers:
            queries.append({"invoice_number": {"$in": invoice_numbers}})
        elif unique_ref_id:
            queries.append({"unique_ref_id": unique_ref_id})
        queries.append({"issued_to_type": IssuedToTypes.RESELLER})
        query = {"$and": queries}
        hotel_invoice_reports = get_hotel_invoice_reports_repository().find(**query)
        self._push_hotel_invoice_reports(hotel_invoice_reports)
