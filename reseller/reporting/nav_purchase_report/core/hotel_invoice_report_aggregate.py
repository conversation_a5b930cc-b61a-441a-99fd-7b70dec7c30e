from ths_common.constants.booking_constants import BookingChannels
from treebo_commons.utils import dateutils

from core.hotel_invoice.data_classes.invoice_report import HotelInvoiceReport
from reseller.reporting.nav_purchase_report.constants import (
    InvoiceEntryType,
    PurchaseInvoiceReportCategory,
    PurchaseInvoiceTypes,
)
from reseller.reporting.nav_purchase_report.utils import (
    get_state_code,
    sanitize_string,
    to_navision_room_type,
    to_ymd_str,
)


class HotelInvoiceReportAggregate:
    def __init__(self, hotel_invoice_report):
        self.hotel_invoice_report: HotelInvoiceReport = hotel_invoice_report

    @property
    def entry_type(self):
        if self.hotel_invoice_report.entity_type.lower() == "invoice":
            return InvoiceEntryType.ORDER.value
        return InvoiceEntryType.CREDIT.value

    @property
    def order_date(self):
        return to_ymd_str(
            dateutils.ymd_str_to_date(self.hotel_invoice_report.invoice_date)
        )

    @property
    def posting_date(self):
        # Making this equal to order date for now, as we ingest some invoices later to the invoice date on reseller.
        # Ideally this should be navision_date_context.posting_date
        return self.order_date

    @property
    def due_date(self):
        return self.order_date

    @property
    def state_code(self):
        return get_state_code(self.hotel_invoice_report.billed_to_state)

    @property
    def source(self):
        return self.hotel_invoice_report.source

    @property
    def sub_source(self):
        return self.hotel_invoice_report.sub_source

    @property
    def remark(self):
        return "Purch {0} {1}/{2}".format(
            "Inv" if self.entry_type == InvoiceEntryType.ORDER.value else "CN",
            dateutils.ymd_str_to_date(self.hotel_invoice_report.invoice_date).strftime(
                "%b %y"
            ),
            self.source,
        )

    @property
    def reference_number(self):
        return self.hotel_invoice_report.reference_number

    @property
    def unit_price(self):
        return self.hotel_invoice_report.pre_tax_price

    @property
    def tax_percentage(self):
        return int(self.get_tax_percentage())

    @property
    def hsn_code(self):
        return self.hotel_invoice_report.sac_code

    @property
    def hotel_name(self):
        if len(self.hotel_invoice_report.hotel_trade_name) > 30:
            hotel_name = [
                word
                for word in self.hotel_invoice_report.hotel_trade_name.split(" ")
                if word not in ["Treebo", "Trip", "Trend", "Tryst"]
            ]
            return " ".join(hotel_name)
        return self.hotel_invoice_report.hotel_trade_name

    @property
    def check_in(self):
        return to_ymd_str(dateutils.ymd_str_to_date(self.hotel_invoice_report.checkin))

    @property
    def check_out(self):
        return to_ymd_str(dateutils.ymd_str_to_date(self.hotel_invoice_report.checkout))

    @property
    def stay_days(self):
        return self.hotel_invoice_report.days

    @property
    def room_type(self):
        return to_navision_room_type(self.hotel_invoice_report.room_type)

    @property
    def occupancy(self):
        return self.hotel_invoice_report.occupancy

    @property
    def guest_name(self):
        return sanitize_string(self.hotel_invoice_report.booking_owner_name, 50)

    @property
    def uvid_date(self):
        return self.order_date

    @property
    def invoice_number(self):
        return self.hotel_invoice_report.invoice_number

    @property
    def total_invoice_amount(self):
        return self.hotel_invoice_report.total_invoice_amount

    @property
    def hotel_code(self):
        return self.hotel_invoice_report.cs_hotel_id

    @property
    def original_invoice_number(self):
        return self.hotel_invoice_report.original_invoice_number

    def get_tax_percentage(self):
        return (
            float(self.cgst_percent if self.cgst_percent else "0")
            + float(self.sgst_percent if self.sgst_percent else "0")
            + float(self.igst_percent if self.igst_percent else "0")
            + float(self.flood_cess_percent if self.flood_cess_percent else "0")
        )

    @property
    def cgst_percent(self):
        return self.hotel_invoice_report.cgst_percent

    @property
    def sgst_percent(self):
        return self.hotel_invoice_report.sgst_percent

    @property
    def igst_percent(self):
        return self.hotel_invoice_report.igst_percent

    @property
    def flood_cess_percent(self):
        return self.hotel_invoice_report.flood_cess_percent

    @property
    def unique_ref_id(self):
        return self.hotel_invoice_report.unique_ref_id

    @property
    def source_created_on(self):
        return self.hotel_invoice_report.created_on

    @property
    def customer_invoice_number(self):
        return self.hotel_invoice_report.customer_invoice_number

    @property
    def purchase_type(self):
        return PurchaseInvoiceTypes.RESELLER_INVOICE.value

    @property
    def report_category(self):
        return PurchaseInvoiceReportCategory.RESELLER_PURCHASE_INVOICE_REPORT.value
