from typing import List

from reseller.reporting.nav_purchase_report.core.hotel_invoice_report_aggregate import (
    HotelInvoiceReportAggregate,
)


class HotelInvoiceReportGenerator:
    def __init__(self, hotel_invoice_report_data):
        self.hotel_invoice_report_data = hotel_invoice_report_data

    def generate(self) -> List[HotelInvoiceReportAggregate]:
        hotel_invoice_report_aggregates = [
            HotelInvoiceReportAggregate(hotel_invoice_report_row)
            for hotel_invoice_report_row in self.hotel_invoice_report_data
        ]
        return hotel_invoice_report_aggregates
