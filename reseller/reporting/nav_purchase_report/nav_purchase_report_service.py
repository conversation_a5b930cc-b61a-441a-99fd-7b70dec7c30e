import os
from datetime import datetime
from typing import List

from imapclient.util import chunk

from core.common.constants import IssuedToTypes, PurchaseInvoiceReportStatus
from core.common.utils.files import write_to_csv
from core.notification.handlers.navision import (
    send_navision_error_report,
    send_slack_notification,
)
from object_registry import register_instance
from reseller.reporting.nav_purchase_report.core.hotel_invoice_report_aggregate import (
    HotelInvoiceReportAggregate,
)
from reseller.reporting.nav_purchase_report.core.hotel_invoice_report_generator import (
    HotelInvoiceReportGenerator,
)
from reseller.reporting.nav_purchase_report.external_client.all_e_tech_client import (
    AllETechClient,
)
from reseller.reporting.nav_purchase_report.external_client.error import (
    NavisionDuplicateException,
)
from reseller.reporting.nav_purchase_report.serialisers.all_e_tech import (
    BaseInvoicePushSchema,
    PurchaseInvoicePushSchema,
)
from reseller.repository.hotel_invoice_report import (
    get_hotel_invoice_reports_repository,
)


@register_instance(dependencies=[AllETechClient])
class NavisionReportingService:

    CHUNK_SIZE = 1

    def __init__(self, all_e_tech_client: AllETechClient):
        self.all_e_tech_client = all_e_tech_client

    def push_hotel_invoices_for_date(
        self, date, status=PurchaseInvoiceReportStatus.PENDING
    ):
        hotel_invoice_reports = get_hotel_invoice_reports_repository().get_issued_to_reseller_reports_by_status(
            date, status
        )
        self._push_hotel_invoice_reports(hotel_invoice_reports)

    def _push_hotel_invoice_reports(self, hotel_invoice_report_data, date=None):

        hotel_invoice_report_aggregates: List[
            HotelInvoiceReportAggregate
        ] = HotelInvoiceReportGenerator(
            hotel_invoice_report_data=hotel_invoice_report_data
        ).generate()

        # TODO: This is just a temporary fix for the invoice reports allowance,
        #  we are sending these invoice numbers to the navision_error_report email. These can be handled manually.
        valid_hotel_invoice_report_aggregates = (
            self._filter_out_invoices_having_allowance(
                date, hotel_invoice_report_aggregates
            )
        )

        self._push_data(valid_hotel_invoice_report_aggregates)

    def _filter_out_invoices_having_allowance(
        self, date, hotel_invoice_report_aggregates
    ):
        valid_tax_percentages = [0, 5, 12, 18]
        valid_hotel_invoice_report_aggregates, invoice_report_with_allowance = [], []
        for hotel_invoice_report_aggregate in hotel_invoice_report_aggregates:
            if hotel_invoice_report_aggregate.tax_percentage in valid_tax_percentages:
                valid_hotel_invoice_report_aggregates.append(
                    hotel_invoice_report_aggregate
                )
            else:
                invoice_report_with_allowance.append(hotel_invoice_report_aggregate)
        if invoice_report_with_allowance:
            invoice_numbers = ", ".join(
                {
                    invoices_with_allowance.invoice_number
                    for invoices_with_allowance in invoice_report_with_allowance
                }
            )
            msg = "Given invoices failed to push: {0} due to invalid tax percentage".format(
                invoice_numbers
            )
            send_navision_error_report(msg, date)
            self._update_purchase_invoice_report_status(
                invoice_report_with_allowance,
                PurchaseInvoiceReportStatus.MANUAL_ACTION_NEEDED,
            )
        return valid_hotel_invoice_report_aggregates

    def _push_data(self, valid_hotel_invoice_report_aggregates):
        # filter out test hotels
        valid_hotel_invoice_report_aggregates = [
            agg
            for agg in valid_hotel_invoice_report_aggregates
            if agg.hotel_code != "1502702"
        ]
        for chunked_data_index, chunked_data in enumerate(
            chunk(valid_hotel_invoice_report_aggregates, self.CHUNK_SIZE)
        ):
            try:
                self.all_e_tech_client.push_purchase_invoices(
                    hotel_invoice_report_aggregates=chunked_data,
                    chunked_data_index=chunked_data_index,
                )
                self._update_purchase_invoice_report_status(
                    chunked_data, PurchaseInvoiceReportStatus.GENERATED
                )
            except NavisionDuplicateException:
                # Duplicate error means record is already there in navision.
                # This will happen only if there is sync issue in the status of reseller and Nav.
                # For safe hand we keep this: eg NAv give us failure response but internally they inserted the record.
                # on subsequent push they give duplicate,so we assume duplicate response as "data push already happened"
                self._update_purchase_invoice_report_status(
                    chunked_data, PurchaseInvoiceReportStatus.GENERATED
                )
            except Exception as e:
                self._update_purchase_invoice_report_status(
                    chunked_data, PurchaseInvoiceReportStatus.FAILED
                )
                raise

    @staticmethod
    def _update_purchase_invoice_report_status(
        invoice_reports: List[HotelInvoiceReportAggregate], status
    ):
        try:
            query_set = []
            data_set = []
            for report in invoice_reports:
                data_set.append(dict(purchase_invoice_report_status=status))
                query_set.append({"unique_ref_id": report.unique_ref_id})
            get_hotel_invoice_reports_repository().bulk_update(
                queries=query_set, data_list=data_set
            )
        except Exception as e:
            unique_ref_ids = ",".join([inv.unique_ref_id for inv in invoice_reports])
            send_slack_notification(
                "Nav push: Failed to update status {0} for {1} : reason {2}".format(
                    status, unique_ref_ids, str(e)
                )
            )

    def push_hotel_invoices(
        self,
        invoice_numbers=None,
        unique_ref_id=None,
        status=PurchaseInvoiceReportStatus.PENDING,
    ):
        if not (unique_ref_id or invoice_numbers):
            raise Exception("invalid parameter")
        queries = []
        if invoice_numbers:
            queries.append({"invoice_number": {"$in": invoice_numbers}})
        elif unique_ref_id:
            queries.append({"unique_ref_id": unique_ref_id})
        queries.extend(
            [
                {"issued_to_type": IssuedToTypes.RESELLER},
                {"purchase_invoice_report_status": status},
            ]
        )
        query = {"$and": queries}
        hotel_invoice_reports = get_hotel_invoice_reports_repository().find(**query)
        self._push_hotel_invoice_reports(hotel_invoice_reports)

    def export_data_in_csv(self, params):
        if params.get("invoice_numbers"):
            inv_numbers = params.get("invoice_numbers").split(",")
            query = {
                "invoice_number": {"$in": inv_numbers},
                "issued_to_type": IssuedToTypes.RESELLER,
            }
        elif params.get("created_from") and params.get("created_to"):
            from_date, to_date = params.get("created_from"), params.get("created_to")
            query = {
                "created_on": {"$gte": from_date, "$lte": to_date},
                "issued_to_type": IssuedToTypes.RESELLER,
            }
        elif params.get("invoice_from") and params.get("invoice_to"):
            from_date, to_date = params.get("invoice_from"), params.get("invoice_to")
            query = {
                "invoice_date": {"$gte": from_date, "$lte": to_date},
                "issued_to_type": IssuedToTypes.RESELLER,
            }
        else:
            raise Exception(
                "pass (created_from and created_to) or (invoice_from and invoice_to) or invoice_numbers"
            )
        reports = get_hotel_invoice_reports_repository().find(**query)
        hotel_invoice_report_aggregates: List[
            HotelInvoiceReportAggregate
        ] = HotelInvoiceReportGenerator(hotel_invoice_report_data=reports).generate()
        valid_tax_percentages = [0, 5, 12, 18]
        hotel_invoice_report_aggregates = [
            agg
            for agg in hotel_invoice_report_aggregates
            if agg.tax_percentage in valid_tax_percentages
        ]
        hotel_invoice_report_aggregates = [
            agg
            for agg in hotel_invoice_report_aggregates
            if agg.hotel_code != "1502702"
        ]
        data = (
            PurchaseInvoicePushSchema()
            .dump(hotel_invoice_report_aggregates, many=True)
            .data
        )
        return self._write_to_csv(data) if data else None

    @staticmethod
    def _write_to_csv(data_list):
        current_time = datetime.now().strftime("%Y-%m-%d-%H:%M:%S")
        file_name = f"/nav_file_purchase_{current_time}.csv"
        file_path = os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name
        headers = [
            "EntryType",
            "OrderDate",
            "PostingDate",
            "BookingRefNo",
            "State",
            "Structure",
            "NatureofSupply",
            "UnitPrice",
            "GSTGroupCode",
            "HotelName",
            "Check-In",
            "Check-Out",
            "TotalDays",
            "RoomType",
            "NoofPax",
            "GuestName",
            "UVIDDate",
            "UVIDNo",
            "UVAmount",
            "Hotelcode",
            "TransactionRefId",
            "Source",
            "SubSource",
            "VendorNo",
            "DueDate",
            "GSTVendorType",
            "Remark",
            "HSNSACCode",
            "OriginalInvNo",
        ]
        rows_for_csv = [[data.get(key, "") for key in headers] for data in data_list]
        rows_for_csv = [headers] + rows_for_csv
        write_to_csv(file_path, rows_for_csv)
        return file_path

    @staticmethod
    def fetch_hotel_invoice_data(
        invoice_numbers=None,
        unique_ref_ids=None,
    ):
        queries = []
        if invoice_numbers:
            queries.append({"invoice_number": {"$in": invoice_numbers}})
        elif unique_ref_ids:
            queries.append({"unique_ref_id": {"$in": unique_ref_ids}})
        queries.extend(
            [
                {"issued_to_type": IssuedToTypes.RESELLER},
            ]
        )
        query = {"$and": queries}
        hotel_invoice_reports = get_hotel_invoice_reports_repository().find(**query)
        hotel_invoice_report_aggregates: List[
            HotelInvoiceReportAggregate
        ] = HotelInvoiceReportGenerator(
            hotel_invoice_report_data=hotel_invoice_reports
        ).generate()
        return hotel_invoice_report_aggregates
