from ths_common.constants.base_enum import BaseEnum


class InvoiceEntryType(BaseEnum):
    ORDER = "order"
    CREDIT = "credit memo"


class Structure(BaseEnum):
    GST = "GST"


class NatureOfSupply(BaseEnum):
    B2B = "B2B"
    B2C = "B2C"


class GSTType(BaseEnum):
    REGISTERED = "Registered"
    UNREGISTERED = "Unregistered"


class PurchaseInvoiceTypes(BaseEnum):
    RESELLER_INVOICE = "Purchase invoice of reseller"


class PurchaseInvoiceReportCategory(BaseEnum):
    RESELLER_PURCHASE_INVOICE_REPORT = "reseller_purchase_invoice_report"
