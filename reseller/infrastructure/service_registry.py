import enum
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry


class ServiceEndPointNames(enum.Enum):
    CATALOG_SERVICE_URL = "catalog_service_url"
    GROWTH_SERVICE_URL = "realisation_service_url"
    NOTIFICATION_SERVICE_URL = "notification_service_url"
    PROWL_SERVICE_URL = "prowl_service_url"
    MINT_SERVICE_URL = "mint_service_url"
    TAX_SERVICE_URL = "tax_service_url"
    CRS_SERVICE_URL = "crs_service_url"
    FINANCE_SERVICE_URL = "finance_portal_service_url"
    CLEAR_TAX_SERVICE_URL = "clear_tax_service_url"


class ServiceRegistryClient:
    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_service_url(cls, endpoint_name: ServiceEndPointNames) -> str:
        return cls.ALL_API_ENDPOINTS.get(
            endpoint_name.value, os.environ.get(endpoint_name.value)
        )
