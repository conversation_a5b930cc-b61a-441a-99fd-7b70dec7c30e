from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class CRSConfig(object):
    """
    CRS Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "crs-events"
        self.exchange_type = "topic"
        self.queue_name = "reseller_crs_listener_queue"
        self.routing_keys = ["booking.#"]
        self.exclusive = False


class CatalogConfig(object):
    """
    CRS Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "reseller_cs_exchange"
        self.exchange_type = "direct"
        self.queue_name = "reseller_hotel_sync"
        self.routing_keys = ["com.cs.property"]
        self.exclusive = False


class IngestionEventPublisherConfig(object):
    """
    Integration ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "reseller-exchange"
        self.exchange_type = "topic"
        self.queue_name = "reseller_ingestion_queue"
        self.routing_keys = [
            "ingestion.#",
        ]
        self.exclusive = False


class IngestionEventConsumerConfig(object):
    """
    Integration ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "reseller-exchange"
        self.exchange_type = "topic"
        self.queue_name = "reseller_ingestion_queue"
        self.routing_keys = [
            "ingestion.#",
        ]
        self.exclusive = False


class ResellerReportingConfig(object):
    """
    CRS Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "reseller-exchange"
        self.exchange_type = "topic"
        self.queue_name = "reseller-reporting-queue"
        self.routing_keys = ["reseller-reporting.#"]
        self.exclusive = False
