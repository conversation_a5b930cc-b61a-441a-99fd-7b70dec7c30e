class MaxRetriesExceeded(Exception):
    def __init__(self, max_retires):
        super(MaxRetriesExceeded, self).__init__(
            "Maximum retries: {m} exceeded".format(m=max_retires)
        )


class RejectToDeadLetterQueue(Exception):
    pass


class SkipAcknowledgement(Exception):
    """
    Skips acking of message so the message will dangle in the queue.
    Generally used in dead-letter consumers on messages which dead letter consumer doesn't know what to do.
    """

    def __init__(self, original_exc):
        self.original_exc = original_exc
        super(SkipAcknowledgement, self).__init__("SkipAcknowledgement")
