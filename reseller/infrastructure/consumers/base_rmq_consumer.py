import json
import logging
import socket
import uuid

from amqp import PreconditionFailed
from kombu import Connection, Exchange, Queue
from kombu.entity import PERSISTENT_DELIVERY_MODE
from kombu.mixins import ConsumerProducerMixin

from workers.exception import (
    MaxRetriesExceeded,
    RejectToDeadLetterQueue,
    SkipAcknowledgement,
)

logger = logging.getLogger(__name__)


class BaseRMQConsumer(ConsumerProducerMixin):
    default_exchange_name = "reseller-exchange"
    exchange_name = None
    declare_exchange_if_not_exists = False

    priority_queue = False
    priority_level = 10

    enable_delayed_retries = False
    delay_queue_name = None
    delay_in_seconds = 900
    max_retries = 3
    override_existing_queue = False  # be careful of the after effects while using this

    dead_letter_queue_name = None
    queue_name = None
    queue_routing_key = "#"
    default_dead_letter_queue_name = "dead-letter-queue"

    prefetch_count = 1

    def __init__(self, config):
        self.config = config
        try:
            self.connection = Connection(
                config.rabbitmq_url, transport_options={"confirm_publish": True}
            )
        except Exception as e:
            logger.exception("Error connecting to rabbitmq: %s", config.rabbitmq_url)
        if not self.config.queue_name:
            raise RuntimeError("Queue name is required")
        self.queue()

    def process_message(self, body, message):
        raise NotImplementedError(
            "'{n}' needs to implement process_message(...)".format(
                n=self.__class__.__name__
            )
        )

    def queue(self, **kwargs):
        options = {"routing_key": self.config.routing_keys[0], "durable": True}
        if self.priority_queue:
            options["max_priority"] = self.priority_level
        if kwargs:
            options.update(kwargs)
        return Queue(self.config.queue_name, exchange=self.exchange(), **options)

    def exchange(self, **kwargs):
        name = (
            self.config.exchange_name
            if self.config.exchange_name
            else self.default_exchange_name
        )
        options = {"passive": True}
        if self.declare_exchange_if_not_exists:
            exchange_type = (
                self.config.exchange_type if self.config.exchange_type else "topic"
            )
            options = {"type": exchange_type, "passive": False, "durable": True}
        if kwargs:
            options.update(kwargs)
        return Exchange(name, **options)

    def dead_letter_queue(self, **kwargs):
        name = (
            self.dead_letter_queue_name
            if self.dead_letter_queue_name
            else self.default_dead_letter_queue_name
        )
        options = {"routing_key": "dead.#", "durable": True}
        if kwargs:
            options.update(kwargs)
        return Queue(name, exchange=self.exchange(), **options)

    def delay_queue(self, **kwargs):
        name = (
            self.delay_queue_name
            if self.delay_queue_name
            else "{q}-delay".format(q=self.config.queue_name)
        )
        exchange = self.exchange()
        options = {
            "routing_key": "delay.{rk}".format(rk=self.config.routing_keys[0]),
            "durable": True,
            "queue_arguments": {
                "x-dead-letter-exchange": exchange.name,
                "x-dead-letter-routing-key": self.config.routing_keys[0],
            },
        }
        if kwargs:
            options.update(kwargs)
        return Queue(name, exchange=exchange, **options)

    def get_consumers(self, consumer, channel):  # pylint: disable=arguments-differ
        self._maybe_delete_queue(queue=self.queue())
        return [
            consumer(
                queues=[self.queue()],
                callbacks=[self._on_task],
                prefetch_count=self.prefetch_count,
                tag_prefix="({h})-".format(h=socket.gethostname()),
            )
        ]

    def publish(self, message, reject=False):
        routing_key_prefix = "dead" if reject else "delay"

        if routing_key_prefix == "dead":
            queue = self.dead_letter_queue()
            delay = None
            message.headers.pop(
                "retries", None
            )  # reset retry header if pushing to dead letter queue
            if queue.name == self.config.queue_name:
                # ideally to check if dead-letter queue is its own dead letter queue
                # without this check will cause deadlock.
                raise SkipAcknowledgement(original_exc=None)

        elif routing_key_prefix == "delay":
            if not self.enable_delayed_retries:
                # should never happen. Means somebody is messing with this.
                raise RuntimeError(
                    "Routing to delay queue cannot happen if enable_delayed_retries is False"
                )

            queue = self.delay_queue()
            delay = self.delay_in_seconds
            self._maybe_delete_queue(
                queue
            )  # instead rely on changing delay queue name and set an expiration for it.

        else:
            raise RuntimeError(
                "Unknown routing key prefix: {rk}".format(rk=routing_key_prefix)
            )

        data = dict(
            body=message.body,
            headers=message.headers,
            exchange=queue.exchange,
            declare=[queue],
            routing_key="{p}.{k}".format(
                p=routing_key_prefix, k=message.delivery_info["routing_key"]
            ),
            expiration=delay,
            retry=True,
            delivery_mode=PERSISTENT_DELIVERY_MODE,
        )

        self.producer.publish(**data)

    def notify_permanent_failure(self, body, error_message):
        pass

    def _on_task(self, body, message):
        _mid = self._maybe_set_unique_id(message)
        logger.info("{m} with body: {b} received".format(m=_mid, b=body))
        try:
            try:
                self._can_retry(message=message)
                self.process_message(body, message)
            except (MaxRetriesExceeded, RejectToDeadLetterQueue) as e:
                error_message = "Exception in {m} body: {b} due to {e}".format(
                    m=repr(message), b=body, e=repr(e)
                )
                self.notify_permanent_failure(body, error_message)
                logger.info("{m} failed due to {e}".format(m=_mid, e=repr(e)))
                self._add_to_error_trail(message=message, exc=e)
                self.publish(message=message, reject=True)
            except SkipAcknowledgement:
                raise
            except Exception as e:
                logger.exception("{m} failed due to {e}".format(m=_mid, e=repr(e)))
                self._add_to_error_trail(message=message, exc=e)
                self.publish(message=message, reject=not self.enable_delayed_retries)
            message.ack()
            logger.info("{m} body: {b} acked".format(m=_mid, b=body))
        except SkipAcknowledgement as e:
            # Don't raise. passing of this will let the message dangle in the queue
            logger.exception(
                "SkipAck Exception in {m} body: {b} due to {e}".format(
                    m=repr(message), b=body, e=repr(e.original_exc)
                )
            )
        except Exception as e:
            error_message = "Exception in {m} body: {b} due to {e}".format(
                m=repr(message), b=body, e=repr(e)
            )
            logger.exception(error_message)
            # Currently raising since alerting system is supposed to highlight this. Without raise we will just keep
            # consuming. Exception at this level deserved to be bought to the notice of devs/ops.
            self.notify_permanent_failure(body, error_message)
            message.ack()

    def _can_retry(self, message):
        try:
            message.headers["retries"] += 1
        except KeyError:
            message.headers["retries"] = 1
        if message.headers["retries"] > self.max_retries:
            raise MaxRetriesExceeded(max_retires=self.max_retries)

    def _add_to_error_trail(self, message, exc):
        msg = "Try {t}: due to {e}".format(e=str(exc), t=message.headers["retries"])
        try:
            message.headers["error_trail"].append(msg)
        except KeyError:
            message.headers["error_trail"] = [msg]

    def _maybe_delete_queue(self, queue):
        """Delete the queue if queue declaration parameters have changed."""
        try:
            queue.maybe_bind(channel=self.connection.channel())
            queue.declare()
        except PreconditionFailed as e:
            if not self.override_existing_queue:
                logger.exception(e)
                raise RuntimeError(
                    "Precondition Failed usually means the queue is already declared with few params "
                    "which have changed. Eg: routing_key, priority_level. "
                    "Set override_existing_queue=True to override existing queue declaration. "
                    "Original Error: {e}".format(e=e.message)
                )

            logger.warning(
                "Deleting queue {q} due to {e}".format(q=repr(queue), e=repr(e))
            )
            queue.delete(if_empty=True)

    def _maybe_set_unique_id(self, message):
        try:
            unique_message_id = message.properties.get("uuid", "")
            if unique_message_id:
                logger.info(
                    "Got uuid {u} in message: {m}".format(
                        u=unique_message_id, m=repr(message)
                    )
                )
                return unique_message_id
            unique_message_id = str(uuid.uuid4())
            message.properties["uuid"] = unique_message_id
        except Exception as e:
            logger.exception(e)
            unique_message_id = str(uuid.uuid4())
        logger.info(
            "Set uuid {u} to message: {m}".format(u=unique_message_id, m=repr(message))
        )
        return unique_message_id

    def start_consumer(self):
        try:
            self.run()
        except Exception as e:
            logging.critical(e)

    @staticmethod
    def parse_body(body):
        if isinstance(body, bytes):
            body = body.decode()
        if isinstance(body, str):
            body = json.loads(body)
        return body
